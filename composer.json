{"name": "woocommerce/woocommerce-shipping-multiple-addresses", "description": "Allow customers to ship orders with multiple products or quantities to separate addresses instead of forcing them to place multiple orders for different delivery addresses.", "homepage": "https://woocommerce.com/products/shipping-multiple-addresses/", "type": "wordpress-plugin", "license": "GPL-2.0+", "archive": {"exclude": ["!/assets", "!/languages", "!/assets/js/*.js", "!/assets/js/*.min.js", "/assets/css/*.scss", "bin", "tests", "node_modules", "/vendor", "Gruntfile.js", "README.md", "package.json", "package-lock.json", "composer.json", "composer.lock", "phpunit.xml.dist", ".*", "woocommerce-shipping-multiple-addresses.zip", "vendor", "pnpm-lock.yaml", "client", "webpack.config.js", "babel.config.js"]}, "require-dev": {"woocommerce/qit-cli": "*", "squizlabs/php_codesniffer": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "wp-coding-standards/wpcs": "*", "woocommerce/woocommerce-sniffs": "*"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && ./vendor/bin/qit run:security woocommerce-shipping-multiple-addresses --zip=woocommerce-shipping-multiple-addresses.zip"]}}