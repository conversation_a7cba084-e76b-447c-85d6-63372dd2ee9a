{"name": "woocommerce-shipping-multiple-addresses", "title": "WooCommerce Shipping Multiple Addresses", "version": "4.2.9", "homepage": "https://woocommerce.com/products/shipping-multiple-addresses/", "repository": {"type": "git", "url": "git://github.com/woocommerce/woocommerce-shipping-multiple-addresses.git"}, "devDependencies": {"clean-css-cli": "^4.3.0", "uglify-js": "^3.14.3", "@wordpress/scripts": "^27.4.0", "babel-loader": "^9.1.3", "babel-minify": "^0.5.2", "css-loader": "^6.8.1", "mini-css-extract-plugin": "^2.9.0", "sass": "^1.62.1", "sass-loader": "^13.3.1", "style-loader": "^3.3.3", "webpack": "^5.91.0", "node-wp-i18n": "~1.2.3"}, "config": {"use_pnpm": true, "translate": true, "use_gh_release_notes": true, "paths": {"js": "assets/js/*.js", "js_min": "assets/js/*.min.js", "css": "assets/css/*.css", "sass": "assets/sass", "cssfolder": "assets/css"}}, "scripts": {"build": "pnpm run build:prod && pnpm run archive", "build:dev": "composer install -o && pnpm run makepot && pnpm run uglify && pnpm run sass && webpack --env mode=development", "build:prod": "composer install --no-dev -o && pnpm run makepot && pnpm run uglify && pnpm run sass && webpack --env mode=production", "archive": "composer archive --file=$npm_package_name --format=zip", "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name", "preuglify": "rm -f $npm_package_config_paths_js_min", "uglify": "for f in $npm_package_config_paths_js; do file=${f%.js}; node_modules/.bin/uglifyjs $f -c -m > $file.min.js; done", "presass": "rm -f $npm_package_config_paths_css", "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed", "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch", "postsass": "for f in $npm_package_config_paths_css; do file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done", "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs", "preminify": "rm -f assets/js/*.min.js", "minify": "for f in assets/js/*.js; do file=${f%.js}; node_modules/.bin/minify $f --out-file $file.min.js; done", "sass:watch": "pnpm run sass --watch", "wp-start": "wp-scripts start"}, "engines": {"node": "^22.14.0", "pnpm": "^10.4.1"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}