.shipping_data {margin-top: 15px;}
.item-addresses-holder {display: block;}
.item-address-box {float: left; width: 200px; border-right: 1px solid #ccc; padding: 10px; position: relative;}
.item-address-box span.complete {
    background: transparent url(../images/success.png) top left no-repeat;
    width: 32px;
    height: 28px;
    display: block;
    position: absolute;
    top: 0;
    left: 5px;
}
div.item-addresses-holder div.item-address-box:last-child {border-right: none !important;}
.clear { clear: both; }

.wc-shipment {
    border-bottom: 1px solid #DFDFDF;
    padding-bottom: 20px;
    padding-top: 20px;
}

.wc-shipment:first-child {
    padding-top: 0;
}

.wc-shipment:last-child {
    border-bottom: none;
}

#poststuff .wc-shipment h3 {
    padding: 0;
}

.wc-shipment div.address {
    float: right;
}

.wc-shipment table {
    margin: 0px;
    width: 100%;
    border: 1px solid #DFDFDF;
    border-spacing: 0px;
    background-color: #F9F9F9;
}
.wc-shipment table tr {
    vertical-align: top;
}
.wc-shipment table th {
    padding: 5px 8px 8px;
    background-color: #F1F1F1;
}
.wc-shipment table th.left,
.wc-shipment table td.left {
    width: 38%;
    text-align: left;
}

.wc-shipment table td {
    padding: 5px;
}

ul.wcsm-config {
    font-size: .87em;
    list-style: none;
    font-weight: normal;
}

button#show_excluded_dates_calendar, button#hide_excluded_dates_calendar{
	display: block;
    line-height: 1;
}

.woocommerce table.ui-datepicker-calendar th {
	padding-right: 0 !important;
}
