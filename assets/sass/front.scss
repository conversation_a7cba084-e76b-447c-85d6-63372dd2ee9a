#address_form {
	#cart_addresses {
		/*float: right;
		width: 78%;
		margin-left: 10px;*/
	}

	#cart_items {
		ul {
			background: #f5f5f5;
			border: 1px solid #c4c4c4;
			font-size: 12px;
			padding: 10px;
			margin-bottom: 20px;
			margin-top: 5px;

			li {
				&.cart-item {
					position: relative;
					margin: 5px 0;
					padding: 5px;
					list-style: none;
					text-align: left;
					border-bottom: 1px dotted #ccc;
					cursor: move;
					background: none;
					overflow: auto;
	
					&:last-child {
						border-bottom: none;
					}
				}

				span.qty {
					display: inline-block;
					padding: 3px 9px;
					border-radius: 5px;
					-moz-border-radius: 10px;
					-webkit-border-radius: 10px;
					position: absolute;
					right: 10px;
					background: red;
					color: #fff;
					font-weight: bold;
				}
			}
		}
	}

	tbody {
		.row-template {
			display:none;
		}
	}

	tfoot {
		tr {
			td#availability-info {
				padding-left:0px;
				padding-right:0px;
				padding-bottom:0px;
			}

			&#row-add-input {
				background-color:#f0f0f0;

				&.hidden {
					display:none;
				}

				.input-text.qty {
					padding: 0px;
					background-color:#fff;
				}
			}
		}
	}

	h2 {
		a.button {
			font-size: 12px ;
			vertical-align: middle;
		}
	}

	.address_block {
		overflow: auto;
	}

	ul {
		&.items-column {
			background: #f5f5f5;
			border: 1px solid #c4c4c4;
			font-size: 12px;
			margin: 0;
			min-height: 40px;
	
			&.guest {
				/*float: left;
				width: 200px;*/
			}

			li {
				position: relative;
				margin: 0;
				padding: 5px 10px;
				list-style: none;
				text-align: left;
				border-bottom: 1px dotted #ccc;
				cursor: default;
				background: none;
				height: 100%;
				overflow: auto;

				&:last-child {
					border-bottom: none;
				}

				h3 {
					font-size: 14px;
				}

				img {
					&.remove {
						width: 64px;
						height: auto;
						float: left;
						margin-right: 5px;
					}	
				}

				span {
					&.qty {
						display: inline-block;
						padding: 3px 8px;
						margin-right: 5px;
						border-radius: 5px;
						-moz-border-radius: 10px;
						-webkit-border-radius: 10px;
						float: left;
						background: red;
						color: #fff;
						font-weight: bold;
						font-size: 10px;
					}	
				}

				a {
					&.remove{
						position: absolute;
						top: 0;
						right: 0;
					}
				}
			}
		}
	}

	li.placeholder {
		text-align: center !important;
		font-size: 18px;
		color: #999;
		padding: 30px;
	}

	.address-select {
		max-width:100%;
	}

	.address_block {
		border-bottom: 1px solid #CCC;
	}

	.availability-title {
		font-weight:700;
		font-size:1.3rem;
	}

	.availability-product {
		margin-bottom:8px;

		.product-name {
			font-weight:700;
		}
	}

	.usage-percentage {
		width:100%;
		height:32px;
		position:relative;
		background-color:#f8f8f8;

		.percentage-bar {
			background-color:#0f834d;
			display:block;
			position:absolute;
			top:0px;
			bottom:0px;
			left:0px;
			width:0%;
			max-width:100%;
			min-width:96px;
			text-align:left;
			overflow: hidden;
			font-weight:700;
			color:#fff;
			display:inline-block;
			font-size:13px;
			line-height:32px;
			box-sizing:border-box;
			padding-left:8px;
			padding-right:8px;
		}

		&.still-available .percentage-bar {
			background-color:#3d9cd2;
		}

		&.exceeded .percentage-bar {
			background-color:#e2401c;
		}
	}
}

body {
	a.h2-link {
		font-size: 15px;
	}

	li{
		&.cart-item {
			position: relative;
			margin: 5px 0;
			padding: 5px;
			list-style: none;
			text-align: left;
			border-bottom: 1px dotted #ccc;
			cursor: move;
			background: none;
			overflow: auto;

			&.ui-draggable-dragging {
				zoom: 1;
				filter: alpha(opacity=30);
				opacity: 0.3;
				z-index: 900;
			}

			div.quantity {
				display: none;
			}

			span.qty  {
				display: inline-block;
				padding: 3px 9px;
				border-radius: 5px;
				-moz-border-radius: 10px;
				-webkit-border-radius: 10px;
				position: absolute;
				right: 10px;
				background: red;
				color: #fff;
				font-weight: bold;
			}

			&-active {
				background-color: #E6E56C;
			}
		}
	}

	ul {
		&.items-column {
			padding: 0 !important;

			&.ui-state-hover {
				background-color: #E6E56C !important;
				color: #FFF;

				li {
					background-color: #E6E56C !important;
					color: #FFF;
				}
			}
		}
	}

	#addresses_container {
		.account-address {
			float: left;
			margin: 5px 10px;
			padding: 5px;
			width: 200px;
			border: 1px solid #999;
			background: #f5f5f5;
			min-height: 140px;
			overflow: hidden;
	
			a.edit {
				float: right;
			}
		}
	}

	#tb_addresses {
		overflow: auto;
	
		> .address_block {
			width: 150px;
			float: left;
			margin: 10px 2px;
			background: #F5F5F5;
			border-radius: 10px;
			padding: 5px;
			border: 1px solid #CCC;
			cursor: pointer;
	
			&:hover {
				background: #BCFFEC !important;
			}
		}
	}

	.ship_address {
		float: left;
		width: 45%;
		/*margin: 2%;*/
		margin: 2% 2% 2% 0;
		padding: 8px;
		background-color: #eee;
		border-radius: 5px;
		-moz-border-radius: 5px;
		-webkit-border-radius: 5px;
		border: 1px solid #ddd;
		-webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
		-moz-box-sizing: border-box;    /* Firefox, other Gecko */
		box-sizing: border-box;         /* Opera/IE 8+ */
		line-height: 1.5em;

		select {
			width: 100% !important;
		}
	}

	.no_shipping_address {
		float: left;
		width: 45%;
		/*margin: 2%;*/
		margin: 2% 2% 2% 0;
		padding: 8px;
		background-color: #eee;
		border-radius: 5px;
		-moz-border-radius: 5px;
		-webkit-border-radius: 5px;
		border: 1px solid #ddd;
		-webkit-box-sizing: border-box; /* Safari/Chrome, other WebKit */
		-moz-box-sizing: border-box;    /* Firefox, other Gecko */
		box-sizing: border-box;         /* Opera/IE 8+ */
		line-height: 1.5em;
	}

	tr.multi_shipping {
		div.ship_address {
			img.help_tip {
				width: 16px;
				height: 16px;
			}

			small.data {
				display: block;
				margin: 0 0 10px 10px;
			}
		}
	}

	.update-shipping-addresses {
		float: left;
	}
	
	.set-shipping-addresses {
		float: right;
	}

	.account-address-block {
		float: left;
		width: 200px;
		margin-bottom: 20px;
	}

	.address-block {
		float: left;
		width: 200px;
		min-height: 170px;
		margin: 10px 2px;
		position: relative;

		.buttons {
			text-align: center;
			position: absolute;
			bottom: 5px;
			width: 100%;

			.button {
				width: 48%;
			}
		}
	}

	.address-container {
		.clear {
			height: 1px;
			clear: both;
		}
	}

	ul.wcsm-config {
		font-size: .87em;
		list-style: none;
		font-weight: normal;
	}

	.woocommerce-edit-address {
		a.delete-address-button {
			float: right;
		}

		.col2-set.addresses {
			.col-1 {
				clear: both;
			}
		}
	}

	.wc-shipping-multiple-addresses {
		&.shop_table {
			&.cart {
				table-layout: fixed;

				& th, & td {
					padding:10px;
				}

				button {
					&.delete-item,
					&.split-item,
					&#wcms-add-row {
						padding:5px 10px;
						margin:0px 7px 7px 0px;
					}
				}
			}
		}
	}

	& .wcms-error-notice,
	& .wcms-error-address-empty {
		display:block;

		&.hidden {
			display:none;
		}
	}

	.address-form-note {
		padding: 20px;
		display: block;
		margin-top: 20px;
		background: #fbfbfb;
		color: #000;
		font-style: italic;
	}
}

@media (max-width: 768px) {
    .wc-shipping-multiple-addresses.shop_table.cart {
        font-size: 12px;
		display:flex;
		flex-direction:column;

		thead {
			display:block;
			order:1;

			tr {
				display:block;

				th {
					display:block;
					float:left;

					&.product-name {
						width:30%;
					}

					&.product-quantity {
						width:20%;
					}

					&.shipping-address {
						width:50%;
					}

					&.remove-item {
						display:none;
					}
				}
			}
		}

		tbody {
			display:block;
			order:2;

			tr {
				display:block;
				margin-bottom:10px;
				
				&:after {
					content:"";
					display:block;
					clear:both;
				}

				td {
					display:block;
					float:left;

					&.product-name {
						width:30%;

						.product-text {
							font-size:14px;
							height:2em;
						}
					}

					&.product-quantity {
						width:20%;
						font-size:14px;

						input {
							padding:0px;

							&.input-quantity {
								line-height:2em;
								height:2em;
							}
						}
					}

					&.shipping-address {
						width:50%;

						.input-address {
							font-size:14px;
							line-height:2em;
							height:2em;
						}
					}

					&.row-action {
						clear:both;
						width:100%;
						display:flex;
						flex-direction:row;

						button {
							width:50%;
							margin:0px 5px;
						}
					}
				}
			}
		}

		tfoot {
			display:block;
			order:3;

			tr {
				display:block;

				td {
					display:block;
					float:left;

					&.add-product {
						width:30%;
					}

					&.add-quantity {
						width:20%;
					}

					&.add-address {
						width:50%;
					}

					&.add-button {
						clear:both;
						width:100%;

						button {
							width:100%;
						}
					}

					&#availability-info {
						float:none;
						width:100%;
					}
				}
			}
		}
    }

    .update-shipping-addresses {
        font-size: 12px;
    }

    .set-shipping-addresses {
        font-size: 12px;
    }
}