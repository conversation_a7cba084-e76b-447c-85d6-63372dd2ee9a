jQuery( document ).ready( function() {

	// VARIABLE DECLARATION
	const selected_addresses = [];
	const availability = [];

	const address_form = jQuery( '#address_form' );
	const availability_info = jQuery( '#availability-info' );
	const row_input = address_form.find( '#row-add-input' );
	const notice_text = address_form.find( '.wcms-error-notice' );
	const address_empty_text = address_form.find( '.wcms-error-address-empty' );
	const input_item_product = row_input.find( '#row_add_item_product' );
	const input_item_qty = row_input.find( '#row_add_item_qty' );
	const rows_added = address_form.find( '.row-added' );
	const btn_add = row_input.find( '#wcms-add-row' );
	const btn_submits = address_form.find( 'input[type="submit"]' );

	const raw_addresses = Array.isArray( wcms_address_selection_params.addresses ) ? Object.assign({}, wcms_address_selection_params.addresses ) : wcms_address_selection_params.addresses;

	if ( typeof raw_addresses !== 'object' || null === raw_addresses || Object.keys( raw_addresses ).length < 1 ) {
		jQuery( '.wc-shipping-multiple-addresses, .form-row, .address-form-note' ).remove();
		address_empty_text.removeClass( 'hidden' );
		notice_text.addClass( 'hidden' );
		return;
	}

	const addresses = {};

	for ( const key of Object.keys( raw_addresses ) ) {
		let address = raw_addresses[ key ];
		let combined_address_data = address.shipping_first_name + " " + address.shipping_last_name;
		combined_address_data += ", " + address.shipping_address_1;

		if ( null !== address.shipping_address_2 && address.shipping_address_2.length > 0 ) {
			combined_address_data += ", " + address.shipping_address_2;
		}

		if ( null !== address.shipping_city && address.shipping_city.length > 0 ) {
			combined_address_data += ", " + address.shipping_city;
		}

		if ( null !== address.shipping_state && address.shipping_state.length > 0 ) {
			combined_address_data += ", " + address.shipping_state;
		}

		if ( null !== address.shipping_country && address.shipping_country.length > 0 ) {
			combined_address_data += ", " + address.shipping_country;
		}
		
		addresses[ key ] = combined_address_data;
	}

	// HELPER FUNCTION
	const refresh_availability = function() {
		let enable_save = true;
		let enable_add_item = false;

		for ( const item_key in wcms_address_selection_params.contents ) {
			const item = wcms_address_selection_params.contents[ item_key ];
			availability[ item.key ] = item.quantity;
		}

		selected_addresses.forEach( function( address, idx ) {
			const cart_key_qty = parseInt( availability[ address['cart_key'] ] );
			availability[ address['cart_key'] ] = cart_key_qty - parseInt( address['quantity'] );
		} );

		for ( const item_key in availability ) {
			const item = wcms_address_selection_params.contents[ item_key ];
			const item_qty = parseInt( item.quantity );
			const available_qty = parseInt( availability[ item_key ] );
			const usage_percentage = parseFloat( ( ( item_qty - available_qty ) / item_qty ) * 100 ).toFixed( 2 );
			const item_container = availability_info.find( '.' + item_key );
			const usage_container = item_container.find( '.usage-percentage' );
			usage_container.find( '.percentage-bar' ).css( 'width', usage_percentage + '%' );
			usage_container.find( '.percentage-bar .bar-percent' ).text( ( item_qty - available_qty ) + '/' + item_qty );

			usage_container.removeClass( 'exceeded' );
			usage_container.removeClass( 'still-available' );
			if ( usage_percentage < 100 ) {
				usage_container.addClass( 'still-available' );
			}  else if ( usage_percentage > 100 ) {
				usage_container.addClass( 'exceeded' );
			}

			if ( available_qty !== 0 ) {
				enable_save = false;
			}

			if ( available_qty > 0 ) {
				enable_add_item = true;
			}
		}

		if ( enable_add_item ) {
			row_input.removeClass( 'hidden' );
			notice_text.removeClass( 'hidden' );
		} else {
			row_input.addClass( 'hidden' );
			notice_text.addClass( 'hidden' );
		}
		
		btn_submits.prop( 'disabled', ! enable_save );
	}

	const refresh_tbody = function() {
		const tbody_contents = jQuery( '#content-addresses' );
		const rows_added = tbody_contents.find( 'tr.row-added' );
		
		rows_added.remove();
		selected_addresses.forEach( function( address, idx ) {
			const new_row = tbody_contents.find( '.row-template' ).clone();
			const new_product = new_row.find( '.input-product' );
			const new_quantity = new_row.find( '.input-quantity' );
			const new_address = new_row.find( '.input-address' );

			new_row.removeClass( 'row-template' );
			new_row.addClass( 'row-added' );
			new_row.data( 'row-index', idx );
			new_row.data( 'cart-key', address['cart_key'] );

			new_product.val( address['product'] );
			new_product.attr( 'name', 'items[' + address['cart_key'] + '][product][]' );
			new_product.data( 'row-index', idx );

			new_quantity.val( address['quantity'] );
			new_quantity.attr( 'name', 'items[' + address['cart_key'] + '][qty][]' );
			new_quantity.on( 'change', handle_qty_change );

			new_address.val( address['address_idx'] );
			new_address.attr( 'name', 'items[' + address['cart_key'] + '][address][]' );
			new_address.data( 'row-index', idx );
			new_address.on( 'change', handle_select_change );
			
			new_row.find( '.product-text' ).text( address['product_name'] );

			const btn_delete = new_row.find( '.delete-item' );
			btn_delete.data( 'row-index', idx );
			btn_delete.on( 'click', handle_btn_delete );

			const btn_split = new_row.find( '.split-item' );
			btn_split.data( 'row-index', idx );
			btn_split.on( 'click', handle_btn_split );

			new_row.appendTo( tbody_contents );
		} );
		refresh_availability();
		implement_exit_confirmation();
	}

	const add_selected_addresses = function( qty, cart_key, address ) {
		const new_data = {};
		for ( const item_key in wcms_address_selection_params.contents ) {
			const item = wcms_address_selection_params.contents[ item_key ];

			if ( item.key === cart_key ) {
				new_data['cart_key'] = item.key;
				new_data['product_name'] = item.product_name;
				new_data['product'] = item.product_id;
			}
		}

		new_data['quantity'] = qty;
		new_data['address_idx'] = address;
		new_data['address_text'] = addresses[ address ];

		selected_addresses.push( new_data );
	}

	const implement_exit_confirmation = function() {
		jQuery( window ).on( 'beforeunload', function(){
			return true;
		} );
	}

	const remove_exit_confirmation = function() {
		jQuery( window ).off( 'beforeunload' );
	}



	// EVENTS HANDLER
	const handle_qty_change = function( evt ) {
		const current_input = jQuery( evt.currentTarget );
		const current_row = current_input.closest( '.row-added' );
		const current_idx = current_row.data( 'row-index' );
		const qty_val = parseInt( current_input.val() );

		selected_addresses[ current_idx ].quantity = qty_val;

		refresh_availability();
		implement_exit_confirmation();
	}

	const handle_select_change = function( evt ) {
		const current_input = jQuery( evt.currentTarget );
		const current_idx = current_input.data( 'row-index' );
		const address_val = parseInt( current_input.val() );

		selected_addresses[ current_idx ].address_idx = address_val;
		selected_addresses[ current_idx ].address_text = addresses[ address_val ];
		
		implement_exit_confirmation();
	}

	const handle_btn_delete = function( evt ) {
		evt.preventDefault();

		const current_btn = jQuery( evt.currentTarget );
		const current_idx = current_btn.data( 'row-index' );

		if ( parseInt( current_idx ) > -1 ) {
			selected_addresses.splice( current_idx, 1);
		}

		refresh_tbody();
	};

	const handle_btn_split = function( evt ) {
		evt.preventDefault();
		
		const current_btn = jQuery( evt.currentTarget );
		const current_idx = current_btn.data( 'row-index' );

		const current_qty = parseInt( selected_addresses[ current_idx ].quantity );

		if ( current_qty <= 1 ) {
			return;
		}

		const first_qty = parseInt( current_qty / 2 ) + ( current_qty % 2 );
		const second_qty = current_qty - first_qty;
		const new_addresses = [];

		selected_addresses.forEach( function( address, idx ) {
			if ( idx === parseInt( current_idx ) ) {
				const first_address = {
					...address,
					quantity: first_qty
				};
				new_addresses.push( first_address );

				const second_address = {
					...address,
					quantity: second_qty
				};
				new_addresses.push( second_address );
			} else {
				new_addresses.push( { ...address } );
			}
		} );
		selected_addresses.splice( 0, selected_addresses.length );

		new_addresses.forEach( function( address, idx ) {
			selected_addresses.push( address );
		} );

		refresh_tbody();
	};

	const handle_input_product_change = function( evt ) {
		const current_input = jQuery( evt.currentTarget );
		let cart_key = '';

		for ( const item_key in wcms_address_selection_params.contents ) {
			const item = wcms_address_selection_params.contents[ item_key ];

			if ( parseInt( current_input.val() ) === parseInt( item.product_id ) ) {
				cart_key = item.key;
				break;	
			}
		}

		const avail_qty = parseInt( availability[ cart_key ] );
		input_item_qty.prop( 'max', avail_qty );

		if ( input_item_qty.val() > avail_qty ) {
			input_item_qty.val( avail_qty );
		}
	}

	const handle_input_qty_change = function( evt ) {
		const current_input = jQuery( evt.currentTarget );
		const max_input = parseInt( current_input.prop( 'max' ) );

		if ( current_input.val() > max_input ) {
			current_input.val( max_input );
		}
	}

	const handle_btn_add = function( evt ) {
		evt.preventDefault();
		const current_btn = jQuery( evt.currentTarget );
		const row_parents = current_btn.closest( '#row-add-input' );
		const inp_qty = row_parents.find( '#row_add_item_qty' );
		const inp_product = row_parents.find( '#row_add_item_product' );
		const inp_address = row_parents.find( '#row_add_item_address' );
		const qty_val = inp_qty.val();
		const item_key_val = inp_product.val();
		const address_val = inp_address.val();

		if ( ! qty_val || ! item_key_val || ! address_val ) {
			alert( current_btn.data( 'error-text' ) );
			return false;
		}

		add_selected_addresses( qty_val, item_key_val, address_val );

		refresh_tbody();

		inp_qty.val( 0 );
		inp_product.val( "" );
		inp_address.val( "" );
	};

	const handle_btn_submit = function( evt ) {
		remove_exit_confirmation();
		return true;
	};



	// INITIATION
	rows_added.each( function() {
		let current_row = jQuery( this );
		const inp_qty = current_row.find( '.input-quantity' );
		const inp_address = current_row.find( '.input-address' );
		const btn_split = current_row.find( '.split-item' );
		const btn_delete = current_row.find( '.delete-item' );
		const qty_val = inp_qty.val();
		const address_val = inp_address.val();
		const cart_key = current_row.data( 'cart-key' );

		inp_qty.on( 'change', handle_qty_change );
		inp_address.on( 'change', handle_select_change );
		btn_split.on( 'click', handle_btn_split );
		btn_delete.on( 'click', handle_btn_delete );

		add_selected_addresses( qty_val, cart_key, address_val );
	} );
	refresh_availability();

	btn_submits.on( 'click', handle_btn_submit );
	btn_add.on( 'click', handle_btn_add );
	input_item_product.on( 'change', handle_input_product_change );
	input_item_qty.on( 'change', handle_input_qty_change );
} );