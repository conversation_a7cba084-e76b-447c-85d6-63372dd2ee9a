# WooCommerce Shipping Multiple Addresses - WooCommerce 10.2 Compatibility QA Test Plan

## Overview
This QA test plan covers the compatibility fixes applied to make the WooCommerce Shipping Multiple Addresses plugin compatible with WooCommerce 10.2 Beta 2, specifically addressing checkout block functionality and email template issues.

## Test Environment Setup

### Prerequisites
- WordPress 6.4+
- WooCommerce 10.2 Beta 2
- WooCommerce Shipping Multiple Addresses plugin (with applied fixes)
- Block-based checkout enabled
- Test products with different shipping requirements

### Test Data Setup
1. Create test products with different weights/dimensions
2. Set up multiple shipping zones and methods
3. Configure plugin settings:
   - Enable multiple shipping addresses
   - Enable cart duplication
   - Configure shipping notes, dates, and gifts options
   - Set up email templates

## Critical Test Cases

### 1. Checkout Block Integration Tests

#### Test 1.1: Basic Checkout Block Loading
**Objective**: Verify checkout blocks load without JavaScript errors
**Steps**:
1. Navigate to checkout page with block-based checkout
2. Open browser developer console
3. Check for JavaScript errors related to experimental APIs
**Expected Result**: No console errors related to `ExperimentalOrderShippingPackages` or `ExperimentalDiscountsMeta`

#### Test 1.2: Multiple Address Selection
**Objective**: Verify multiple address functionality works in blocks
**Steps**:
1. Add multiple products to cart
2. Navigate to checkout
3. Click "Set Multiple Addresses" button
4. Configure different shipping addresses for products
5. Return to checkout
**Expected Result**: Multiple addresses display correctly in checkout block

#### Test 1.3: Shipping Method Selection
**Objective**: Verify shipping methods display per address
**Steps**:
1. Set up multiple addresses with different shipping zones
2. Navigate to checkout
3. Verify shipping methods appear for each address
4. Select different shipping methods for each address
**Expected Result**: Shipping methods display and can be selected independently

### 2. Store API Integration Tests

#### Test 2.1: Cart Data Extension
**Objective**: Verify Store API extensions work correctly
**Steps**:
1. Add products to cart
2. Set multiple addresses
3. Check browser network tab for Store API calls
4. Verify cart extension data structure
**Expected Result**: Store API returns proper schema-compliant data

#### Test 2.2: Extension Cart Update
**Objective**: Verify cart updates work with new API
**Steps**:
1. Add shipping notes, dates, or gifts
2. Verify data persists in localStorage
3. Check that extensionCartUpdate calls succeed
**Expected Result**: Cart updates without errors, data persists

### 3. Email Template Tests

#### Test 3.1: Order Confirmation Email
**Objective**: Verify email templates render correctly
**Steps**:
1. Complete an order with multiple addresses
2. Check order confirmation email
3. Verify multiple shipping addresses display properly
**Expected Result**: Email displays all shipping addresses with proper formatting

#### Test 3.2: Partial Shipment Email
**Objective**: Verify partial shipment emails work
**Steps**:
1. Create order with multiple packages
2. Mark one package as completed from admin
3. Send partial shipment email
**Expected Result**: Email sends successfully with correct package information

### 4. Admin Interface Tests

#### Test 4.1: Order Management
**Objective**: Verify admin order management works
**Steps**:
1. View order in admin with multiple addresses
2. Update package statuses
3. Send package completion emails
**Expected Result**: All admin functions work without errors

### 5. Compatibility Tests

#### Test 5.1: Legacy Checkout Compatibility
**Objective**: Verify plugin works with legacy checkout
**Steps**:
1. Switch to legacy checkout
2. Test multiple address functionality
3. Complete order
**Expected Result**: All functionality works in legacy mode

#### Test 5.2: Theme Compatibility
**Objective**: Test with different themes
**Steps**:
1. Test with Storefront theme
2. Test with Twenty Twenty-Four theme
3. Test with block-based theme
**Expected Result**: Consistent functionality across themes

## Performance Tests

### Test 6.1: JavaScript Loading Performance
**Objective**: Verify optimized asset loading
**Steps**:
1. Check network tab for asset loading times
2. Verify proper dependency loading
3. Check for unnecessary API calls
**Expected Result**: Assets load efficiently without blocking

### Test 6.2: Store API Performance
**Objective**: Verify API calls are optimized
**Steps**:
1. Monitor Store API call frequency
2. Check response times
3. Verify data caching
**Expected Result**: Minimal API calls with reasonable response times

## Regression Tests

### Test 7.1: Core Plugin Features
**Objective**: Verify all existing features still work
**Steps**:
1. Test address book functionality
2. Test shipping method filtering
3. Test order splitting
4. Test email notifications
**Expected Result**: All existing features work as before

### Test 7.2: Settings and Configuration
**Objective**: Verify plugin settings work correctly
**Steps**:
1. Test all plugin settings
2. Verify settings save correctly
3. Test setting effects on frontend
**Expected Result**: All settings function properly

## Error Handling Tests

### Test 8.1: JavaScript Error Recovery
**Objective**: Verify graceful error handling
**Steps**:
1. Simulate network errors
2. Test with invalid data
3. Test with missing dependencies
**Expected Result**: Plugin handles errors gracefully without breaking checkout

### Test 8.2: API Error Handling
**Objective**: Verify Store API error handling
**Steps**:
1. Test with invalid cart data
2. Test with network timeouts
3. Test with server errors
**Expected Result**: Appropriate error messages, no checkout blocking

## Browser Compatibility

### Test 9.1: Cross-Browser Testing
**Objective**: Verify compatibility across browsers
**Browsers to Test**:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
**Expected Result**: Consistent functionality across all browsers

## Mobile Compatibility

### Test 10.1: Mobile Checkout
**Objective**: Verify mobile checkout functionality
**Steps**:
1. Test on mobile devices
2. Test responsive design
3. Test touch interactions
**Expected Result**: Full functionality on mobile devices

## Test Completion Criteria

### Pass Criteria
- All critical test cases pass
- No JavaScript console errors
- Email templates render correctly
- Store API integration works properly
- Performance meets acceptable standards

### Fail Criteria
- Checkout process breaks
- JavaScript errors prevent functionality
- Email templates don't render
- Store API calls fail
- Significant performance degradation

## Test Reporting

### Bug Report Template
```
**Bug Title**: [Brief description]
**Severity**: Critical/High/Medium/Low
**Steps to Reproduce**: [Detailed steps]
**Expected Result**: [What should happen]
**Actual Result**: [What actually happens]
**Browser/Environment**: [Browser and version]
**Screenshots**: [If applicable]
```

### Test Sign-off
- [ ] All critical tests passed
- [ ] All high-priority tests passed
- [ ] Performance tests acceptable
- [ ] Cross-browser compatibility verified
- [ ] Mobile compatibility verified
- [ ] Regression tests passed

**QA Engineer**: _______________  
**Date**: _______________  
**WooCommerce Version**: 10.2 Beta 2  
**Plugin Version**: 4.2.9 (with compatibility fixes)
