<?php
/**
 * Store_API_Extension class.
 *
 * A class to extend the store public API with Multiple Addresses shipping functionality.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

namespace WooCommerce\Multiple_Addresses;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\StoreApi\Schemas\V1\CartSchema;
use WC_Ship_Multiple;

/**
 * Store API Extension.
 */
class Store_API_Extension {
	/**
	 * Global WC_Ship_Multiple variable.
	 *
	 * @var WC_Ship_Multiple
	 */
	private static WC_Ship_Multiple $wcms;

	/**
	 * Plugin Identifier, unique to each plugin.
	 *
	 * @var string
	 */
	const IDENTIFIER = 'wc_shipping_multiple_addresses';

	/**
	 * Bootstraps the class and hooks required data.
	 *
	 * @since 1.0.0
	 */
	public static function init() {
		self::$wcms = $GLOBALS['wcms'];
		self::extend_store();
	}

	/**
	 * Registers the data into each endpoint.
	 */
	public static function extend_store() {
		$logger              = wc_get_logger();
		$update_callback_reg = woocommerce_store_api_register_update_callback(
			array(
				'namespace' => self::IDENTIFIER,
				'callback'  => function( $data ) {
					self::update_shipping_notes( $data );
				},
			)
		);

		if ( is_wp_error( $update_callback_reg ) ) {
			$logger->error( $update_callback_reg->get_error_message() );
			return;
		}

		$endpoint_data_reg = woocommerce_store_api_register_endpoint_data(
			array(
				'endpoint'        => CartSchema::IDENTIFIER,
				'namespace'       => self::IDENTIFIER,
				'data_callback'   => array( static::class, 'data_callback' ),
				'schema_callback' => array( static::class, 'schema_callback' ),
				'schema_type'     => ARRAY_A,
			)
		);

		if ( is_wp_error( $endpoint_data_reg ) ) {
			$logger->error( $endpoint_data_reg->get_error_message() );
			return;
		}
	}

	/**
	 * Update multiple shipping notes an delivery dates.
	 *
	 * @param array $post_data Shipping notes data from POST.
	 */
	public static function update_shipping_notes( $post_data ) {
		if ( ! empty( $post_data['notes'] ) && is_array( $post_data['notes'] ) ) {
			wcms_session_set( 'wcms_package_notes', $post_data['notes'] );
		}

		if ( ! empty( $post_data['dates'] ) && is_array( $post_data['dates'] ) ) {
			wcms_session_set( 'wcms_delivery_dates', $post_data['dates'] );
		}

		if ( ! empty( $post_data['gifts'] ) && is_array( $post_data['gifts'] ) ) {
			wcms_session_set( 'wcms_package_gifts', $post_data['gifts'] );
		}
	}

	/**
	 * Store API extension data callback.
	 *
	 * @return array
	 */
	public static function data_callback() {
		$packages          = WC()->cart->get_shipping_packages();
		$shipping_packages = WC()->shipping->get_packages();

		foreach ( $shipping_packages as $index => $package ) {
			if ( ! isset( $packages[ $index ] ) ) {
				continue;
			}

			$packages[ $index ]['rates'] = $package['rates'];
		}

		$data = ( self::$wcms->cart->cart_is_eligible_for_multi_shipping() ) ? self::prepare_multi_shipping_data( $packages ) : array();

		return array(
			'duplicate_cart'      => self::prepare_duplicate_cart_button(),
			'multi_shipping_info' => array(
				'data'       => $data,
				'static_var' => self::prepare_settings_static_variable(),
			),
		);
	}

	/**
	 * Store API extension schema callback.
	 *
	 * @return array Registered schema.
	 */
	public static function schema_callback() {
		return array(
			'duplicate_cart' => array(
				'description' => __( 'MS duplicate cart variable', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'object',
				'context'     => array( 'view' ),
				'readonly'    => true,
				'properties'  => array(
					'text' => array(
						'description' => __( 'Duplicate cart button text', 'woocommerce-shipping-multiple-addresses' ),
						'type'        => 'string',
						'context'     => array( 'view' ),
						'readonly'    => true,
					),
					'url' => array(
						'description' => __( 'Duplicate cart URL', 'woocommerce-shipping-multiple-addresses' ),
						'type'        => 'string',
						'context'     => array( 'view' ),
						'readonly'    => true,
					),
				),
			),
			'multi_shipping_info' => array(
				'description' => __( 'MS info', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'object',
				'context'     => array( 'view' ),
				'readonly'    => true,
				'properties'  => array(
					'data' => array(
						'description' => __( 'Multi shipping data', 'woocommerce-shipping-multiple-addresses' ),
						'type'        => 'array',
						'context'     => array( 'view' ),
						'readonly'    => true,
					),
					'static_var' => array(
						'description' => __( 'Static variables', 'woocommerce-shipping-multiple-addresses' ),
						'type'        => 'object',
						'context'     => array( 'view' ),
						'readonly'    => true,
					),
				),
			),
		);
	}

	/**
	 * Prepare the duplicate cart data.
	 */
	public static function prepare_duplicate_cart_button() {
		$ms_settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		if ( ! isset( $ms_settings['cart_duplication'] ) || 'no' === $ms_settings['cart_duplication'] ) {
			return array();
		}

		$dupe_url = add_query_arg(
			array(
				'duplicate-form' => '1',
				'_wcmsnonce'     => wp_create_nonce( 'wcms-duplicate-cart' ),
			),
			get_permalink( wc_get_page_id( 'multiple_addresses' ) )
		);

		return array(
			'text' => esc_html__( 'Duplicate Cart', 'woocommerce-shipping-multiple-addresses' ),
			'url'  => $dupe_url,
		);
	}

	/**
	 * Prepare multiple shipping data.
	 *
	 * @param array $packages Cart packages.
	 */
	public static function prepare_multi_shipping_data( $packages ) {
		$page_id                 = wc_get_page_id( 'multiple_addresses' );
		$ms_permalink            = get_permalink( $page_id );
		$chosen_shipping_methods = wcms_session_get( 'shipping_methods' ) ?? array();
		$notes                   = wcms_session_get( 'wcms_package_notes' ) ?? array();
		$dates                   = wcms_session_get( 'wcms_delivery_dates' ) ?? array();
		$gifts                   = wcms_session_get( 'wcms_package_gifts' ) ?? array();
		$ms_packages             = array();

		foreach ( $packages as $x => $package ) {
			$products = $package['contents'];

			if ( self::$wcms->is_address_empty( $package['destination'] ) ) {
				$ms_packages[ $x ]['error_message'] = esc_html__( 'The following items do not have a shipping address assigned.', 'woocommerce-shipping-multiple-addresses' );
			} elseif ( ! isset( $package['rates'] ) || empty( $package['rates'] ) ) {
				$ms_packages[ $x ]['error_message'] = esc_html__( 'There are no shipping options available for the following items.', 'woocommerce-shipping-multiple-addresses' );
			}

			// translators: %d is package index.
			$ms_packages[ $x ]['package_name']      = sprintf( __( 'Shipment %d Address', 'woocommerce-shipping-multiple-addresses' ), intval( $x ) + 1 );
			$ms_packages[ $x ]['ms_permalink']      = $ms_permalink;
			$ms_packages[ $x ]['formatted_address'] = wcms_get_formatted_address( $package['destination'] );
			$ms_packages[ $x ]['products']          = array();

			foreach ( $products as $p => $product ) {
				$attributes    = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
				$product_title = wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
				$product_attr  = wp_kses_post( str_replace( "\n", '<br/>', $attributes ) );

				$ms_packages[ $x ]['products'][] = array(
					'attribute' => $product_attr,
					'title'     => $product_title,
				);
			}

			$ship_rates             = self::get_multi_addr_shipping_rates( $package, $packages );
			$chosen_shipping_method = array();
			$session_chosen_methods = WC()->session->get( 'chosen_shipping_methods' );

			foreach ( $ship_rates as $method_id => $method ) {
				if ( 'multiple_shipping' === $method_id ) {
					continue;
				}

				if ( isset( $chosen_shipping_methods[ $x ]['id'] ) && $chosen_shipping_methods[ $x ]['id'] === $method['option_value'] ) {
					$chosen_shipping_method = $method;
				} elseif ( isset( $session_chosen_methods[ $x ] ) && $session_chosen_methods[ $x ] === $method['option_value'] ) {
					$chosen_shipping_method = $method;
				}
			}

			$ms_packages[ $x ]['note']                   = isset( $notes[ $x ] ) ? $notes[ $x ] : '';
			$ms_packages[ $x ]['date']                   = isset( $dates[ $x ] ) ? $dates[ $x ] : '';
			$ms_packages[ $x ]['gift']                   = isset( $gifts[ $x ] ) ? $gifts[ $x ] : 'no';
			$ms_packages[ $x ]['chosen_shipping_method'] = $chosen_shipping_method;
			$ms_packages[ $x ]['session_cart_addresses'] = wcms_session_get( 'cart_item_addresses' );
		}

		return $ms_packages;
	}

	/**
	 * Formatting the shipping method label.
	 *
	 * @param object $method Shipping Method object.
	 */
	public static function get_formatted_shipping_method_label( $method ) {
		$label = esc_html( $method->label );

		if ( $method->cost <= 0 ) {
			return $label;
		}

		$shipping_tax = $method->get_shipping_tax();
		$label       .= ' &mdash; ';

		// Append price to label using the correct tax settings.
		if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {
			$label .= wc_price( $method->cost );
			if ( $shipping_tax > 0 && WC()->cart->prices_include_tax ) {
				$label .= ' ' . WC()->countries->ex_tax_or_vat();
			}

			return $label;
		}

		$label .= wc_price( $method->cost + $shipping_tax );
		if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
			$label .= ' ' . WC()->countries->inc_tax_or_vat();
		}

		return $label;
	}

	/**
	 * Get shipping rates for the multiple address.
	 *
	 * @param array $package Current package.
	 * @param array $packages the whole cart packages.
	 *
	 * @return array
	 */
	public static function get_multi_addr_shipping_rates( $package, $packages ) {
		$shipping_rates = array();

		$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
		if ( isset( $sess_cart_addresses ) && ! empty( $sess_cart_addresses ) ) {
			// Always allow users to select shipping.
			foreach ( $package['rates'] as $rate ) {
				$rate->label                 = self::get_formatted_shipping_method_label( $rate );
				$shipping_rates[ $rate->id ] = $rate;
				$shipping_rates[ $rate->id ] = array(
					'option_label' => wp_strip_all_tags( $rate->label ),
					'option_value' => esc_attr( $rate->id ),
				);
			}
		} elseif ( self::$wcms->packages_have_different_origins( $packages ) || self::$wcms->packages_have_different_methods( $packages ) || self::$wcms->packages_contain_methods( $packages ) ) {

			$type = ( self::$wcms->packages_have_different_origins( $packages ) || self::$wcms->packages_have_different_methods( $packages ) ) ? 1 : 2;

			// Show shipping methods available to each package.
			foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

				if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
					continue;
				}

				if ( ! $shipping_method->is_available( $package ) ) {
					continue;
				}

				// Reset Rates.
				$shipping_method->rates = array();

				// Calculate Shipping for package.
				$shipping_method->calculate_shipping( $package );

				// Place rates in package array.
				if ( empty( $shipping_method->rates ) || ! is_array( $shipping_method->rates ) ) {
					continue;
				}

				foreach ( $shipping_method->rates as $rate ) {
					$rate->label  = self::get_formatted_shipping_method_label( $rate );
					$option_label = ( 1 === $type ) ? wp_kses_post( wc_cart_totals_shipping_method_label( $rate ) ) : esc_html( $rate->label );
					$option_value = ( 1 === $type ) ? esc_attr( $rate->id ) . '||' . wp_strip_all_tags( $rate->label ) : esc_attr( $rate->id );

					$shipping_rates[ $rate->id ] = array(
						'option_label' => $option_label,
						'option_value' => $option_value,
					);
				}
			}
		}

		return $shipping_rates;
	}

	/**
	 * Prepare variables from the settings and static text or urls.
	 */
	public static function prepare_settings_static_variable() {
		$id                = wc_get_page_id( 'multiple_addresses' );
		$reset_url         = add_query_arg(
			array(
				'wcms_reset_address' => true,
				'nonce'              => wp_create_nonce( 'wcms_reset_address_security' ),
			),
			wc_get_checkout_url()
		);
		$modify_addr_link  = get_permalink( $id );
		$add_addr_link     = add_query_arg( 'cart', 1, get_permalink( $id ) );
		$has_multi_address = ( self::$wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() );
		$note_limit        = ! empty( self::$wcms->gateway_settings['checkout_notes_limit'] ) ? absint( self::$wcms->gateway_settings['checkout_notes_limit'] ) : '';
		$show_notes        = ( ! empty( self::$wcms->gateway_settings['checkout_notes'] ) && 'yes' === self::$wcms->gateway_settings['checkout_notes'] ) ? true : false;
		$show_datepicker   = ( ! empty( self::$wcms->gateway_settings['checkout_datepicker'] ) && 'yes' === self::$wcms->gateway_settings['checkout_datepicker'] ) ? true : false;
		$valid_dates       = ( ! empty( self::$wcms->gateway_settings['checkout_valid_days'] ) ) ? self::$wcms->gateway_settings['checkout_valid_days'] : array();
		$excluded_dates    = ( ! empty( self::$wcms->gateway_settings['checkout_exclude_dates'] ) ) ? self::$wcms->gateway_settings['checkout_exclude_dates'] : array();
		$show_gifts        = \WC_MS_Gifts::is_enabled();
		$lang_notification = \WC_Ship_Multiple::$lang['notification'];
		$lang_button       = \WC_Ship_Multiple::$lang['btn_items'];

		return array(
			'is_eligible_wcms'  => self::$wcms->cart->cart_is_eligible_for_multi_shipping(),
			'has_multi_address' => $has_multi_address,
			'reset_url'         => $reset_url,
			'modify_addr_link'  => $modify_addr_link,
			'add_addr_link'     => $add_addr_link,
			'modify_addr_text'  => esc_html__( 'Modify/Add Address', 'woocommerce-shipping-multiple-addresses' ),
			'reset_addr_text'   => esc_html__( 'Reset Address', 'woocommerce-shipping-multiple-addresses' ),
			'lang_notification' => esc_html( $lang_notification ),
			'lang_button'       => esc_attr( $lang_button ),
			'show_notes'        => $show_notes,
			'note_label_text'   => esc_html( 'Note:', 'woocommerce-shipping-multiple-addresses' ),
			'show_gifts'        => $show_gifts,
			'gifts_text'        => esc_html__( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ),
			'show_datepicker'   => $show_datepicker,
			'date_label_text'   => esc_html( 'Shipping date:', 'woocommerce-shipping-multiple-addresses' ),
			'valid_dates'       => $valid_dates,
			'excluded_dates'    => $excluded_dates,
			'date_error_text'   => esc_html__( 'The item cannot be send on', 'woocommerce-shipping-multiple-addresses' ),
			'note_limit'        => $note_limit,
			'no_method_text'    => esc_html__( 'No shipping method', 'woocommerce-shipping-multiple-addresses' ),
		);
	}
}
