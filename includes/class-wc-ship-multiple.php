<?php
/**
 * Class WC_Ship_Multiple file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\Multiple_Addresses\Blocks_Integration;
use WooCommerce\Multiple_Addresses\Store_API_Extension;

/**
 * Class WC_Ship_Multiple.
 */
class WC_Ship_Multiple {

	const FILE = WC_MS_FILE;

	/**
	 * WC_MS_Front object.
	 *
	 * @var WC_MS_Front
	 */
	public $front;

	/**
	 * WC_MS_Cart object.
	 *
	 * @var WC_MS_Cart
	 */
	public $cart;

	/**
	 * WC_MS_Packages object.
	 *
	 * @var WC_MS_Packages
	 */
	public $packages;

	/**
	 * WC_MS_Address_Book object.
	 *
	 * @var WC_MS_Address_Book
	 */
	public $address_book;

	/**
	 * WC_MS_Checkout object.
	 *
	 * @var WC_MS_Checkout
	 */
	public $checkout;

	/**
	 * WC_MS_Notes object.
	 *
	 * @var WC_MS_Notes
	 */
	public $notes;

	/**
	 * WC_MS_Gifts object.
	 *
	 * @var WC_MS_Gifts
	 */
	public $gifts;

	/**
	 * WC_MS_Admin object.
	 *
	 * @var WC_MS_Admin
	 */
	public $admin;

	/**
	 * WC_MS_Order object.
	 *
	 * @var WC_MS_Order
	 */
	public $order;

	/**
	 * WC_MS_Order_Shipment object.
	 *
	 * @var WC_MS_Order_Shipment
	 */
	public $shipments;

	/**
	 * WC_MS_Customer_Order_Csv_Export object.
	 *
	 * @var WC_MS_Customer_Order_Csv_Export
	 */
	public $csv_export;

	/**
	 * Order meta key name.
	 *
	 * @var string
	 */
	public $meta_key_order = '_shipping_methods';

	/**
	 * Meta key settings ( Might not be used anymore ).
	 *
	 * @var string
	 */
	public $meta_key_settings = '_shipping_settings';

	/**
	 * Settings from shipping settings.
	 *
	 * @var array
	 */
	public $settings = null;

	/**
	 * Saved settings.
	 *
	 * @var array
	 */
	public $gateway_settings = null;

	/**
	 * Notification and button text.
	 *
	 * @var array
	 */
	public static $lang = array(
		'notification' => 'You may use multiple shipping addresses on this cart',
		'btn_items'    => 'Set Multiple Addresses',
	);

	/**
	 * Class constructor.
	 */
	public function __construct() {
		// Load the shipping options.
		$this->settings = get_option( $this->meta_key_settings, array() );

		add_action( 'after_setup_theme', array( $this, 'load_textdomain' ) );

		// Define shortcodes.
		add_shortcode( 'woocommerce_select_multiple_addresses', array( $this, 'draw_form' ) );
		add_shortcode( 'woocommerce_account_addresses', array( $this, 'account_addresses' ) );

		// Override needs shipping method and totals.
		add_action( 'woocommerce_init', array( $this, 'wc_init' ) );
		add_action( 'woocommerce_init', array( $this, 'maybe_install_pages' ) );

		// Declare the HPOS compatibility for this plugin.
		add_action( 'before_woocommerce_init', array( $this, 'declare_hpos_compatibility' ) );

		require_once WC_MS_ABSPATH . 'includes/class-wc-multiple-shipping-settings.php';

		add_filter( 'woocommerce_shipping_methods', array( $this, 'add_multiple_shipping_method' ) );

		// Register Blocks Integration.
		add_action( 'woocommerce_blocks_loaded', array( $this, 'register_blocks_integration' ) );
		add_action( 'woocommerce_blocks_loaded', array( $this, 'extend_store_api' ) );

		// Subscribe to automated translations.
		add_filter( 'woocommerce_translations_updates_for_' . basename( WC_MS_FILE, '.php' ), '__return_true' );

		$settings               = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$this->gateway_settings = $settings;

		if ( isset( $settings['lang_notification'] ) ) {
			self::$lang['notification'] = $settings['lang_notification'];
		}

		if ( isset( $settings['lang_btn_items'] ) ) {
			self::$lang['btn_items'] = $settings['lang_btn_items'];
		}

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-post-types.php';

		include_once WC_MS_ABSPATH . 'includes/functions.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-gifts.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-notes.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-checkout.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-cart.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-packages.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-address-book.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-front.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-admin.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order-type-order-shipment.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order-shipment.php';

		include_once WC_MS_ABSPATH . 'includes/integrations/class-wc-ms-customer-order-csv-export.php';

		include_once ABSPATH . 'wp-admin/includes/plugin.php';

		if (
			is_plugin_active( 'se_woocommerce/shippingeasy_order.php' ) ||
			is_plugin_active( 'woocommerce-shippingeasy/woocommerce-shippingeasy.php' )
		) {
			include_once WC_MS_ABSPATH . 'includes/class-wc-ms-shipping-easy.php';
		}

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-api.php';
		new WC_MS_API();

		$this->gifts        = new WC_MS_Gifts( $this );
		$this->notes        = new WC_MS_Notes( $this );
		$this->checkout     = new WC_MS_Checkout( $this );
		$this->cart         = new WC_MS_Cart( $this );
		$this->packages     = new WC_MS_Packages( $this );
		$this->address_book = new WC_MS_Address_Book( $this );
		$this->front        = new WC_MS_Front( $this );
		$this->admin        = new WC_MS_Admin( $this );
		$this->order        = new WC_MS_Order( $this );
		$this->shipments    = new WC_MS_Order_Shipment( $this );
		$this->csv_export   = new WC_MS_Customer_Order_Csv_Export( $this );

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-shipworks.php';

		$this->load_compat_classes();
	}

	/**
	 * Load textdomain for localization.
	 */
	public function load_textdomain() {
		load_plugin_textdomain( 'woocommerce-shipping-multiple-addresses', false, basename( WC_MS_ABSPATH ) . '/languages' );
	}

	/**
	 * Add multiple shipping settings inside WooCommerce > Settings > Shipping page.
	 *
	 * @param array $methods All of registered shipping method.
	 */
	public function add_multiple_shipping_method( $methods ) {
		if ( in_array( 'woocommerce-shipping-multiple-addresses/woocommerce-shipping-multiple-addresses.php', get_option( 'active_plugins' ), true ) ) {
			$methods['multiple_shipping'] = 'WC_Multiple_Shipping_Settings';
		}

		return $methods;
	}

	/**
	 * Creating multiship pages.
	 */
	public function maybe_install_pages() {

		$page_id = wc_get_page_id( 'multiple_addresses' );

		if ( -1 === $page_id || null === get_post( $page_id ) ) {
			// get the checkout page.
			$checkout_id = wc_get_page_id( 'checkout' );

			// add page and assign.
			$page = array(
				'menu_order'     => 0,
				'comment_status' => 'closed',
				'ping_status'    => 'closed',
				'post_author'    => 1,
				'post_content'   => '[woocommerce_select_multiple_addresses]',
				'post_name'      => 'shipping-addresses',
				'post_parent'    => $checkout_id,
				'post_title'     => 'Shipping Addresses',
				'post_type'      => 'page',
				'post_status'    => 'publish',
				'post_category'  => array( 1 ),
			);

			$page_id = wp_insert_post( $page );

			update_option( 'woocommerce_multiple_addresses_page_id', $page_id );
		}

		$page_id = wc_get_page_id( 'account_addresses' );

		if ( -1 === $page_id || null === get_post( $page_id ) ) {
			// Get the checkout page.
			$account_id = wc_get_page_id( 'myaccount' );

			// Add page and assign.
			$page = array(
				'menu_order'     => 0,
				'comment_status' => 'closed',
				'ping_status'    => 'closed',
				'post_author'    => 1,
				'post_content'   => '[woocommerce_account_addresses]',
				'post_name'      => 'account-addresses',
				'post_parent'    => $account_id,
				'post_title'     => 'Shipping Addresses',
				'post_type'      => 'page',
				'post_status'    => 'publish',
				'post_category'  => array( 1 ),
			);

			$page_id = wp_insert_post( $page );

			update_option( 'woocommerce_account_addresses_page_id', $page_id );
		}
	}

	/**
	 * Load compatibility classes.
	 *
	 * @since 3.6.13
	 * @return void
	 */
	public function load_compat_classes() {
		if ( class_exists( 'WC_PIP' ) ) {
			include_once WC_MS_ABSPATH . 'includes/compat/class-wc-pip-compat.php';
			new WC_Pip_Compat();
		}
	}

	/**
	 * Declaring HPOS compatibility.
	 */
	public function declare_hpos_compatibility() {
		if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
			\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', 'woocommerce-shipping-multiple-addresses/woocommerce-shipping-multiple-addresses.php', true );
		}
	}

	/**
	 * Check if multiship is enabled.
	 *
	 * @return boolean.
	 */
	public function is_multiship_enabled(): bool {
		$enabled = true;

		if ( class_exists( 'WC_Role_Methods' ) ) {
			// Process Role-based shipping methods.
			$enabled = false;

			// Get the current logged-in user roles, or set to Guest if not logged in.
			$current_user_roles = is_user_logged_in() ? array_map( 'strtolower', wp_get_current_user()->roles ) : array( 'Guest' );

			foreach ( $current_user_roles as $user_role ) {
				if ( WC_Role_Methods::get_instance()->check_rolea_methods( $user_role, 'multiple_shipping:0' ) ) {
					$enabled = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate multiship activation value.
		 *
		 * @param boolean $enabled is the multiship activation value.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_is_multiship_enabled', $enabled );
	}

	/**
	 * Display privacy notice.
	 */
	public function wc_init() {

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-privacy.php';

		add_action( 'woocommerce_before_order_total', array( $this, 'display_shipping_methods' ) );
		add_action( 'woocommerce_review_order_before_order_total', array( $this, 'display_shipping_methods' ) );
	}

	/**
	 * Display shipping option per product.
	 */
	public function product_options() {
		global $post, $thepostid;

		$settings  = $this->settings;
		$thepostid = $post->ID;

		$ship = WC()->shipping;

		$shipping_methods   = WC()->shipping->shipping_methods;
		$ship_methods_array = array();
		$categories_array   = array();

		foreach ( $shipping_methods as $id => $object ) {
			if ( 'yes' === $object->enabled && 'multiple_shipping' !== $id ) {
				$ship_methods_array[ $id ] = $object->method_title;
			}
		}

		$method = $this->get_product_shipping_method( $thepostid );
		?>
		<p style="border-top: 1px solid #DFDFDF;">
			<strong><?php esc_html_e( 'Shipping Options', 'periship' ); ?></strong>
		</p>
		<p class="form-field method_field">
			<label for="product_method"><?php esc_html_e( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' ); ?></label>
			<select name="product_method[]" id="product_method" class="chzn-select" multiple>
				<option value=""></option>
				<?php
				foreach ( $ship_methods_array as $value => $label ) :
					?>
					<option value="<?php echo esc_attr( $value ); ?>" <?php selected( $method, $value ); ?>><?php echo esc_html( $label ); ?></option>
				<?php endforeach; ?>
			</select>
			<input type="hidden" name="product_option_nonce" value="<?php echo esc_attr( wp_create_nonce( 'wcms_product_option' ) ); ?>" />
		</p>
		<script type="text/javascript">jQuery("#product_method").chosen();</script>
		<?php
	}

	/**
	 * Process metabox. ( Might not be used anymore. )
	 *
	 * @param int $post_id Post ID.
	 */
	public function process_metabox( $post_id ) {
		$settings = $this->settings;

		$nonce = ( isset( $_POST['product_option_nonce'] ) ) ? sanitize_text_field( wp_unslash( $_POST['product_option_nonce'] ) ) : '';
		if ( empty( $nonce ) || ! wp_verify_nonce( $nonce, 'wcms_product_option' ) ) {
			return;
		}

		$zip_origin = null;
		$method     = ( ! empty( $_POST['product_method'] ) && is_array( $_POST['product_method'] ) ) ? sanitize_text_field( wp_unslash( $_POST['product_method'] ) ) : false;

		if ( ! $method ) {
			return;
		}

		// Remove all instances of this product is first.
		foreach ( $settings as $idx => $setting ) {
			$post_id          = intval( $post_id );
			$setting_products = array_map( 'intval', $setting['products'] );

			if ( in_array( $post_id, $setting_products, true ) ) {
				foreach ( $setting_products as $pid => $id ) {
					if ( $id === $post_id ) {
						unset( $settings[ $idx ]['products'][ $pid ] );
					}
				}
			}
		}

		// Look for a matching zip code.
		$matched   = false;
		$zip_match = false;
		foreach ( $settings as $idx => $setting ) {

			if ( $setting['zip'] === $zip_origin ) {
				$zip_match = $idx;
				// Methods must match.
				if ( $method && 0 === count( array_diff( $setting['method'], $method ) ) ) {
					// Zip and method matched
					// add to existing setting.
					$matched                        = true;
					$settings[ $idx ]['products'][] = $post_id;
					break;
				}
			}
		}

		if ( ! $matched ) {
			$settings[] = array(
				'zip'        => $zip_origin,
				'products'   => array( $post_id ),
				'categories' => array(),
				'method'     => $method,
			);
		}

		// Finally, do some cleanup.
		foreach ( $settings as $idx => $setting ) {
			if ( empty( $setting['products'] ) && empty( $setting['categories'] ) ) {
				unset( $settings[ $idx ] );
			}
		}
		$settings = array_merge( $settings, array() );

		// Update the settings.
		update_option( $this->meta_key_settings, $settings );
	}

	/**
	 * Display current user's addresses.
	 */
	public function account_addresses() {
		ob_start();

		$this->cart->load_cart_files();

		$checkout    = WC()->checkout;
		$user        = wp_get_current_user();
		$ship_fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

		if ( 0 === $user->ID ) {
			return;
		}

		$idx = isset( $_GET['edit'] ) ? absint( $_GET['edit'] ) : -1; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( -1 !== $idx ) {
			$updating   = true;
			$other_addr = get_user_meta( $user->ID, 'wc_other_addresses', true );
			$address    = $other_addr[ $idx ];
		} else {
			$updating = false;
			$address  = array();
		}

		// Enqueue scripts.
		wp_enqueue_script( 'wc-country-select' );
		wp_enqueue_script( 'wc-address-i18n' );

		wc_get_template(
			'account-address-form.php',
			array(
				'checkout'    => $checkout,
				'user'        => $user,
				'ship_fields' => $ship_fields,
				'address'     => $address,
				'idx'         => $idx,
				'updating'    => $updating,
			),
			'multi-shipping',
			WC_MS_ABSPATH . 'templates/'
		);

		return ob_get_clean();
	}

	/**
	 * Displaying a form.
	 */
	public function draw_form() {
		if ( is_null( WC()->cart ) ) {
			return '';
		}

		ob_start();

		if ( ! isset( $_GET['order_id'] ) || empty( $_GET['order_id'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended

			$this->cart->load_cart_files();

			$user        = wp_get_current_user();
			$cart        = WC()->cart;
			$checkout    = WC()->checkout;
			$contents    = wcms_get_real_cart_items();
			$ship_fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

			if ( ( 0 !== $user->ID && ! $this->address_book->user_has_shipping_address( $user->ID ) )
				|| ( 0 === $user->ID && 0 === count( $this->address_book->get_user_addresses( $user->ID ) ) )
			) {
				$this->address_book->save_checkout_address( $user->ID );
			}

			$addresses = $this->address_book->get_available_user_addresses( $user );
			unset( $ship_fields['shipping_state']['country'] );

			if ( isset( $_GET['new'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				wc_add_notice( __( 'New address saved', 'woocommerce-shipping-multiple-addresses' ) );
			}

			wc_print_notices();

			if ( empty( $addresses ) || isset( $_REQUEST['address-form'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				wc_get_template(
					'address-form.php',
					array(
						'checkout'    => $checkout,
						'addresses'   => $addresses,
						'ship_fields' => $ship_fields,
					),
					'multi-shipping',
					WC_MS_ABSPATH . 'templates/'
				);
			} elseif ( ! empty( $contents ) ) {
				$relations = wcms_session_get( 'wcms_item_addresses' );

				if ( $addresses ) {
					foreach ( $addresses as $x => $addr ) {
						foreach ( $contents as $key => $value ) {
							if ( isset( $relations[ $x ] ) && ! empty( $relations[ $x ] ) ) :
								$qty = array_count_values( $relations[ $x ] );

								if ( in_array( $key, $relations[ $x ], true ) ) {
									if ( isset( $placed[ $key ] ) ) {
										$placed[ $key ] += $qty[ $key ];
									} else {
										$placed[ $key ] = $qty[ $key ];
									}
								}

							endif;
						}
					}
				}

				$minimized_contents = array_map(
					function( $content ) {
						$product_id   = isset( $content['data'] ) ? $content['data']->get_id() : 0;
						$product_name = isset( $content['data'] ) ? $content['data']->get_name() : '';
						return array(
							'key'          => $content['key'],
							'product_id'   => $product_id,
							'product_name' => $product_name,
							'quantity'     => $content['quantity'],

						);
					},
					$contents
				);

				$minimized_contents = array_filter(
					$minimized_contents,
					function( $content ) {
						return ! empty( $content['product_name'] ) && ! empty( $content['product_id'] );
					}
				);

				wp_enqueue_script( 'wcms-address-selection' );
				wp_localize_script(
					'wcms-address-selection',
					'wcms_address_selection_params',
					/**
					 * Filter to manipulate the countries parameters on JS.
					 *
					 * @param array JSON encoded list of countries and select text.
					 *
					 * @since 3.3.19
					 */
					apply_filters(
						'wcms_address_selection_params',
						array(
							'addresses' => $addresses,
							'contents'  => $minimized_contents,
						)
					)
				);

				$relations = wcms_session_get( 'wcms_item_addresses' );
				if ( empty( $relations ) ) {
					$relations = array();
					foreach ( $contents as $key => $content ) {
						for ( $i = 0; $i < $content['quantity']; $i++ ) {
							$relations[] = $key;
						}
					}
					$relations = array( $relations );
				}

				wc_get_template(
					'shipping-address-table.php',
					array(
						'addresses'   => $addresses,
						'relations'   => $relations,
						'checkout'    => $checkout,
						'contents'    => $contents,
						'ship_fields' => $ship_fields,
						'user'        => $user,
					),
					'multi-shipping',
					WC_MS_ABSPATH . 'templates/'
				);
			}
		} else {
			// Load order and display the addresses.
			$order_id = intval( $_GET['order_id'] ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended
			$order    = wc_get_order( $order_id );

			if ( ! is_user_logged_in() ) {
				return esc_html__( 'Please log in to access this page', 'woocommerce-shipping-multiple-addresses' );
			}

			if ( ! $order instanceof WC_Order ) {
				return esc_html__( 'Order could not be found', 'woocommerce-shipping-multiple-addresses' );
			}

			$current_user = wp_get_current_user();

			if ( $order->get_user_id() !== $current_user->ID && $order->get_billing_email() !== $current_user->user_email ) {
				return esc_html__( 'You don\'t have access to this page', 'woocommerce-shipping-multiple-addresses' );
			}

			$packages = $order->get_meta( '_wcms_packages' );

			if ( ! is_array( $packages ) || empty( $packages ) ) {
				return esc_html__( 'This order does not ship to multiple addresses', 'woocommerce-shipping-multiple-addresses' );
			}

			// load the address fields.
			$this->cart->load_cart_files();

			echo '<table class="shop_tabe"><thead><tr><th class="product-name">' . esc_html__( 'Product', 'woocommerce-shipping-multiple-addresses' ) . '</th><th class="product-quantity">' . esc_html__( 'Qty', 'woocommerce-shipping-multiple-addresses' ) . '</th><th class="product-address">' . esc_html__( 'Address', 'woocommerce-shipping-multiple-addresses' ) . '</th></thead>';
			echo '<tbody>';

			$tr_class = '';
			foreach ( $packages as $x => $package ) {
				$products  = $package['contents'];
				$item_meta = '';
				foreach ( $products as $i => $product ) {
					$tr_class = ( '' === $tr_class ) ? 'alt-table-row' : '';

					if ( isset( $product['data']->item_meta ) && ! empty( $product['data']->item_meta ) ) {
						$item_meta .= '<pre>';
						foreach ( $product['data']->item_meta as $meta ) {
							$item_meta .= $meta['meta_name'] . ': ' . $meta['meta_value'] . "\n";
						}
						$item_meta .= '</pre>';
					}

					/**
					 * Filter to manipulate product title.
					 *
					 * @param string Product title.
					 * @param WC_Product $product Product object.
					 *
					 * @since 3.1
					 */
					$package_product_title = apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product );
					echo '<tr class="' . esc_attr( $tr_class ) . '">';
					echo '<td class="product-name"><a href="' . esc_url( get_permalink( $product['data']->get_id() ) ) . '">' . wp_kses_post( $package_product_title ) . '</a><br />' . wp_kses_post( $item_meta ) . '</td>';
					echo '<td class="product-quantity">' . esc_html( $product['quantity'] ) . '</td>';
					// no need to escape. It's already been filtered by `wcms_get_formatted_address()`.
					echo '<td class="product-address"><address>' . wcms_get_formatted_address( $package['destination'] ) . '</td>'; //phpcs:ignore
					echo '</tr>';
				}
			}

			echo '</table>';
		}

		return ob_get_clean();
	}

	/**
	 * Display multiple shipping methods.
	 */
	public function display_shipping_methods() {

		$packages          = WC()->cart->get_shipping_packages();
		$shipping_packages = WC()->shipping->get_packages();

		foreach ( $shipping_packages as $index => $package ) {
			if ( ! isset( $packages[ $index ] ) ) {
				continue;
			}

			$packages[ $index ]['rates'] = $package['rates'];
		}

		if ( ! $this->cart->cart_is_eligible_for_multi_shipping() ) {
			return;
		}

		$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
		if ( isset( $sess_cart_addresses ) && ! empty( $sess_cart_addresses ) ) {
			// Always allow users to select shipping.
			$this->render_shipping_row( $packages, 0 );
		} elseif ( $this->packages_have_different_origins( $packages ) || $this->packages_have_different_methods( $packages ) ) {
			// Show shipping methods available to each package.
			$this->render_shipping_row( $packages, 1 );
		} elseif ( $this->packages_contain_methods( $packages ) ) {
			// Methods must be combined.
			$this->render_shipping_row( $packages, 2 );
		}
	}

	/**
	 * Render shipping row for multiple shipping.
	 *
	 * @param array $packages Cart packages.
	 * @param int   $type 0=multi-shipping; 1=different packages; 2=same packages.
	 */
	public function render_shipping_row( $packages, $type = 2 ) {

		$page_id         = wc_get_page_id( 'multiple_addresses' );
		$rates_available = false;

		$field_name = 'shipping_method';
		$post       = array();

		// No need for nonce verification. It has been verified on `WC_AJAX::update_order_review()`.
		if ( isset( $_POST['post_data'] ) ) { //phpcs:ignore
			parse_str($_POST['post_data'], $post); //phpcs:ignore
		}

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$title    = ! empty( $settings['title'] ) ? $settings['title'] : __( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' );

		if ( 0 === $type || 1 === $type ) :

			?>
			<tr class="multi_shipping">
				<td style="vertical-align: top;" colspan="1">
					<?php echo esc_html( $title ); ?>

					<div id="shipping_addresses">
						<?php
						foreach ( $packages as $x => $package ) :
							$error_message = '';

							if ( $this->is_address_empty( $package['destination'] ) ) {

								$error_message = esc_html__( 'The following items do not have a shipping address assigned.', 'woocommerce-shipping-multiple-addresses' );

							} elseif ( ! isset( $package['rates'] ) || empty( $package['rates'] ) ) {

								$error_message = esc_html__( 'There are no shipping options available for the following items.', 'woocommerce-shipping-multiple-addresses' );

							}

							if ( ! empty( $error_message ) ) {
								// We have cart items with no set address.
								$products = $package['contents'];
								?>
								<div class="ship_address no_shipping_address">
									<em><?php echo esc_html( $error_message ); ?></em>
									<ul>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<li>
												<strong>
													<?php
													/**
													 * Filter to manipulate product title
													 *
													 * @param string Product title.
													 * @param WC_Product $product Product object.
													 *
													 * @since 3.1
													 */
													echo wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
													?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</li>
										<?php endforeach; ?>
									</ul>
									<?php
									echo '<p style="text-align: center"><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="button modify-address-button">' . esc_html__( 'Assign Shipping Address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									?>
								</div>
								<?php
								continue;
							}

							$shipping_methods = array();
							$products         = $package['contents'];
							$selected         = wcms_session_get( 'shipping_methods' );
							$rates_available  = true;

							if ( 0 === $type ) :
								?>
								<div class="ship_address">
									<dl>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<dd>
												<strong>
													<?php
													/**
													 * Filter to manipulate product title
													 *
													 * @param string Product title.
													 * @param WC_Product $product Product object.
													 *
													 * @since 3.1
													 */
													echo wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
													?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</dd>
										<?php endforeach; ?>
									</dl>
									<?php
									$formatted_address = wcms_get_formatted_address( $package['destination'] );

									// no need to escape. it's already filtered by `wcms_get_formatted_address()`.
									echo '<address>'. $formatted_address .'</address><br />'; //phpcs:ignore ?>
									<?php

									/**
									 * Filter to manipulate product title
									 *
									 * @param int   $x Package index.
									 * @param array $package Cart package.
									 *
									 * @since 3.1
									 */
									do_action( 'wc_ms_shipping_package_block', $x, $package );

									// If at least one shipping method is available.
									$ship_package['rates'] = array();

									foreach ( $package['rates'] as $rate ) {
										$ship_package['rates'][ $rate->id ] = $rate;
									}

									foreach ( $ship_package['rates'] as $method ) {
										if ( 'multiple_shipping' === $method->id ) {
											continue;
										}

										$method->label = esc_html( $method->label );

										if ( $method->cost > 0 ) {
											$shipping_tax   = $method->get_shipping_tax();
											$method->label .= ' &mdash; ';

											// Append price to label using the correct tax settings.
											if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {

												if ( $shipping_tax > 0 ) {
													if ( WC()->cart->prices_include_tax ) {
														$method->label .= wc_price( $method->cost ) . ' ' . WC()->countries->ex_tax_or_vat();
													} else {
														$method->label .= wc_price( $method->cost );
													}
												} else {
													$method->label .= wc_price( $method->cost );
												}
											} else {
												$method->label .= wc_price( $method->cost + $shipping_tax );
												if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
													$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
												}
											}
										}

										$shipping_methods[] = $method;
									}

									// Print the single available shipping method as plain text.
									if ( 1 === count( $shipping_methods ) ) {
										$method = $shipping_methods[0];

										echo esc_html( $method->label );
										echo '<input type="hidden" class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '" value="' . esc_attr( $method->id ) . '">';

										// Show multiple shipping methods in a select list.
									} elseif ( count( $shipping_methods ) > 1 ) {
										if ( ! is_array( $selected ) || ! isset( $selected[ $x ] ) ) {
											$cheapest_rate = wcms_get_cheapest_shipping_rate( $package['rates'] );

											if ( $cheapest_rate ) {
												$selected[ $x ] = $cheapest_rate;
											}
										}

										echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';

										foreach ( $package['rates'] as $rate ) {
											if ( 'multiple_shipping' === $rate->id ) {
												continue;
											}
											$sel = '';

											if ( isset( $selected[ $x ]['id'] ) && $selected[ $x ]['id'] === $rate->id ) {
												$sel = 'selected';
											}

											echo '<option value="' . esc_attr( $rate->id ) . '" ' . esc_attr( $sel ) . '>';
											// No need to escape. Already use `strip_tags()`.
											echo strip_tags( $rate->label ); //phpcs:ignore
											echo '</option>';
										}

										echo '</select>';
									} else {
										echo '<p>' . esc_html__( '(1) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
									}

									$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
									if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
										echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									}
									?>
								</div>
								<?php
							elseif ( 1 === $type ) :
								?>
								<div class="ship_address">
									<dl>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<dd>
												<strong>
												<?php
												/**
												 * Filter to manipulate product title
												 *
												 * @param string Product title.
												 * @param WC_Product $product Product object.
												 *
												 * @since 3.1
												 */
												echo esc_html( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
												?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</dd>
										<?php endforeach; ?>
									</dl>
									<?php
									// If at least one shipping method is available.
									// Calculate shipping method rates.
									$ship_package['rates'] = array();

									foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

										if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
											continue;
										}

										if ( $shipping_method->is_available( $package ) ) {

											// Reset Rates.
											$shipping_method->rates = array();

											// Calculate Shipping for package.
											$shipping_method->calculate_shipping( $package );

											// Place rates in package array.
											if ( ! empty( $shipping_method->rates ) && is_array( $shipping_method->rates ) ) {
												foreach ( $shipping_method->rates as $rate ) {
													$ship_package['rates'][ $rate->id ] = $rate;
												}
											}
										}
									}

									foreach ( $ship_package['rates'] as $method ) {
										if ( 'multiple_shipping' === $method->id ) {
											continue;
										}

										$method->label = esc_html( $method->label );

										if ( $method->cost > 0 ) {
											$shipping_tax   = $method->get_shipping_tax();
											$method->label .= ' &mdash; ';

											// Append price to label using the correct tax settings.
											if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {

												if ( $shipping_tax > 0 ) {
													if ( WC()->cart->prices_include_tax ) {
														$method->label .= wc_price( $method->cost ) . ' ' . WC()->countries->ex_tax_or_vat();
													} else {
														$method->label .= wc_price( $method->cost );
													}
												} else {
													$method->label .= wc_price( $method->cost );
												}
											} else {
												$method->label .= wc_price( $method->cost + $shipping_tax );
												if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
													$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
												}
											}
										}

										$shipping_methods[] = $method;
									}

									// Print a single available shipping method as plain text.
									if ( 1 === count( $shipping_methods ) ) {
										$method = $shipping_methods[0];

										echo esc_html( $method->label );
										// no need to escape. it's already using `strip_tags`.
										echo '<input type="hidden" class="shipping_methods shipping_method" name="'. $field_name .'['. $x .']" value="'.esc_attr( $method->id ).'||'. strip_tags($method->label) .'">';//phpcs:ignore

										// Show multiple shipping methods in a select list.
									} elseif ( count( $shipping_methods ) > 1 ) {
										echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';
										foreach ( $shipping_methods as $method ) {
											if ( 'multiple_shipping' === $method->id ) {
												continue;
											}
											$current_selected = ( isset( $selected[ $x ] ) ) ? $selected[ $x ]['id'] : '';

											// no need to escape. it's already using `strip_tags`.
											echo '<option value="'.esc_attr( $method->id ).'||'. strip_tags($method->label) .'" '.selected( $current_selected, $method->id, false).'>';//phpcs:ignore

											echo wp_kses_post( wc_cart_totals_shipping_method_label( $method ) );

											echo '</option>';
										}
										echo '</select>';
									} else {
										echo '<p>' . esc_html__( '(2) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
									}

									$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
									if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
										echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									}
									?>
								</div>
								<?php
							endif;

							$all_shippings = array();
							foreach ( $shipping_methods as $shipping_method ) {
								if ( ! array_key_exists( $shipping_method->get_id(), $all_shippings ) ) {
									$all_shippings[ $shipping_method->get_id() ] = array(
										'id'    => $shipping_method->get_id(),
										'label' => $shipping_method->get_label(),
									);
								}
							}

						endforeach;
						?>
						<div style="clear:both;"></div>

						<input type="hidden" name="all_shipping_methods" value="<?php echo ! empty( $all_shippings ) ? esc_attr( wp_json_encode( $all_shippings ) ) : ''; ?>" />
					</div>

				</td>
				<td style="vertical-align: top;">
					<?php
					$shipping_total = WC()->cart->shipping_total;
					$shipping_tax   = WC()->cart->shipping_tax_total;
					$inc_or_exc_tax = '';

					if ( $shipping_total > 0 && wc_tax_enabled() ) {

						// Append price to label using the correct tax settings.
						if ( ! WC()->cart->display_totals_ex_tax ) {
							$shipping_total += $shipping_tax;

							if ( 0 < $shipping_tax ) {
								$inc_or_exc_tax = WC()->countries->inc_tax_or_vat();
							}
						}
					}
					// no need to escape. It has been filtered by `wc_price`.
					echo wc_price( $shipping_total ) . ' ' . esc_html( $inc_or_exc_tax ); //phpcs:ignore
					?>
				</td>
				<script type="text/javascript">
					jQuery(document).ready(function() {
						jQuery("tr.shipping").remove();
					});
					<?php
					if ( null === wcms_session_get( 'shipping_methods' ) && $rates_available ) {
						echo 'jQuery("body").trigger("update_checkout");';
					}
					?>
				</script>
			</tr>
			<?php
		else :
			?>
			<tr class="multi_shipping">
				<td style="vertical-align: top;" colspan="1">
					<?php esc_html_e( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' ); ?>

					<?php
					foreach ( $packages as $x => $package ) :
						$shipping_methods = array();
						$products         = $package['contents'];

						if ( 2 === $type ) :
							// If at least one shipping method is available.
							// Calculate shipping method rates.
							$ship_package['rates'] = array();

							foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

								if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
									continue;
								}

								if ( $shipping_method->is_available( $package ) ) {

									// Reset Rates.
									$shipping_method->rates = array();

									// Calculate Shipping for package.
									$shipping_method->calculate_shipping( $package );

									// Place rates in package array.
									if ( ! empty( $shipping_method->rates ) && is_array( $shipping_method->rates ) ) {
										foreach ( $shipping_method->rates as $rate ) {
											$ship_package['rates'][ $rate->id ] = $rate;
										}
									}
								}
							}

							foreach ( $ship_package['rates'] as $method ) {
								if ( 'multiple_shipping' === $method->id ) {
									continue;
								}

								$method->label = esc_html( $method->label );

								if ( $method->cost > 0 ) {
									$method->label .= ' &mdash; ';

									// Append price to label using the correct tax settings.
									if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {
										$method->label .= wc_price( $method->cost );
										if ( $method->get_shipping_tax() > 0 && WC()->cart->prices_include_tax ) {
											$method->label .= ' ' . WC()->countries->ex_tax_or_vat();
										}
									} else {
										$method->label .= wc_price( $method->cost + $method->get_shipping_tax() );
										if ( $method->get_shipping_tax() > 0 && ! WC()->cart->prices_include_tax ) {
											$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
										}
									}
								}
								$shipping_methods[] = $method;
							}

							// Print a single available shipping method as plain text.
							if ( 1 === count( $shipping_methods ) ) {
								$method = $shipping_methods[0];
								echo esc_html( $method->label );
								echo '<input type="hidden" class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '" value="' . esc_attr( $method->id ) . '">';

								// Show multiple shipping methods in a select list.
							} elseif ( count( $shipping_methods ) > 1 ) {
								echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';
								foreach ( $shipping_methods as $method ) {
									if ( 'multiple_shipping' === $method->id ) {
										continue;
									}
									echo '<option value="' . esc_attr( $method->id ) . '" ' . selected( $method->id, ( isset( $post['shipping_method'] ) ) ? $post['shipping_method'] : '', false ) . '>';
									echo esc_html( $method->label );
									echo '</option>';
								}
								echo '</select>';
							} else {
								echo '<p>' . esc_html__( '(3) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
							}

							$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
							if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
								echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
							}
						endif;
					endforeach;
					?>
				</td>
				<?php // no need to escape. It has been filtered from `wc_price`. ?>
				<td style="vertical-align: top;"><?php echo wc_price( WC()->cart->shipping_total + WC()->cart->shipping_tax_total ); ?></td><?php // phpcs:ignore ?>
				<script type="text/javascript">
					jQuery("tr.shipping").remove();
					<?php
					if ( null === wcms_session_get( 'shipping_methods' ) && $rates_available ) {
						echo 'jQuery("body").trigger("update_checkout");';
					}
					?>
				</script>
			</tr>
			<?php
		endif;
	}

	/**
	 * Get available shipping methods based on the current cart packages.
	 *
	 * @return array Available shipping methods.
	 */
	public function get_available_shipping_methods() {

		$packages = WC()->cart->get_shipping_packages();

		// Loop packages and merge rates to get a total for each shipping method.
		$available_methods = array();

		foreach ( $packages as $package ) {
			if ( ! isset( $package['rates'] ) || ! $package['rates'] ) {
				continue;
			}

			foreach ( $package['rates'] as $id => $rate ) {

				if ( isset( $available_methods[ $id ] ) ) {
					// Merge cost and taxes - label and ID will be the same.
					$available_methods[ $id ]->cost += $rate->cost;

					foreach ( array_keys( $available_methods[ $id ]->taxes + $rate->taxes ) as $key ) {
						$available_methods[ $id ]->taxes[ $key ] = ( isset( $rate->taxes[ $key ] ) ? $rate->taxes[ $key ] : 0 ) + ( isset( $available_methods[ $id ]->taxes[ $key ] ) ? $available_methods[ $id ]->taxes[ $key ] : 0 );
					}
				} else {
					$available_methods[ $id ] = $rate;
				}
			}
		}

		/**
		 * Filter to manipulate the available shipping methods.
		 *
		 * @param array $available_methods Available shipping methods.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wcms_available_shipping_methods', $available_methods );
	}

	/**
	 * Clear sessions and transients from the saved packages.
	 *
	 * @param int|WC_Order|string $order_obj Order ID or Order Object.
	 */
	public function clear_session( $order_obj = '' ) {
		$order = ( $order_obj instanceof WC_Order ) ? $order_obj : wc_get_order( $order_obj );

		if ( $order instanceof WC_Order && in_array( $order->get_status(), array( 'pending', 'failed' ), true ) ) {
			return;
		}

		$packages = wcms_session_get( 'wcms_packages' );

		// Clear packages transient.
		if ( is_array( $packages ) ) {
			foreach ( $packages as $package ) {
				$package_hash = 'wc_ship_' . md5( wp_json_encode( $package ) );
				delete_transient( $package_hash );
			}
		}

		wcms_session_delete( 'cart_item_addresses' );
		wcms_session_delete( 'wcms_item_addresses' );
		wcms_session_delete( 'cart_address_sigs' );
		wcms_session_delete( 'address_relationships' );
		wcms_session_delete( 'shipping_methods' );
		wcms_session_delete( 'wcms_original_cart' );
		wcms_session_delete( 'wcms_packages' );
		wcms_session_delete( 'wcms_packages_after_tax_calc' );
		wcms_session_delete( 'wcms_item_delivery_dates' );
		wcms_session_delete( 'user_default_address' );
		wcms_session_delete( 'wcms_package_notes' );
		wcms_session_delete( 'wcms_delivery_dates' );
		wcms_session_delete( 'wcms_package_gifts' );

		/**
		 * Action after session is cleared.
		 *
		 * @since 3.1
		 */
		do_action( 'wc_ms_cleared_session' );
	}

	/**
	 * Get shipping rate from the cart package.
	 *
	 * @param array $package Cart package.
	 */
	public function get_package_shipping_rates( $package = array() ) {

		$_tax = new WC_Tax();

		// See if we have an explicitly set shipping tax class.
		$shipping_tax_class = get_option( 'woocommerce_shipping_tax_class' );
		if ( $shipping_tax_class ) {
			$tax_class = 'standard' === $shipping_tax_class ? '' : $shipping_tax_class;
		}

		if ( ! empty( $package['destination'] ) ) {
			$country  = $package['destination']['country'];
			$state    = $package['destination']['state'];
			$postcode = $package['destination']['postcode'];
			$city     = $package['destination']['city'];

			// Prices which include tax should always use the base rate if we don't know where the user is located
			// Prices excluding tax however should just not add any taxes, as they will be added during checkout.
		} elseif ( 'yes' === get_option( 'wc_prices_include_tax' ) || 'base' === get_option( 'woocommerce_default_customer_address' ) ) {
				$country  = WC()->countries->get_base_country();
				$state    = WC()->countries->get_base_state();
				$postcode = '';
				$city     = '';
		} else {
				return array();
		}

		// If we are here then shipping is taxable - work it out.
		// This will be per order shipping - loop through the order and find the highest tax class rate.
		$found_tax_classes = array();
		$matched_tax_rates = array();
		$rates             = false;

		// Loop cart and find the highest tax band.
		if ( count( WC()->cart->get_cart() ) > 0 ) {
			foreach ( WC()->cart->get_cart() as $item ) {
				$found_tax_classes[] = $item['data']->get_tax_class();
			}
		}

		$found_tax_classes = array_unique( $found_tax_classes );

		// If multiple classes are found, use highest.
		if ( count( $found_tax_classes ) > 1 ) {
			if ( in_array( '', $found_tax_classes, true ) ) {
				$rates = $_tax->find_rates(
					array(
						'country'  => $country,
						'state'    => $state,
						'city'     => $city,
						'postcode' => $postcode,
					)
				);
			} else {
				$tax_classes = array_filter( array_map( 'trim', explode( "\n", get_option( 'woocommerce_tax_classes' ) ) ) );

				foreach ( $tax_classes as $tax_class ) {
					if ( in_array( $tax_class, $found_tax_classes, true ) ) {
						$rates = $_tax->find_rates(
							array(
								'country'   => $country,
								'state'     => $state,
								'postcode'  => $postcode,
								'city'      => $city,
								'tax_class' => $tax_class,
							)
						);
						break;
					}
				}
			}

			// If a single tax class is found, use it.
		} elseif ( 1 === count( $found_tax_classes ) ) {

			$rates = $_tax->find_rates(
				array(
					'country'   => $country,
					'state'     => $state,
					'postcode'  => $postcode,
					'city'      => $city,
					'tax_class' => $found_tax_classes[0],
				)
			);

		}

		// If no class rate are found, use standard rates.
		if ( ! $rates ) {
			$rates = $_tax->find_rates(
				array(
					'country'  => $country,
					'state'    => $state,
					'postcode' => $postcode,
					'city'     => $city,
				)
			);
		}

		if ( $rates ) {
			foreach ( $rates as $key => $rate ) {
				if ( isset( $rate['shipping'] ) && 'yes' === $rate['shipping'] ) {
					$matched_tax_rates[ $key ] = $rate;
				}
			}
		}

		return $matched_tax_rates;
	}

	/**
	 * Get cart item subtotal.
	 *
	 * @param array $cart_item Cart item.
	 *
	 * @return float.
	 */
	public function get_cart_item_subtotal( $cart_item ) {

		$_product = $cart_item['data'];
		$quantity = $cart_item['quantity'];

		$price   = $_product->get_price();
		$taxable = $_product->is_taxable();

		if ( $taxable ) {
			if ( 'excl' === WC()->cart->get_tax_price_display_mode() ) {
				$row_price = wc_get_price_excluding_tax( $_product, array( 'qty' => $quantity ) );
			} else {
				$row_price = wc_get_price_including_tax( $_product, array( 'qty' => $quantity ) );
			}

			// Non-taxable.
		} else {
			$row_price = $price * $quantity;
		}

		return $row_price;
	}

	/**
	 * Get product shipping method based on product ID.
	 *
	 * @param int $product_id Product ID.
	 *
	 * @return bool|string
	 */
	public function get_product_shipping_method( $product_id ) {
		$settings     = $this->settings;
		$product_cats = wp_get_post_terms( $product_id, 'product_cat', array( 'fields' => 'ids' ) );

		// Look for direct product matches.
		foreach ( $settings as $idx => $setting ) {
			$products = is_array( $setting['products'] ) ? array_map( 'intval', $setting['products'] ) : array( intval( $setting['products'] ) );
			if ( in_array( $product_id, $products, true ) ) {
				return $setting['method'];
			}
		}

		// Look for category matches.
		foreach ( $settings as $idx => $setting ) {
			$categories = array_map( 'intval', $setting['categories'] );

			foreach ( $product_cats as $product_cat_id ) {
				if ( in_array( intval( $product_cat_id ), $categories, true ) ) {
					return $setting['method'];
				}
			}
		}

		return false;
	}

	/**
	 * Check if the package has different methods.
	 *
	 * @param array $packages Cart package.
	 *
	 * @return boolean.
	 */
	public function packages_have_different_methods( $packages = array() ) {
		$last_method = false;
		$_return     = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['method'] ) ) {
				if ( ! $last_method ) {
					$last_method = $package['method'];
				} elseif ( $last_method !== $package['method'] ) {
					$_return = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate the packages value that has different methods.
		 *
		 * @param array $return packages that has different methods.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_have_different_methods', $_return, $packages );
	}

	/**
	 * Check if the package has a different origin.
	 *
	 * @param array $packages Cart package.
	 *
	 * @return boolean
	 */
	public function packages_have_different_origins( $packages = array() ) {
		$last_origin = false;
		$_return     = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['origin'] ) ) {
				if ( ! $last_origin ) {
					$last_origin = $package['origin'];
				} elseif ( $last_origin !== $package['origin'] ) {
					$_return = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate the packages value that has different origins.
		 *
		 * @param array $return packages that has different origins.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_have_different_origins', $_return, $packages );
	}

	/**
	 * Check if the packages contain any shipping method.
	 *
	 * @param array $packages Cart package.
	 */
	public function packages_contain_methods( $packages = array() ) {
		$return = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['method'] ) ) {
				$return = true;
				break;
			}
		}

		/**
		 * Filter to manipulate the packages value that has method.
		 *
		 * @param array $return packages that has method.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_contain_methods', $return, $packages );
	}

	/**
	 * Clear current package cache.
	 */
	public function clear_packages_cache() {

		WC()->cart->calculate_totals();
		$packages = WC()->cart->get_shipping_packages();

		foreach ( $packages as $idx => $package ) {
			$package_hash = 'wc_ship_' . md5( wp_json_encode( $package ) );
			delete_transient( $package_hash );
		}
	}

	/**
	 * Check if address is empty or not.
	 *
	 * @param array $address_array Address data.
	 */
	public function is_address_empty( $address_array ) {
		if ( empty( $address_array['country'] ) ) {
			return true;
		}

		$address_fields = WC()->countries->get_address_fields( $address_array['country'], 'shipping_' );

		foreach ( $address_fields as $key => $field ) {
			$key = str_replace( 'shipping_', '', $key );

			if ( isset( $field['required'] ) && $field['required'] && empty( $address_array[ $key ] ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Generate session for cart item and addresses.
	 *
	 * @param array $packages Cart package.
	 */
	public function generate_address_session( $packages ) {

		$fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );
		$data   = array();
		$rel    = array();

		foreach ( $packages as $pkg_idx => $package ) {

			if (
				! isset( $package['destination'] ) ||
				empty( $package['destination']['postcode'] ) ||
				empty( $package['destination']['country'] )
			) {
				continue;
			}

			$items = $package['contents'];

			foreach ( $items as $cart_key => $item ) {

				$qty = $item['quantity'];

				$product_id = $item['product_id'];
				$sig        = $cart_key . '_' . $product_id . '_';
				$address_id = 0;

				$i = 1;
				for ( $x = 0; $x < $qty; $x++ ) {
					$rel[ $address_id ][] = $cart_key;

					while ( isset( $data[ 'shipping_first_name_' . $sig . $i ] ) ) {
						++$i;
					}
					$_sig = $sig . $i;

					if ( $fields ) {
						foreach ( $fields as $key => $field ) {
							$address_key                = str_replace( 'shipping_', '', $key );
							$data[ $key . '_' . $_sig ] = $package['destination'][ $address_key ];
						}
					}
				}
			}
		}

		wcms_session_set( 'cart_item_addresses', $data );
		wcms_session_set( 'address_relationships', $rel );
	}

	/**
	 * Register blocks integration.
	 */
	public function register_blocks_integration() {
		require_once WC_MS_ABSPATH . 'includes/class-blocks-integration.php';
		add_action(
			'woocommerce_blocks_checkout_block_registration',
			function ( $integration_registry ) {
				$integration_registry->register( new Blocks_Integration() );
			}
		);
	}

	/**
	 * Extend the store API.
	 */
	public function extend_store_api() {
		require_once WC_MS_ABSPATH . 'includes/class-store-api-extension.php';
		Store_API_Extension::init();
	}
}
