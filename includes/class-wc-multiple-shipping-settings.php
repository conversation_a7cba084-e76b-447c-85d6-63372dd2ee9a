<?php
/**
 * Class WC_Multiple_Shipping_Settings file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_Multiple_Shipping_Settings class.
 *
 * @extends WC_Shipping_Method
 */
class WC_Multiple_Shipping_Settings extends WC_Shipping_Method {

	/**
	 * Is cart duplication enabled?
	 *
	 * @var string.
	 */
	public $cart_duplication;

	/**
	 * Checkout notification value.
	 *
	 * @var string.
	 */
	public $lang_notification;

	/**
	 * Text for multiple address button.
	 *
	 * @var string.
	 */
	public $lang_btn_items;

	/**
	 * Class constructor.
	 */
	public function __construct() {
		$this->id                 = 'multiple_shipping';
		$this->method_title       = __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' );
		$this->method_description = __( 'Multiple Shipping is used automatically by the WooCommerce Ship to Multiple Addresses.', 'woocommerce-shipping-multiple-addresses' );
		$this->init();
	}

	/**
	 * Initiate the action and filter.
	 */
	public function init() {
		// Load the form fields.
		$this->init_form_fields();

		// Load the settings.
		$this->init_settings();

		// Define user set variables.
		$this->enabled           = 'yes';
		$this->title             = $this->settings['title'];
		$this->cart_duplication  = $this->settings['cart_duplication'];
		$this->lang_notification = $this->settings['lang_notification'];
		$this->lang_btn_items    = $this->settings['lang_btn_items'];

		add_action( 'woocommerce_update_options_shipping_multiple_shipping', array( $this, 'process_admin_options' ) );
		add_filter( 'woocommerce_settings_api_sanitized_fields_' . $this->id, array( $this, 'save_settings' ) );
	}

	/**
	 * Do nothing when calculate shipping.
	 *
	 * @param array $package Current cart package.
	 */
	public function calculate_shipping( $package = array() ) {
		// Do nothing here.
	}

	/**
	 * Listing the form fields.
	 */
	public function init_form_fields() {
		$this->form_fields = array(
			'title'                    => array(
				'title'       => __( 'Title', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'text',
				'description' => __( 'This controls the title which the user sees during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'default'     => __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' ),
			),
			'cart_duplication_section' => array(
				'type'        => 'title',
				'title'       => __( 'Cart Duplication', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'This functionality will allow your customers to duplicate the contents of their cart in order to be able to ship the same cart to multiple addresses in addition to individual products.', 'woocommerce-shipping-multiple-addresses' ),
			),
			'cart_duplication'         => array(
				'title' => __( 'Enable Cart Duplication', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'checkbox',
				'label' => 'Enable',
			),
			'checkout_section'         => array(
				'type'  => 'title',
				'title' => __( 'Checkout Fields', 'woocommerce-shipping-multiple-addresses' ),
			),
			'checkout_notes'           => array(
				'type'        => 'checkbox',
				'title'       => __( 'Delivery Notes', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'default'     => 'yes',
				'description' => __( 'Allow customers to write delivery notes to every shipping address selected.', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'checkout_notes_limit'     => array(
				'type'        => 'text',
				'title'       => __( 'Limit Character Input', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Characters', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Limit the character input for delivery notes. Leave the box empty to disable the limit.', 'woocommerce-shipping-multiple-addresses' ),
				'css'         => 'width: 100px',
				'desc_tip'    => true,
			),
			'checkout_datepicker'      => array(
				'type'        => 'checkbox',
				'title'       => __( 'Date Picker', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Allow customers to pick delivery dates for every shipping address selected.', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'checkout_valid_days'      => array(
				'type'        => 'multiselect',
				'title'       => __( 'Valid Shipping Days', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Days', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Days that customers can select for the "Shipping Date" during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'options'     => array(
					0 => __( 'Sunday', 'woocommerce-shipping-multiple-addresses' ),
					1 => __( 'Monday', 'woocommerce-shipping-multiple-addresses' ),
					2 => __( 'Tuesday', 'woocommerce-shipping-multiple-addresses' ),
					3 => __( 'Wednesday', 'woocommerce-shipping-multiple-addresses' ),
					4 => __( 'Thursday', 'woocommerce-shipping-multiple-addresses' ),
					5 => __( 'Friday', 'woocommerce-shipping-multiple-addresses' ),
					6 => __( 'Saturday', 'woocommerce-shipping-multiple-addresses' ),
				),
				'default'     => array( 0, 1, 2, 3, 4, 5, 6 ),
				'class'       => 'show-if-checkout-datepicker wc-enhanced-select',
				'desc_tip'    => true,
			),
			'checkout_exclude_dates'   => array(
				'type'        => 'ms_multi_datepicker',
				'title'       => __( 'Excluded Delivery Dates', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Excluded Dates', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Specific dates that customers cannnot select for the "Shipping Date" during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'class'       => 'show-if-checkout-datepicker',
				'desc_tip'    => true,
			),
			'gift_section'             => array(
				'type'        => 'title',
				'title'       => __( 'Gift Packages', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Allow customers to mark certain shipping packages as gifts', 'woocommerce-shipping-multiple-addresses' ),
			),
			'gift_packages'            => array(
				'title' => __( 'Enable Gift Packages', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'checkbox',
				'label' => 'Enable',
			),
			'exclusions'               => array(
				'type'        => 'title',
				'title'       => __( 'Excluded Products &amp; Categories', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Do not allow multiple shipping addresses when any of the products and categories below are in the cart', 'woocommerce-shipping-multiple-addresses' ),
			),
			'excluded_products'        => array(
				'title' => __( 'Products', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'ms_product_select',
			),
			'excluded_categories'      => array(
				'title' => __( 'Categories', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'ms_category_select',
			),
			'language_section'         => array(
				'type'  => 'title',
				'title' => __( 'Text your shoppers see when Multiple Shipping is enabled at checkout', 'woocommerce-shipping-multiple-addresses' ),
			),
			'lang_notification'        => array(
				'type'    => 'text',
				'title'   => __( 'Checkout Notification', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'You may use multiple shipping addresses on this cart', 'woocommerce-shipping-multiple-addresses' ),
			),
			'lang_btn_items'           => array(
				'type'    => 'text',
				'title'   => __( 'Button: Item Addresses', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'Set Multiple Addresses', 'woocommerce-shipping-multiple-addresses' ),
			),
			'partial_orders'           => array(
				'title'       => __( 'Partially Complete Orders', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'checkbox',
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Partially complete order by shipping address', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'email_section'            => array(
				'type'  => 'title',
				'title' => __( 'Partial Order Completed Email', 'woocommerce-shipping-multiple-addresses' ),
			),
			'partial_orders_email'     => array(
				'title'       => __( 'Send Email', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'checkbox',
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Send an email when an order has been marked as partially complete', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'email_subject'            => array(
				'type'    => 'text',
				'title'   => __( 'Subject', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'Part of your order has been shipped', 'woocommerce-shipping-multiple-addresses' ),
			),
			'email_message'            => array(
				'type'        => 'ms_wp_editor',
				'description' => __( 'Leave empty to use the default email message', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
				'title'       => __( 'Message', 'woocommerce-shipping-multiple-addresses' ),
				'css'         => 'width: 350px;',
				'default'     => sprintf(
					// translators: %1$s is a blog name.
					__( '<p>Hi there. Part of your recent order on %1$s has been completed. Your order details are shown below for your reference:</p><h2>Order: {order_id}</h2><br />{products_table}<br />{addresses_table}', 'woocommerce-shipping-multiple-addresses' ),
					get_option( 'blogname' )
				),
			),
		);
	}

	/**
	 * Check if current package is available for multiple address.
	 *
	 * @param mixed $package Cart package.
	 * @return boolean
	 */
	public function is_available( $package ) {
		$packages = WC()->cart->get_shipping_packages();

		if ( ! empty( $packages ) && count( $packages ) > 1 ) {
			return true;
		}

		return false;
	}

	/**
	 * Generate WP editor field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_wp_editor_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$html     = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();
		$editor            = '';
		$content           = ( isset( $settings['email_message'] ) ) ? $settings['email_message'] : $data['default'];

		ob_start();
		wp_editor( $content, esc_attr( $this->plugin_id . $this->id . '_' . $key ) );
		$editor = ob_get_clean();

		$html     .= '<tr valign="top">' . "\n";
			$html .= '<th scope="row" class="titledesc">';
			$html .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

			$html .= '<br/><br/><div class="vars-box">
			<strong>Available Variables</strong><br/>
			<em>{order_id}</em><br/>
			<em>{order_date}</em><br/>
			<em>{order_time}</em><br/>
			<em>{products_table}</em><br/>
			<em>{addresses_table}</em>
			</div>';

			$html     .= '</th>' . "\n";
			$html     .= '<td class="forminp">' . "\n";
				$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";
				$html .= $editor;

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

			$html .= '</fieldset>';
			$html .= '</td>' . "\n";
		$html     .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate product field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_product_select_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$html = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();

		if ( ! isset( $settings['excluded_products'] ) ) {
			$settings['excluded_products'] = array();
		}

		$product_ids = array_filter( array_map( 'absint', $settings['excluded_products'] ) );

		$html .= '<tr valign="top">' . "\n";
		$html .= '<th scope="row" class="titledesc">';
		$html .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

		$html .= '</th>' . "\n";
		$html .= '<td class="forminp">' . "\n";
		$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";

		$html .= '<select
						multiple="multiple"
						id="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '"
						name="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '[]"
						class="wcms-product-search"
						data-placeholder="' . esc_attr__( 'Search for a product&hellip;', 'woocommerce-shipping-multiple-addresses' ) . '"
						style="width: 400px"
					>' . "\n";

		foreach ( $product_ids as $product_id ) {
			$product      = wc_get_product( $product_id );
			$product_name = $product ? htmlspecialchars( wp_kses_post( $product->get_formatted_name() ), ENT_COMPAT ) : '';

			$html .= '<option value="' . esc_attr( $product_id ) . '" selected="selected">' . esc_html( $product_name ) . '</option>' . "\n";
		}

		$html .= '</select>' . "\n";

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

		$html .= '</fieldset>';
		$html .= '</td>' . "\n";
		$html .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate category field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_category_select_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$html = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();
		$categories        = get_terms(
			array(
				'taxonomy' => 'product_cat',
				'order_by' => 'name',
				'order'    => 'ASC',
			)
		);
		$html             .= '<tr valign="top">' . "\n";
			$html         .= '<th scope="row" class="titledesc">';
			$html         .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

			$html     .= '</th>' . "\n";
			$html     .= '<td class="forminp">' . "\n";
				$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";
				$html .= '<select multiple="multiple" id="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '" name="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '[]" class="chosen_select " multiple data-placeholder="' . __( 'Select categories&hellip;', 'woocommerce-shipping-multiple-addresses' ) . '" style="width: 400px">';

		foreach ( $categories as $category ) {
			$selected = ( isset( $settings['excluded_categories'] ) && is_array( $settings['excluded_categories'] ) && in_array( $category->term_id, array_map( 'intval', $settings['excluded_categories'] ), true ) ) ? 'selected' : '';
			$html    .= '<option value="' . $category->term_id . '" ' . $selected . '>' . esc_html( $category->name ) . '</option>';
		}
				$html .= '</select>';

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

			$html .= '</fieldset>';
			$html .= '</td>' . "\n";
		$html     .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate multi datepicker HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_multi_datepicker_html( $key, $data ) {
		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		ob_start();
		?>
		<tr valign="top">
			<th scope="row" class="titledesc">
				<label for="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>"><?php echo wp_kses_post( $data['title'] ); ?></label>

				<?php
				if ( $tip ) {
					echo '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . esc_url( WC()->plugin_url() . '/assets/images/help.png' ) . '" height="16" width="16" />';
				}
				?>
			</th>
			<td class="forminp">
				<fieldset><legend class="screen-reader-text"><span><?php echo wp_kses_post( $data['title'] ); ?></span></legend>
					<?php
					$excludes = isset( $settings['checkout_exclude_dates'] ) ? $settings['checkout_exclude_dates'] : array();

					if ( ! $excludes ) {
						$excludes = array();
					}
					?>

					<div class="datepicker-div" style="float: left; width: 350px;"></div>

					<div style="float: left; width: 350px;">
						<div style="display: inline-block; width: 300px;">
							<select class="wc-enhanced-select excluded-list show-if-checkout-datepicker" id="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>" name="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>[]" multiple>
								<?php foreach ( $excludes as $date ) : ?>
									<option selected value="<?php echo esc_attr( $date ); ?>"><?php echo esc_html( $date ); ?></option>
								<?php endforeach; ?>
							</select>
						</div>
						<button class="button" type="button" id="show_excluded_dates_calendar"><span class="dashicons dashicons-calendar-alt" style="line-height: inherit;"></span></button>
						<button class="button" type="button" id="hide_excluded_dates_calendar" style="display: none;"><span class="dashicons dashicons-yes" style="line-height: inherit;"></span></button>
					</div>
				</fieldset>
			</td>
		</tr>
		<?php
		return ob_get_clean();
	}

	/**
	 * Save settings.
	 *
	 * @param array $settings Setting values.
	 *
	 * @return array.
	 */
	public function save_settings( $settings ) {
		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Nonce verification is handled in WC
		$settings['email_subject']       = isset( $_POST['woocommerce_multiple_shipping_email_subject'] ) ? sanitize_text_field( wp_unslash( $_POST['woocommerce_multiple_shipping_email_subject'] ) ) : '';
		$settings['email_message']       = isset( $_POST['woocommerce_multiple_shipping_email_message'] ) ? sanitize_textarea_field( wp_unslash( $_POST['woocommerce_multiple_shipping_email_message'] ) ) : '';
		$settings['excluded_categories'] = isset( $_POST['woocommerce_multiple_shipping_excluded_categories'] ) ? array_map( 'absint', $_POST['woocommerce_multiple_shipping_excluded_categories'] ) : array();

		$products = array();
		$key      = 'woocommerce_multiple_shipping_excluded_products';
		if ( ! empty( $_POST[ $key ] ) ) {
			$values = wc_clean( wp_unslash( $_POST[ $key ] ) );
			if ( ! empty( $values[0] ) && strpos( $values[0], ',' ) !== false ) {
				$products = explode( ',', $values[0] );
			} else {
				$products = array_map( 'absint', $values );
			}
		}
		$settings['excluded_products'] = $products;

		return $settings;
		// phpcs:enable WordPress.Security.NonceVerification.Missing
	}

	/**
	 * Validating the product field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_product_select_field( $key ) {
		$text = $this->get_option( $key );
		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$text = $new;
		}
		// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}

	/**
	 * Validating the category field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_category_select_field( $key ) {
		$text = $this->get_option( $key );

		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$text = $new;
		}
			// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}

	/**
	 * Validating the multi datepicker field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_multi_datepicker_field( $key ) {
		$text = $this->get_option( $key );

		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$new = array_unique( $new );

			$text = $new;
		} else {
			$text = array();
		}
		// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}
}
