<?php
/**
 * Functions Collection.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Get Cheapest shipping rate.
 *
 * @param array $rates Shipping rates.
 *
 * @return array Cheapest shipping rate.
 */
function wcms_get_cheapest_shipping_rate( $rates = array() ) {
	$cheapest  = false;
	$last_cost = false;

	foreach ( $rates as $rate ) {
		if ( false === $last_cost ) {
			$last_cost = $rate->cost;
			$cheapest  = $rate;
			continue;
		}

		if ( $rate->cost < $last_cost ) {
			$last_cost = $rate->cost;
			$cheapest  = $rate;
		}
	}

	if ( $cheapest ) {
		$cheapest = (array) $cheapest;
	}

	return $cheapest;
}

/**
 * Get address.
 *
 * @param array $address Destination address.
 *
 * @return array Shipping address.
 */
function wcms_get_address( $address ) {
	foreach ( $address as $key => $value ) {
		if ( strpos( $key, 'shipping_' ) === false ) {
			$address[ 'shipping_' . $key ] = $value;
		}

		$addr_key             = str_replace( 'shipping_', '', $key );
		$address[ $addr_key ] = $value;
	}

	return $address;
}

/**
 * Get formatted address.
 *
 * @param array $address Shipping Address.
 *
 * @return string Formatted address.
 */
function wcms_get_formatted_address( $address ) {
	$address = wcms_get_address( $address );

	/**
	 * Allow modifying the formatted address.
	 *
	 * @param string $formatted_address Formatted address.
	 * @param array  $address           Shipping address.
	 * @since 3.3
	 */
	return apply_filters( 'wc_ms_formatted_address', WC()->countries->get_formatted_address( $address ), $address );
}

/**
 * Get real cart items count.
 *
 * @return int Cart items count.
 */
function wcms_count_real_cart_items() {

	$count = 0;

	foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {

		if ( ! $cart_item['data']->needs_shipping() ) {
			continue;
		}

		if ( isset( $cart_item['bundled_by'] ) && ! empty( $cart_item['bundled_by'] ) ) {
			continue;
		}

		if ( isset( $cart_item['composite_parent'] ) && ! empty( $cart_item['composite_parent'] ) ) {
			continue;
		}

		++$count;
	}

	return $count;
}

/**
 * Get real cart items.
 *
 * @return array Cart items.
 */
function wcms_get_real_cart_items() {

	$items = array();

	foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {

		if ( ! $cart_item['data']->needs_shipping() ) {
			continue;
		}

		if ( isset( $cart_item['bundled_by'] ) && ! empty( $cart_item['bundled_by'] ) ) {
			continue;
		}

		if ( isset( $cart_item['composite_parent'] ) && ! empty( $cart_item['composite_parent'] ) ) {
			continue;
		}

		$items[ $cart_item_key ] = $cart_item;
	}

	return $items;
}

/**
 * Get Session.
 *
 * @param string $name Session name.
 *
 * @return mixed Session.
 */
function wcms_session_get( $name ) {
	if ( isset( WC()->session->$name ) ) {
		return WC()->session->$name;
	}

	return null;
}

/**
 * Check if session isset.
 *
 * @param string $name Session name.
 *
 * @return bool True if isset.
 */
function wcms_session_isset( $name ) {
	return ( isset( WC()->session->$name ) );
}

/**
 * Set session.
 *
 * @param string $name Session name.
 * @param string $value Session value.
 *
 * @return void
 */
function wcms_session_set( $name, $value ) {
	unset( WC()->session->$name );
	WC()->session->$name = $value;
}

/**
 * Delete a session.
 *
 * @param string $name Session name.
 *
 * @return void
 */
function wcms_session_delete( $name ) {
	unset( WC()->session->$name );
}
