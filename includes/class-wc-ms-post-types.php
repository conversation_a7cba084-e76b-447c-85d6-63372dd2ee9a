<?php
/**
 * Class WC_MS_Post_Types file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class WC_MS_Post_Types.
 */
class WC_MS_Post_Types {

	/**
	 * Initiating.
	 */
	public static function init() {
		add_action( 'init', array( __CLASS__, 'register_post_types' ), 10 );
	}

	/**
	 * Register the post type for order shipment.
	 */
	public static function register_post_types() {
		if ( post_type_exists( 'order_shipment' ) ) {
			return;
		}

		wc_register_order_type(
			'order_shipment',
			/**
			 * Filter to manipulate the order shipment post type args.
			 *
			 * @param array Post type args.
			 *
			 * @since 3.3
			 */
			apply_filters(
				'wc_ms_register_post_type_order_shipment',
				array(
					'label'                            => __( 'Order Shipments', 'woocommerce-shipping-multiple-addresses' ),
					'description'                      => __( 'This is where store order shipments are stored.', 'woocommerce-shipping-multiple-addresses' ),
					'public'                           => false,
					'show_ui'                          => true,
					'capability_type'                  => 'shop_order',
					'map_meta_cap'                     => true,
					'publicly_queryable'               => false,
					'exclude_from_search'              => true,
					'show_in_menu'                     => false,
					'hierarchical'                     => false,
					'show_in_nav_menus'                => false,
					'rewrite'                          => false,
					'query_var'                        => false,
					'supports'                         => array( 'title', 'comments', 'custom-fields' ),
					'has_archive'                      => false,
					'exclude_from_orders_screen'       => true,
					'add_order_meta_boxes'             => false,
					'exclude_from_order_count'         => true,
					'exclude_from_order_views'         => true,
					'exclude_from_order_reports'       => true,
					'exclude_from_order_sales_reports' => true,
					'class_name'                       => 'WC_MS_Order_Type_Order_Shipment',
				)
			)
		);
	}
}

WC_MS_Post_Types::init();
