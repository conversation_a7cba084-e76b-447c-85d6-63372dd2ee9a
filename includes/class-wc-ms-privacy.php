<?php
/**
 * Class WC_MS_Privacy file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! class_exists( 'WC_Abstract_Privacy' ) ) {
	return;
}

/**
 * Class WC_MS_Privacy.
 */
class WC_MS_Privacy extends WC_Abstract_Privacy {
	/**
	 * Constructor
	 */
	public function __construct() {
		parent::__construct( __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' ) );

		$this->add_exporter( 'woocommerce-shipping-multiple-addresses-order-data', __( 'WooCommerce Multple Shipping Order Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'order_data_exporter' ) );
		$this->add_eraser( 'woocommerce-shipping-multiple-addresses-order-data', __( 'WooCommerce Multiple Shipping Order Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'order_data_eraser' ) );

		$this->add_exporter( 'woocommerce-shipping-multiple-addresses-customer-data', __( 'WooCommerce Multiple Shipping Customer Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'customer_data_exporter' ) );
		$this->add_eraser( 'woocommerce-shipping-multiple-addresses-customer-data', __( 'WooCommerce Multiple Shipping Customer Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'customer_data_eraser' ) );
	}

	/**
	 * Returns a list of orders that are using multiple shipping.
	 *
	 * @param string $email_address Email address.
	 * @param int    $page Page number in pagination.
	 *
	 * @return array WP_Post
	 */
	protected function get_s2ma_orders( $email_address, $page ) {
		$user = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.

		$order_query = array(
			'meta_key'   => '_multiple_shipping',// phpcs:ignore --- The meta is needed.
			'meta_value' => 'yes',// phpcs:ignore --- The meta is needed.
			'limit'      => 10,
			'page'       => $page,
		);

		if ( $user instanceof WP_User ) {
			$order_query['customer_id'] = (int) $user->ID;
		} else {
			$order_query['billing_email'] = $email_address;
		}

		return wc_get_orders( $order_query );
	}

	/**
	 * Gets the message of the privacy to display.
	 */
	public function get_privacy_message() {
		// translators: %s is a privacy shipping URL.
		return wpautop( sprintf( __( 'By using this extension, you may be storing personal data or sharing data with an external service. <a href="%s" target="_blank">Learn more about how this works, including what you may want to include in your privacy policy.</a>', 'woocommerce-shipping-multiple-addresses' ), 'https://docs.woocommerce.com/document/privacy-shipping/#woocommerce-shipping-multiple-addresses' ) );
	}

	/**
	 * Handle exporting data for Orders.
	 *
	 * @param string $email_address E-mail address to export.
	 * @param int    $page          Pagination of data.
	 *
	 * @return array
	 */
	public function order_data_exporter( $email_address, $page = 1 ) {
		$done           = false;
		$data_to_export = array();

		$orders = $this->get_s2ma_orders( $email_address, (int) $page );

		$done = true;

		if ( 0 < count( $orders ) ) {
			foreach ( $orders as $order ) {
				$packages = $order->get_meta( '_wcms_packages' );

				foreach ( $packages as $idx => $package ) {
					$products = $package['contents'];
					$address  = ( ! empty( $package['destination'] ) ) ? WC()->countries->get_formatted_address( $package['destination'], ', ' ) : '';

					// translators: %s is package destination address.
					$data  = sprintf( __( 'Products listing for shipping address "%s": ', 'woocommerce-shipping-multiple-addresses' ), $address );
					$data .= implode(
						', ',
						array_map(
							function ( $product ) {
								return get_the_title( $product['data']->id );
							},
							$products
						)
					);

					$order_note = $order->get_meta( '_note_' . $idx );

					if ( ! empty( $order_note ) ) {
						// translators: %s is order note.
						$data .= sprintf( __( '. Note: %s.', 'woocommerce-shipping-multiple-addresses' ), $order_note );
					}

					$data_to_export[] = array(
						'group_id'    => 'woocommerce_orders',
						'group_label' => __( 'Orders', 'woocommerce-shipping-multiple-addresses' ),
						'item_id'     => 'order-' . $order->get_id(),
						'data'        => array(
							array(
								// translators: %s is package index.
								'name'  => sprintf( __( 'Multiple Shipping package "%s"', 'woocommerce-shipping-multiple-addresses' ), $idx ),
								'value' => $data,
							),
						),
					);
				}

				$shipment_data = WC_MS_Order_Shipment::get_shipment_objects_by_order( $order->get_id() );

				foreach ( $shipment_data as $shipment ) {
					$data_to_export[] = array(
						'group_id'    => 'woocommerce_orders',
						'group_label' => __( 'Orders', 'woocommerce-shipping-multiple-addresses' ),
						'item_id'     => 'order-' . $order->get_id(),
						'data'        => array(
							array(
								// translators: %s is for order shipment ID.
								'name'  => sprintf( __( 'Multiple Shipping Order Shipment "%s"', 'woocommerce-shipping-multiple-addresses' ), $shipment->get_id() ),
								'value' => $shipment->get_customer_note(),
							),
						),
					);
				}
			}

			$done = 10 > count( $orders );
		}

		return array(
			'data' => $data_to_export,
			'done' => $done,
		);
	}

	/**
	 * Finds and exports customer data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function customer_data_exporter( $email_address, $page ) {
		$user           = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.
		$data_to_export = array();

		if ( $user instanceof WP_User ) {
			$data_to_export[] = array(
				'group_id'    => 'woocommerce_customer',
				'group_label' => __( 'Customer Data', 'woocommerce-shipping-multiple-addresses' ),
				'item_id'     => 'user',
				'data'        => array(
					array(
						'name'  => __( 'Multiple Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
						'value' => wp_json_encode( get_user_meta( $user->ID, 'wc_other_addresses', true ) ),
					),
				),
			);
		}

		return array(
			'data' => $data_to_export,
			'done' => true,
		);
	}

	/**
	 * Finds and erases customer data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function customer_data_eraser( $email_address, $page ) {
		$page = (int) $page;
		$user = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.

		$other_addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

		$items_removed = false;
		$messages      = array();

		if ( ! empty( $other_addresses ) ) {
			$items_removed = true;
			delete_user_meta( $user->ID, 'wc_other_addresses' );
			$messages[] = __( 'Multiple Shipping User Data Erased.', 'woocommerce-shipping-multiple-addresses' );
		}

		return array(
			'items_removed'  => $items_removed,
			'items_retained' => false,
			'messages'       => $messages,
			'done'           => true,
		);
	}

	/**
	 * Finds and erases order data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function order_data_eraser( $email_address, $page ) {
		$orders = $this->get_s2ma_orders( $email_address, (int) $page );

		$items_removed  = false;
		$items_retained = false;
		$messages       = array();

		foreach ( (array) $orders as $order ) {
			$order = wc_get_order( $order->get_id() );

			list( $removed, $retained, $msgs ) = $this->maybe_handle_order( $order );
			$items_removed                    |= $removed;
			$items_retained                   |= $retained;
			$messages                          = array_merge( $messages, $msgs );
		}

		// Tell core if we have more orders to work on still.
		$done = count( $orders ) < 10;

		return array(
			'items_removed'  => $items_removed,
			'items_retained' => $items_retained,
			'messages'       => $messages,
			'done'           => $done,
		);
	}

	/**
	 * Handle eraser of data tied to Orders
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	protected function maybe_handle_order( $order ) {
		$order_id = $order->get_id();

		$packages          = $order->get_meta( '_shipping_packages' );
		$sess_item_address = $order->get_meta( '_shipping_addresses' );
		$sess_packages     = $order->get_meta( '_wcms_packages' );
		$ms_methods        = $order->get_meta( '_shipping_methods' );
		$sess_rates        = $order->get_meta( '_shipping_rates' );

		if ( empty( $packages ) && empty( $sess_item_address ) && empty( $sess_packages ) && empty( $ms_methods ) && empty( $sess_rates ) ) {
			return array( false, false, array() );
		}

		$shipment_data = WC_MS_Order_Shipment::get_shipment_objects_by_order( $order->get_id() );

		foreach ( $shipment_data as $shipment ) {
			$shipment->delete( true );
		}

		foreach ( $packages as $idx => $package ) {
			$order->delete_meta_data( '_note_' . $idx );
			$order->delete_meta_data( '_date_' . $idx );
		}

		$order->delete_meta_data( '_shipping_packages' );
		$order->delete_meta_data( '_shipping_addresses' );
		$order->delete_meta_data( '_wcms_packages' );
		$order->delete_meta_data( '_shipping_methods' );
		$order->delete_meta_data( '_shipping_rates' );
		$order->save();

		return array( true, false, array( __( 'Multiple Shipping Order Data Erased.', 'woocommerce-shipping-multiple-addresses' ) ) );
	}
}

new WC_MS_Privacy();
