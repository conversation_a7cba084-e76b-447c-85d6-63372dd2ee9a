<?php
/**
 * Class WC_MS_Gifts file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class WC_MS_Gifts.
 */
class WC_MS_Gifts {

	/**
	 * Main class instance.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Main class instance.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {

		$this->wcms = $wcms;

		add_action( 'wc_ms_shipping_package_block', array( __CLASS__, 'render_gift_form' ), 10, 1 );

		add_action( 'woocommerce_checkout_update_order_meta', array( __CLASS__, 'store_order_gift_data' ), 20, 2 );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( __CLASS__, 'store_order_gift_data' ), 20 );

		add_action( 'woocommerce_before_checkout_shipping_form', array( __CLASS__, 'shipping_address_gift_form' ) );
		add_action( 'woocommerce_checkout_update_order_meta', array( __CLASS__, 'store_shipping_address_gift_data' ) );
		add_action( 'woocommerce_admin_order_data_after_shipping_address', array( __CLASS__, 'render_order_shipping_gift_data' ) );

		// Modify the packages, shipping methods and addresses in the session.
		add_filter( 'wc_ms_checkout_session_packages', array( __CLASS__, 'apply_gift_data_to_packages' ), 30 );

		add_action( 'wc_ms_order_package_block_before_address', array( __CLASS__, 'render_gift_data' ), 10, 3 );
	}

	/**
	 * Returns TRUE if the Gift Packages setting is enabled
	 *
	 * @return bool
	 */
	public static function is_enabled() {
		global $wcms;

		if ( ! isset( $wcms->gateway_settings['gift_packages'] ) || 'yes' !== $wcms->gateway_settings['gift_packages'] ) {
			return false;
		}

		return true;
	}

	/**
	 * Show the gift checkbox on the shipping packages blocks.
	 *
	 * @param string $loop Array key.
	 *
	 * @return void
	 */
	public static function render_gift_form( $loop ) {
		if ( ! self::is_enabled() ) {
			return;
		}

		?>
		<div class="gift-form">
			<p>
				<label>
					<input type="checkbox" class="chk-gift" name="shipping_gift[<?php echo esc_attr( $loop ); ?>]" value="yes" data-index="<?php echo esc_attr( $loop ); ?>" />
					<?php esc_html_e( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
			</p>
		</div>

		<?php
	}

	/**
	 * Modify the 'wcms_packages' session data to attach gift data from POST
	 * and at the same time, populate the WC_Gift_Checkout::gifts array
	 *
	 * @param array $packages Shipping packages to modify.
	 *
	 * @return array
	 */
	public static function apply_gift_data_to_packages( $packages ) {
		// No need to use nonce verification. it has been verified on `WC_Checkout::process_checkout()`.
		$shipping_gift = isset( $_POST['shipping_gift'] ) ? wc_clean( $_POST['shipping_gift'] ) : array(); // phpcs:ignore
		if ( empty( $shipping_gift ) ) {
			return $packages;
		}

		foreach ( $shipping_gift as $idx => $value ) {

			if ( 'yes' !== $value ) {
				continue;
			}

			if ( ! isset( $packages[ $idx ] ) ) {
				continue;
			}

			$packages[ $idx ]['gift'] = true;

		}

		return $packages;
	}

	/**
	 * Store gift data.
	 *
	 * @param int|WC_Order $new_order Either order ID or order object.
	 *
	 * @return void
	 */
	public static function store_order_gift_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		// No need for nonce verification. It has been verified on `WC_Checkout::process_checkout()`.
		$shipping_gift = isset( $_POST['shipping_gift'] ) ? wc_clean( $_POST['shipping_gift'] ) : array(); //phpcs:ignore
		$packages      = $order->get_meta( '_wcms_packages' );

		if ( ! empty( $shipping_gift ) && is_array( $shipping_gift ) ) {
			foreach ( $shipping_gift as $idx => $value ) {

				if ( 'yes' !== $value || ! array_key_exists( $idx, $packages ) ) {
					continue;
				}

				$order->update_meta_data( '_gift_' . $idx, true );
			}
		} elseif ( empty( $shipping_gift ) && is_array( $packages ) ) {
			foreach ( $packages as $idx => $package ) {
				if ( isset( $package['gift'] ) && ( 'yes' === $package['gift'] || true === $package['gift'] ) ) {
					$order->update_meta_data( '_gift_' . $idx, true );
				}
			}
		}

		$order->save();
	}

	/**
	 * Render the 'This is a Gift' option in the shipping address form
	 */
	public static function shipping_address_gift_form() {
		if ( ! self::is_enabled() ) {
			return;
		}
		?>
		<div class="gift-form">
			<p>
				<label>
					<input type="checkbox" class="chk-gift" name="checkout_shipping_gift" value="yes" />
					<?php esc_html_e( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
			</p>
		</div>
		<?php
	}

	/**
	 * Mark an order as a gift.
	 *
	 * @param int|WC_Order $new_order Either order ID or order object.
	 *
	 * @return void.
	 */
	public static function store_shipping_address_gift_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		// No need for nonce verification. It has been verified on `WC_Checkout::process_checkout()`.
        if ( ! empty( $_POST['checkout_shipping_gift'] ) ) { //phpcs:ignore
			$order->update_meta_data( '_gift', true );
			$order->save();
		}
	}

	/**
	 * Render gift data for shipping address.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return void
	 */
	public static function render_order_shipping_gift_data( $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$is_gift = $order->get_meta( '_gift' );

		if ( $is_gift ) {
			echo '<p><span class="dashicons dashicons-megaphone"></span> <strong>This is a gift</strong></p>';
		}
	}

	/**
	 * Render gift data.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $package Package to check.
	 * @param string    $package_index Package index.
	 *
	 * @return void.
	 */
	public static function render_gift_data( $order, $package, $package_index ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages      = $order->get_meta( '_wcms_packages' );
		$order_is_gift = wc_string_to_bool( $order->get_meta( '_gift_' . $package_index ) );

		if ( $order_is_gift && is_array( $packages ) && 1 === count( $packages ) ) {
			/**
			 * Inject the gift data into the only package
			 * because multishipping doesn't process gift
			 * data when there's only one package
			 */
			$package['gift'] = true;
		}

		if ( isset( $package['gift'] ) && true === $package['gift'] ) {
			?>
			<div class="gift-package">
				<h5><div class="dashicons dashicons-yes"></div><?php esc_html_e( 'This is a Gift', 'woocommerce-shipping-multiple-addresses' ); ?></h5>
			</div>
			<?php

		}
	}
}
