<?php
/**
 * Class WC_MS_Checkout file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\Utilities\NumberUtil;
use Automattic\WooCommerce\StoreApi\Exceptions\RouteException;

/**
 * Class WC_MS_Checkout.
 */
class WC_MS_Checkout {

	/**
	 * Main class instance.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Main class instance.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {

		$this->wcms = $wcms;

		// free shipping minimum order.
		add_filter( 'woocommerce_shipping_free_shipping_is_available', array( $this, 'free_shipping_is_available_for_package' ), 10, 3 );

		add_filter( 'woocommerce_package_rates', array( $this, 'remove_multishipping_from_methods' ), 10, 2 );
		add_action( 'woocommerce_before_checkout_shipping_form', array( $this, 'display_set_addresses_button' ), 5 );
		add_action( 'woocommerce_before_checkout_shipping_form', array( $this, 'render_user_addresses_dropdown' ) );
		add_action( 'wp_ajax_wcms_ajax_save_billing_fields', array( $this, 'save_billing_fields' ) );
		add_action( 'wp_ajax_nopriv_wcms_ajax_save_billing_fields', array( $this, 'save_billing_fields' ) );
		add_action( 'woocommerce_after_checkout_validation', array( $this, 'legacy_checkout_validation' ) );

		add_action( 'woocommerce_checkout_update_order_meta', array( $this, 'legacy_checkout_process' ) );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( $this, 'blocks_checkout_process' ), 10 );
		add_action( 'woocommerce_store_api_cart_select_shipping_rate', array( $this, 'select_shipping_rate_on_store_api' ), 10, 3 );
		add_action( 'woocommerce_store_api_cart_update_customer_from_request', array( $this, 'update_customer_on_store_api' ), 10, 2 );
		add_action( 'woocommerce_order_after_calculate_totals', array( $this, 'calculate_order_taxes_for_wc_blocks' ), 10, 2 );
		add_action( 'woocommerce_store_api_checkout_update_customer_from_request', array( $this, 'blocks_checkout_validation' ) );
		add_action( 'woocommerce_store_api_checkout_update_order_from_request', array( $this, 'add_temporary_shipping_address_on_parent' ), 10, 2 );

		add_filter( 'woocommerce_order_item_meta', array( $this, 'add_item_meta' ), 10, 2 );
		// Start WC Blocks Changes.
		add_filter( 'pre_option_woocommerce_ship_to_destination', array( $this, 'force_use_billing_address' ), 10, 3 );

		// handle order review events.
		add_action( 'woocommerce_checkout_update_order_review', array( $this, 'update_order_review' ) );
		add_action( 'woocommerce_after_calculate_totals', array( $this, 'calculate_totals' ) );

		// modify a cart item's subtotal to include taxes.
		add_filter( 'woocommerce_cart_item_subtotal', array( $this, 'subtotal_item_include_taxes' ), 10, 3 );

		add_action( 'woocommerce_checkout_order_processed', array( $this, 'legacy_clear_session' ) );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'blocks_clear_session' ), 40 );

		// split order.
		add_action( 'woocommerce_checkout_order_processed', array( $this, 'create_order_shipments' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'remove_temporary_shipping_address_on_parent' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'create_order_shipments' ), 20, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'restore_customer_default_data' ), 30, 1 );

		// stop WC from updating the customer's shipping address.
		// instead, store it in the address book if it's a new shipping address.
		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );
		add_action( 'woocommerce_checkout_update_user_meta', array( $this, 'maybe_store_shipping_address' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_update_customer_from_request', array( $this, 'maybe_store_shipping_address' ), 10, 1 );

		// Initialize order meta.
		add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'store_item_key' ), 10, 2 );

		// Reset multiple address.
		add_action( 'init', array( $this, 'reset_multiple_shipping_address' ), 10 );

		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'maybe_prevent_update_customer_data' ), 100, 1 );
		add_action( 'woocommerce_checkout_update_user_meta', array( $this, 'maybe_save_customer_data' ), 10, 2 );

		add_action('woocommerce_applied_coupon', array( $this, 'coupon_flush_cache' ) );
		add_action('woocommerce_removed_coupon', array( $this, 'coupon_flush_cache' ) );
	}

	/**
	 * Flush the cache when coupon is applied or removed.
	 */
	public function coupon_flush_cache () {
		if ( class_exists( 'WC_Cache_Helper' ) ) {
			WC_Cache_Helper::get_transient_version( 'shipping', true );
		}
	}

	/**
	 * Prevent updating customer data if it's using saved address.
	 *
	 * @param bool $need_update Whether need to update customer data or not.
	 *
	 * return Int.
	 */
	public function maybe_prevent_update_customer_data( $need_update ) {
		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return $need_update;
		}

		// The nonce has been implemented on WooCommerce plugin part.
		// It has been verified on `WC_Checkout::process_checkout()`.
		$use_saved_address    = isset( $_POST['ms_addresses'] ) && '' !== $_POST['ms_addresses'];  // phpcs:ignore WordPress.Security.NonceVerification.Missing
		$to_different_address = ! empty( $_POST['ship_to_different_address'] ); // phpcs:ignore WordPress.Security.NonceVerification.Missing

		if ( $to_different_address && $use_saved_address ) {
			return false;
		}

		return $need_update;
	}

	/**
	 * Save the other customer data if it's using saved address.
	 *
	 * @param Int   $customer_id Customer ID or User ID.
	 * @param Array $data        Post data.
	 */
	public function maybe_save_customer_data( $customer_id, $data ) {
		// The nonce has been implemented on WooCommerce plugin part.
		// It has been verified on `WC_Checkout::process_checkout()`.
		$use_saved_address    = isset( $_POST['ms_addresses'] ) && '' !== $_POST['ms_addresses'];  // phpcs:ignore WordPress.Security.NonceVerification.Missing
		$to_different_address = ! empty( $data['ship_to_different_address'] );

		if ( ! $to_different_address || ! $use_saved_address ) {
			return;
		}

		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		if ( ! is_array( $data ) ) {
			return;
		}

		$customer = new WC_Customer( $customer_id );

		if ( ! empty( $data['billing_first_name'] ) && '' === $customer->get_first_name() ) {
			$customer->set_first_name( $data['billing_first_name'] );
		}

		if ( ! empty( $data['billing_last_name'] ) && '' === $customer->get_last_name() ) {
			$customer->set_last_name( $data['billing_last_name'] );
		}

		// If the display name is an email, update to the user's full name.
		if ( is_email( $customer->get_display_name() ) ) {
			$customer->set_display_name( $customer->get_first_name() . ' ' . $customer->get_last_name() );
		}

		// To make sure it only skips these keys.
		$shipping_address_keys = array(
			'shipping_first_name',
			'shipping_last_name',
			'shipping_company',
			'shipping_country',
			'shipping_address_1',
			'shipping_address_2',
			'shipping_city',
			'shipping_state',
			'shipping_postcode',
		);

		$filtered_data = array_filter(
			$data,
			function ( $key ) use ( $shipping_address_keys ) {
				return ! in_array( $key, $shipping_address_keys, true );
			},
			ARRAY_FILTER_USE_KEY
		);

		foreach ( $filtered_data as $key => $value ) {
			// Use setters where available.
			if ( is_callable( array( $customer, "set_{$key}" ) ) ) {
				$customer->{"set_{$key}"}( $value );

				// Store custom fields prefixed with wither billing_.
			} elseif ( 0 === stripos( $key, 'billing_' ) ) {
				$customer->update_meta_data( $key, $value );
			}
		}

		$customer->save();
	}

	/**
	 * Actions for order item meta.
	 *
	 * @since 3.3.23
	 * @return void
	 */
	public function init_order_meta() {
		add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'store_item_key' ), 10, 2 );
	}

	/**
	 * This method checks if free shipping is available for the current package,
	 * depending on min_amount/requires.
	 *
	 * @param bool               $is_available Is free shipping available.
	 * @param array              $package Cart package.
	 * @param WC_Shipping_Method $shipping_method Shipping method object.
	 *
	 * @return bool
	 */
	public function free_shipping_is_available_for_package( $is_available, $package, $shipping_method ) {
		$min_amount         = $shipping_method->get_option( 'min_amount' );
		$requires           = $shipping_method->get_option( 'requires' );
		$ignore_discounts   = $shipping_method->get_option( 'ignore_discounts' );
		$has_met_min_amount = false;
		$has_coupon         = false;

		if ( in_array( $requires, array( 'coupon', 'either', 'both' ), true ) ) {
			$coupons = WC()->cart->get_coupons();

			if ( $coupons ) {
				foreach ( $coupons as $code => $coupon ) {
					if ( $coupon->is_valid() && $coupon->get_free_shipping() ) {
						$has_coupon = true;
						break;
					}
				}
			}
		}

		if ( in_array( $requires, array( 'min_amount', 'either', 'both' ), true ) && isset( $package['cart_subtotal'] ) ) {
			$total = $package['cart_subtotal'];

			if ( WC()->cart->display_prices_including_tax() ) {
				$total = $total - WC()->cart->get_discount_tax();
			}

			if ( 'no' === $ignore_discounts ) {
				$total = $total - WC()->cart->get_discount_total();
			}

			$total = round( $total, wc_get_price_decimals() );

			if ( $total >= $min_amount ) {
				$has_met_min_amount = true;
			}
		}

		switch ( $requires ) {
			case 'min_amount':
				$is_available = $has_met_min_amount;
				break;
			case 'coupon':
				$is_available = $has_coupon;
				break;
			case 'both':
				$is_available = $has_met_min_amount && $has_coupon;
				break;
			case 'either':
				$is_available = $has_met_min_amount || $has_coupon;
				break;
			default:
				$is_available = true;
				break;
		}

		return $is_available;
	}

	/**
	 * Remove multishipping from shipping methods.
	 *
	 * @param array $rates Shipping rates.
	 *
	 * @return array
	 */
	public function remove_multishipping_from_methods( $rates ) {

		if ( ! wcms_session_isset( 'wcms_packages' ) && isset( $rates['multiple_shipping'] ) ) {
			unset( $rates['multiple_shipping'] );
		}

		return $rates;
	}

	/**
	 * Display multiple address button on checkout page.
	 */
	public function display_set_addresses_button() {
		if ( is_checkout() && ! $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() ) {

			$css = 'display:none;';

			if ( $this->wcms->is_multiship_enabled() && $this->wcms->cart->cart_is_eligible_for_multi_shipping() ) {
				$css = '';
			} else {
				// clear all session, so we don't use old cart addresses in case the customer adds more valid products to the cart.
				$this->wcms->clear_session();
			}

			echo '
				<p class="woocommerce-info woocommerce_message" id="wcms_message" style="' . esc_attr( $css ) . '">
					' . esc_html( WC_Ship_Multiple::$lang['notification'] ) . '<br /><br />
					<input type="button" id="wcms_set_addresses" name="wcms_set_addresses" value="' . esc_attr( WC_Ship_Multiple::$lang['btn_items'] ) . '" />
				</p>';
		}
	}

	/**
	 * Save billing address fields.
	 */
	public function save_billing_fields() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, 'wcms_save_billing_fields_nonce' ) ) {
			wp_send_json(
				array(
					'result'  => 'error',
					'message' => esc_html__( 'Permission denied: Security check failed', 'woocommerce-shipping-multiple-addresses' ),
				)
			);
		}

		foreach ( WC()->checkout->get_checkout_fields( 'billing' ) as $key => $field ) {
			if ( is_callable( array( WC()->customer, "set_{$key}" ) ) ) {
				$post_key = isset( $_POST[ $key ] ) ? wc_clean( wp_unslash( $_POST[ $key ] ) ) : '';
				WC()->customer->{"set_{$key}"}( $post_key );
			}
		}

		WC()->customer->save();

		wp_send_json( array( 'result' => 'success' ) );
	}

	/**
	 * Render user addresses dropdown.
	 */
	public function render_user_addresses_dropdown() {

		$addresses = $this->wcms->address_book->get_available_user_addresses( wp_get_current_user() );

		if ( count( $addresses ) ) :
			?>
		<p id="ms_shipping_addresses_field" class="form-row form-row-wide ms-addresses-field">
			<label><?php esc_html_e( 'Stored Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></label>
			<select id="ms_addresses" name="ms_addresses">
				<option value=""><?php esc_html_e( 'Select an address to use&hellip;', 'woocommerce-shipping-multiple-addresses' ); ?></option>
				<?php
				foreach ( $addresses as $key => $address ) {
					$formatted_address = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ', ' . $address['shipping_address_1'] . ', ' . $address['shipping_city'];
					echo '<option value="' . esc_attr( $key ) . '"';
					foreach ( $address as $key => $value ) {
						echo ' data-' . esc_attr( $key ) . '="' . esc_attr( $value ) . '"';
					}
					echo '>' . esc_html( $formatted_address ) . '</option>';
				}
				?>
			</select>
		</p>
			<?php
		endif;
	}

	/**
	 * Store original cart item key
	 *
	 * @param  WC_Order_Item_Product $item          Order item.
	 * @param  string                $cart_key      Cart item key.
	 */
	public function store_item_key( $item, $cart_key ) {
		$item->add_meta_data( '_wcms_cart_key', $cart_key, true );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout for legacy checkout.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 */
	public function legacy_checkout_process( $new_order ) {
		$this->checkout_process( $new_order, false );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout for WC Blocks.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 */
	public function blocks_checkout_process( $new_order ) {
		$this->checkout_process( $new_order, true );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 * @param boolean      $is_wc_blocks Is using WC Blocks or not.
	 */
	public function checkout_process( $new_order, $is_wc_blocks = false ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		$order_id = $order->get_id();

		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		$has_item_address  = ( ! wcms_session_isset( 'cart_item_addresses' ) || empty( $sess_item_address ) ) ? false : true;

		/**
		 * Allow plugins to add action before checkout multi shipping process.
		 *
		 * @param int $order_id Order ID.
		 *
		 * @since 3.3
		 */
		do_action( 'wc_ms_before_checkout_process', $order_id );

		$packages = WC()->cart->get_shipping_packages();

		$sess_item_address = wcms_session_isset( 'cart_item_addresses' ) ? wcms_session_get( 'cart_item_addresses' ) : false;
		$sess_packages     = wcms_session_isset( 'wcms_packages' ) ? wcms_session_get( 'wcms_packages' ) : false;
		$sess_methods      = wcms_session_isset( 'shipping_methods' ) ? wcms_session_get( 'shipping_methods' ) : false;
		$sess_rates        = wcms_session_isset( 'wcms_package_rates' ) ? wcms_session_get( 'wcms_package_rates' ) : false;

		/**
		 * Allow plugins to modify session cart item addresses one last time.
		 *
		 * @param array $sess_item_address Session cart item addresses data.
		 *
		 * @since 3.3
		 */
		$sess_item_address = apply_filters( 'wc_ms_checkout_session_item_address', $sess_item_address );

		/**
		 * Allow plugins to modify session packages one last time.
		 *
		 * @param array $sess_packages Session packages data.
		 *
		 * @since 3.3
		 */
		$sess_packages = apply_filters( 'wc_ms_checkout_session_packages', $sess_packages );

		/**
		 * Allow plugins to modify session method data one last time.
		 *
		 * @param array $sess_methods Session methods data.
		 *
		 * @since 3.3
		 */
		$sess_methods = apply_filters( 'wc_ms_checkout_session_methods', $sess_methods );

		/**
		 * Allow plugins to modify session rate data one last time.
		 *
		 * @param array $sess_rates Session rates data.
		 *
		 * @since 3.3
		 */
		$sess_rates = apply_filters( 'wc_ms_checkout_session_rates', $sess_rates );

		if ( $has_item_address ) {
			$order->update_meta_data( '_multiple_shipping', 'yes' );
		} else {
			$order->delete_meta_data( '_multiple_shipping' );
		}

		// Update the taxes.
		$packages      = $this->calculate_taxes( null, $packages, true );
		$packages      = $this->apply_extra_data_to_package( $packages, true );
		$sess_packages = $this->calculate_taxes( null, $sess_packages, true );
		$sess_packages = $this->apply_extra_data_to_package( $sess_packages, true );

		if ( $packages ) {
			$order->update_meta_data( '_shipping_packages', $packages );
		}

		if ( false !== $sess_item_address && ! empty( $sess_item_address ) ) {
			$order->update_meta_data( '_shipping_addresses', $sess_item_address );

			if ( is_array( $sess_packages ) ) {
				if ( $has_item_address ) {
					$shipping_address = array(
						'first_name' => '',
						'last_name'  => '',
						'company'    => '',
						'address_1'  => '',
						'address_2'  => '',
						'city'       => '',
						'postcode'   => '',
						'country'    => '',
						'state'      => '',
					);

					if ( 1 === count( $sess_packages ) ) {
						$current_package  = current( $sess_packages );
						$shipping_address = $current_package['destination'];
					}

					// Remove the shipping address.
					$order->set_shipping_first_name( $shipping_address['first_name'] );
					$order->set_shipping_last_name( $shipping_address['last_name'] );
					$order->set_shipping_company( $shipping_address['company'] );
					$order->set_shipping_address_1( $shipping_address['address_1'] );
					$order->set_shipping_address_2( $shipping_address['address_2'] );
					$order->set_shipping_city( $shipping_address['city'] );
					$order->set_shipping_postcode( $shipping_address['postcode'] );
					$order->set_shipping_state( $shipping_address['state'] );

					// Only set shipping country when using WC blocks.
					// Because WC Blocks will check if shipping country is empty or not.
					// Legacy checkout doesn't need to do this. Because they get the shipping country directly from the post data.
					if ( ! $is_wc_blocks ) {
						$order->set_shipping_country( $shipping_address['country'] );
					}
				}
			}
		}

		if ( false !== $sess_packages && ! empty( $sess_packages ) && $has_item_address ) {
			$order->update_meta_data( '_wcms_packages', $sess_packages );
		}

		if ( false !== $sess_methods && ! empty( $sess_methods ) && $has_item_address ) {
			$methods = $sess_methods;
			$order->update_meta_data( '_shipping_methods', $methods );

		} else {
			$methods    = $order->get_shipping_methods();
			$ms_methods = array();

			if ( $sess_packages ) {
				foreach ( $sess_packages as $pkg_idx => $package ) {
					foreach ( $methods as $method ) {
						$ms_methods[ $pkg_idx ] = array(
							'id'    => $method['method_id'],
							'label' => $method['name'],
						);
						continue 2;
					}
				}
			}

			$order->update_meta_data( '_shipping_methods', $ms_methods );
		}

		if ( false !== $sess_rates ) {
			$order->update_meta_data( '_shipping_rates', $sess_rates );
		}

		$order->save();

		/**
		 * Allow plugins to add more action after multi shipping checkout process.
		 *
		 * @param int $order_id Order ID.
		 *
		 * @since 3.3
		 */
		do_action( 'wc_ms_after_checkout_process', $order_id );
	}

	/**
	 * Add temporary shipping address to bypass the WC Store API validation in `Automattic\WooCommerce\StoreApi\Utilities\OrderController::validate_address_fields()`.
	 * Context: https://github.com/woocommerce/woocommerce/blob/2a0ad34d999417bbce78aaa490b869a2368d95f8/plugins/woocommerce/src/StoreApi/Utilities/OrderController.php#L389-L398
	 *
	 * @param \WC_Order        $order Order object.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function add_temporary_shipping_address_on_parent( $order, $request ) {
		$billing_address = $order->get_address( 'billing' );

		$order->set_shipping_first_name( $billing_address['first_name'] );
		$order->set_shipping_last_name( $billing_address['last_name'] );
		$order->set_shipping_company( $billing_address['company'] );
		$order->set_shipping_address_1( $billing_address['address_1'] );
		$order->set_shipping_address_2( $billing_address['address_2'] );
		$order->set_shipping_city( $billing_address['city'] );
		$order->set_shipping_postcode( $billing_address['postcode'] );
		$order->set_shipping_state( $billing_address['state'] );

		$order->save();
	}

	/**
	 * Remove temporary shipping address after the shipping address validation has been bypassed.
	 *
	 * @param \WC_Order $order Order object.
	 */
	public function remove_temporary_shipping_address_on_parent( $order ) {
		$order->set_shipping_first_name( '' );
		$order->set_shipping_last_name( '' );
		$order->set_shipping_company( '' );
		$order->set_shipping_address_1( '' );
		$order->set_shipping_address_2( '' );
		$order->set_shipping_city( '' );
		$order->set_shipping_postcode( '' );
		$order->set_shipping_state( '' );

		$order->save();
	}

	/**
	 * Checkout validation for legacy checkout.
	 *
	 * @param array $post POST data.
	 */
	public function legacy_checkout_validation( $post ) {
		$this->checkout_validation( $post );
	}

	/**
	 * Checkout validation for WC checkout blocks.
	 *
	 * @param array $post POST data.
	 */
	public function blocks_checkout_validation( $post ) {
		$this->checkout_validation( $post, true );
	}

	/**
	 * Making sure all multiple shipping package has address on checkout.
	 *
	 * @param array   $post         POST data.
	 * @param boolean $is_wc_blocks Whether is WC Blocks or not.
	 */
	public function checkout_validation( $post, $is_wc_blocks = false ) {

		if ( ! $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		if ( $is_wc_blocks || empty( $post['shipping_method'] ) || 'multiple_shipping' === $post['shipping_method'] || ( is_array( $post['shipping_method'] ) && count( $post['shipping_method'] ) > 1 ) ) {
			$packages  = wcms_session_get( 'wcms_packages' );
			$has_empty = false;

			foreach ( $packages as $package ) {
				if ( empty( $package['contents'] ) || ( isset( $package['bundled_by'] ) && ! empty( $package['bundled_by'] ) ) ) {
					continue;
				}

				if ( $this->wcms->is_address_empty( $package['destination'] ) ) {
					$has_empty = true;
				}
			}

			if ( ! $has_empty ) {
				return;
			}

			$error_text = __( 'One or more items has no shipping address.', 'woocommerce-shipping-multiple-addresses' );

			if ( ! $is_wc_blocks ) {
				wc_add_notice( $error_text, 'error' );
			} else {
				throw new RouteException(
					'wcms_rest_items_no_shipping_address',
					$error_text,
					400,
					array()
				);
			}
		}
	}

	/**
	 * Add order item meta.
	 *
	 * @param WC_Order_Item_Meta $meta Order item meta object.
	 * @param array              $values Package content.
	 */
	public function add_item_meta( $meta, $values ) {

		$packages = wcms_session_get( 'wcms_packages' );
		$methods  = wcms_session_isset( 'shipping_methods' ) ? wcms_session_get( 'shipping_methods' ) : false;

		if ( false !== $methods && ! empty( $methods ) ) {
			if ( isset( $values['package_idx'] ) && isset( $packages[ $values['package_idx'] ] ) ) {
				$meta->add( 'Shipping Method', $methods[ $values['package_idx'] ]['label'] );
			}
		}
	}

	/**
	 * Update order data on checkout page.
	 *
	 * @param array $post POST data.
	 */
	public function update_order_review( $post ) {
		// #39: Not processing single-package shipment causes an infinite loop - check for an empty session instead.
		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return;
		}
		$packages = wcms_session_get( 'wcms_packages' );
		if ( empty( $packages ) ) {
			return;
		}

		$ship_methods = array();
		$data         = array();
		$field        = 'shipping_method';
		parse_str( $post, $data );

		$all_shippings = isset( $data['all_shipping_methods'] ) ? json_decode( $data['all_shipping_methods'], true ) : array();

		if ( isset( $data[ $field ] ) && is_array( $data[ $field ] ) ) {
			$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );

			foreach ( $data[ $field ] as $x => $method ) {
				$method_info = isset( $all_shippings[ $method ] ) ? $all_shippings[ $method ] : $method;

				if ( empty( $method_info['label'] ) ) {
					$method_label = $method;
				} else {
					$explode_method = explode( ' ', $method_info['label'] );
					unset( $explode_method[ count( $explode_method ) - 1 ] );
					$method_label = implode( ' ', $explode_method );
				}

				$ship_methods[ $x ] = array(
					'id'    => $method,
					'label' => $method_label,
				);

				// Update chosen methods in WooCommerce session.
				$chosen_shipping_methods[ $x ] = $method;
			}

			wcms_session_set( 'shipping_methods', $ship_methods );
			WC()->session->set( 'chosen_shipping_methods', $chosen_shipping_methods );
		}
	}

	/**
	 * Return packages with subscription product only.
	 *
	 * @param  array $packages array of packages.
	 * @return array $packages array of packages that contain subscription product.
	 *
	 * @since 3.6.28
	 */
	private function get_shippable_subscription_product_packages( $packages ) {

		if ( ! class_exists( 'WC_Subscriptions_Product' ) ) {
			return $packages;
		}

		if ( ! is_array( $packages ) ) {
			return $packages;
		}

		if ( empty( $packages ) ) {
			return $packages;
		}

		$non_subscription_package_idx = array();

		$temp_packages = $packages;

		foreach ( $temp_packages as $x => $package ) {

			$package_contains_subscriptions_needing_shipping = false;

			foreach ( $package['contents'] as $cart_item_key => $values ) {
				$_product = $values['data'];
				if ( WC_Subscriptions_Product::is_subscription( $_product ) && $_product->needs_shipping() && ! WC_Subscriptions_Product::needs_one_time_shipping( $_product ) ) {
					$package_contains_subscriptions_needing_shipping = true;
				}
			}

			if ( ! $package_contains_subscriptions_needing_shipping ) {
				unset( $packages[ $x ] );
			}
		}

		return $packages;
	}

	/**
	 * Check the cart if it only contains a recurring product.
	 *
	 * @param array|WC_Cart $cart Cart object.
	 *
	 * @return boolean
	 *
	 * @since 3.6.28
	 */
	private function is_recurring_cart( $cart ) {

		if ( ! class_exists( 'WC_Subscriptions_Cart' ) ) {
			return false;
		}

		if ( true === WC_Subscriptions_Cart::cart_contains_subscriptions_needing_shipping( $cart ) && empty( $this->get_cart_non_subscription_item( $cart ) ) ) {

			return true;

		} else {

			return false;

		}
	}

	/**
	 * Get non WC_Subscriptions_Product items from cart.
	 *
	 * @param WC_Cart $cart Cart object.
	 *
	 * @return WC_Cart Cart object without subscriptions product in the cart content.
	 *
	 * @since 3.6.28
	 */
	private function get_cart_non_subscription_item( $cart ) {

		if ( ! class_exists( 'WC_Subscriptions_Product' ) ) {
			return $cart;
		}

		$non_subscription_cart_items = array();

		foreach ( $cart->get_cart() as $cart_item_key => $cart_item ) {

			$_product = $cart_item['data'];
			if ( ! WC_Subscriptions_Product::is_subscription( $_product ) && $_product->needs_shipping() ) {
				$non_subscription_cart_items[ $cart_item_key ] = $cart_item;
			}
		}

		return $non_subscription_cart_items;
	}

	/**
	 * Calculate multiple shipping totals on cart object.
	 *
	 * @param WC_Cart $cart Cart object.
	 */
	public function calculate_totals( $cart ) {
		// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( isset( $_REQUEST['wc-ajax'] ) && 'update_shipping_method' === $_REQUEST['wc-ajax'] ) {

			// Update chosen shipping methods to match with WooCommerce session variable.
			$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );
			$methods                 = wcms_session_get( 'shipping_methods' );

			if ( ! empty( $methods ) ) {
				foreach ( $methods as $key => $method ) {
					if ( isset( $chosen_shipping_methods[ $key ] ) ) {
						$methods[ $key ] = array(
							'id'    => $chosen_shipping_methods[ $key ],
							'label' => $chosen_shipping_methods[ $key ],
						);
					}
				}
			}

			wcms_session_set( 'shipping_methods', $methods );
		}

		$shipping_total = 0;
		$shipping_taxes = array();

		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return $cart;
		}
		if ( ! wcms_session_isset( 'shipping_methods' ) ) {
			return $cart;
		}
		if ( ! wcms_session_isset( 'cart_item_addresses' ) ) {
			return $cart;
		}

		$methods  = wcms_session_get( 'shipping_methods' );
		$packages = wcms_session_get( 'wcms_packages' );
		$rates    = array();

		if ( ! $packages ) {
			$packages = WC()->cart->get_shipping_packages();
			WC()->shipping->calculate_shipping( $packages );
		}

		$packages     = WC()->shipping->get_packages();
		$tax_based_on = get_option( 'woocommerce_tax_based_on', 'billing' );

		// Remove non subscription packages if the current cart is a recurring cart object.
		if ( $this->is_recurring_cart( $cart ) ) {
			$packages = $this->get_shippable_subscription_product_packages( $packages );
		}

		foreach ( $packages as $x => $package ) {
			$chosen = isset( $methods[ $x ] ) ? $methods[ $x ]['id'] : '';

			if ( $chosen ) {
				WC()->customer->set_calculated_shipping( false );
				WC()->customer->set_shipping_location(
					$package['destination']['country'],
					$package['destination']['state'],
					$package['destination']['postcode'],
					$package['destination']['city']
				);

				$ship = $chosen;

				if ( isset( $package['rates'] ) ) {
					if ( ! isset( $package['rates'][ $ship ] ) ) {
						$rate = wcms_get_cheapest_shipping_rate( $package['rates'] );

						if ( isset( $rate['id'] ) ) {
							$ship = $rate['id'];
						}
					}

					if ( isset( $package['rates'][ $ship ] ) ) {
						$rate            = $package['rates'][ $ship ];
						$rates[ $x ]     = $package['rates'];
						$shipping_total += $rate->cost;
						// @see: https://github.com/woocommerce/woocommerce/issues/19131 .
						$rate_options = get_option( 'woocommerce_' . $rate->get_method_id() . '_' . $rate->get_instance_id() . '_settings', true );

						// Calculate tax based on package shipping address.
						if ( 'shipping' === $tax_based_on
							&& ( ! isset( $rate_options['tax_status'] ) || 'none' !== $rate_options['tax_status'] ) && ! WC()->customer->get_is_vat_exempt() ) {
							$shipping_tax_rates = WC_Tax::get_shipping_tax_rates();
							$rate->taxes        = WC_Tax::calc_tax( $rate->cost, $shipping_tax_rates );
						}

						// calculate tax.
						foreach ( array_keys( $shipping_taxes + $rate->taxes ) as $key ) {
							$shipping_taxes[ $key ] = ( isset( $rate->taxes[ $key ] ) ? $rate->taxes[ $key ] : 0 ) + ( isset( $shipping_taxes[ $key ] ) ? $shipping_taxes[ $key ] : 0 );
						}

						// Round shipping tax calculation.
						$shipping_taxes = $this->round_shipping_taxes( $shipping_taxes );
					}
				}
			}

			$packages[ $x ] = $package;

		}

		$cart->set_shipping_taxes( $shipping_taxes );

		$cart->shipping_total     = $shipping_total;
		$cart->shipping_tax_total = ( is_array( $shipping_taxes ) ) ? array_sum( $shipping_taxes ) : 0;

		// Store the shipping rates.
		wcms_session_set( 'wcms_package_rates', $rates );

		if ( wc_tax_enabled() && ! WC()->customer->get_is_vat_exempt() ) {
			$this->calculate_taxes( $cart, $packages );
		}

		$this->apply_extra_data_to_package( $packages, false );
	}

	/**
	 * Get discounted price.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $package_content Package content value.
	 *
	 * @return float
	 */
	private function get_discounted_price( $cart, $package_content ) {
		if ( isset( $package_content['key'] ) ) {
			$cart_item_key = $package_content['key'];
		} elseif ( ! isset( $package_content['line_total'] ) ) {
			$cart_item_key = current( array_keys( $cart->cart_contents ) );
		} else {
			return $package_content['line_total'];
		}
		$cart_item = $cart->cart_contents[ $cart_item_key ];
		return $cart_item['line_total'] / $cart_item['quantity'];
	}

	/**
	 * Calculate taxes on multiple shipping.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $packages Cart packages.
	 * @param boolean $return_packages Function should return the value as cart package when `true`.
	 *
	 * @return array|WC_Cart
	 */
	public function calculate_taxes( $cart = null, $packages = null, $return_packages = false ) {

		if ( ! $this->wcms->is_multiship_enabled() || ! $this->wcms->cart->cart_is_eligible_for_multi_shipping() ) {
			return $packages;
		}

		if ( 'yes' !== get_option( 'woocommerce_calc_taxes', 0 ) ) {
			if ( $return_packages ) {
				return $packages;
			}

			return;
		}

		$merge = false;
		if ( ! is_object( $cart ) ) {
			$cart  = WC()->cart;
			$merge = true;
		}
        // phpcs:ignore WordPress.Security.NonceVerification.Missing
		if ( isset( $_POST['action'] ) && 'woocommerce_update_shipping_method' === sanitize_text_field( wp_unslash( $_POST['action'] ) ) ) {
			return $cart;
		}

		if ( ! $packages ) {
			$packages = $cart->get_shipping_packages();
		}

		if ( empty( $packages ) ) {
			return;
		}

		// clear the taxes arrays remove tax totals from the grand total.
		$old_shipping_tax_total = $cart->shipping_tax_total;

		$item_taxes = array();
		$cart_taxes = array();

		foreach ( $packages as $idx => $package ) {
			if ( isset( $package['destination'] ) && ! $this->wcms->is_address_empty( $package['destination'] ) ) {
				WC()->customer->set_calculated_shipping( false );
				WC()->customer->set_shipping_location(
					$package['destination']['country'],
					$package['destination']['state'],
					$package['destination']['postcode'],
					$package['destination']['city']
				);
			}

			$tax_rates      = array();
			$shop_tax_rates = array();

			// Calculate subtotals for items. This is done first so that discount logic can use the values.
			foreach ( $package['contents'] as $cart_item_key => $values ) {

				if ( ! isset( $cart->cart_contents[ $values['key'] ] ) ) {
					continue;
				}

				$_product = $values['data'];

				// Prices.
				$line_price        = $_product->get_price() * $values['quantity'];
				$line_subtotal     = 0;
				$line_subtotal_tax = 0;

				// WC Composite Products.
				if ( isset( $values['composite_data'] ) ) {
					$line_price = 0;

					foreach ( $values['composite_data'] as $composite ) {
						if ( isset( $composite['price'] ) ) {
							$line_price += $composite['price'];
						}
					}

					if ( isset( $values['quantity'] ) ) {
						$line_price *= $values['quantity'];
					}
				}

				if ( ! $_product->is_taxable() ) {
					$line_subtotal = $line_price;
				} elseif ( $cart->prices_include_tax ) {

					// Get base tax rates.
					if ( empty( $shop_tax_rates[ $_product->get_tax_class() ] ) ) {
						$shop_tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_base_tax_rates( $_product->get_tax_class() );
					}

					// Get item tax rates.
					if ( empty( $tax_rates[ $_product->get_tax_class() ] ) ) {
						$tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_rates( $_product->get_tax_class() );
					}

					$base_tax_rates = $shop_tax_rates[ $_product->get_tax_class() ];
					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// ADJUST TAX - Calculations when base tax is not equal to the item tax.
					if ( $item_tax_rates !== $base_tax_rates ) {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $base_tax_rates, true, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal = $line_price - array_sum( $taxes );

						// Now add modified taxes.
						$tax_result        = WC_Tax::calc_tax( $line_subtotal, $item_tax_rates );
						$line_subtotal_tax = array_sum( $tax_result );

						// Regular tax calculation (customer inside base and the tax class is unmodified.
					} else {

						// Calc tax normally.
						$taxes             = WC_Tax::calc_tax( $line_price, $item_tax_rates, true );
						$line_subtotal_tax = array_sum( $taxes );
						$line_subtotal     = $line_price - array_sum( $taxes );

					}

					// Prices exclude tax.
					// This calculation is simpler - work with the base, untaxed price.
				} else {
					// Get item tax rates.
					if ( empty( $tax_rates[ $_product->get_tax_class() ] ) ) {
						$tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_rates( $_product->get_tax_class() );
					}

					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// Base tax for line before discount - we will store this in the order data.
					$taxes             = WC_Tax::calc_tax( $line_price, $item_tax_rates );
					$line_subtotal_tax = array_sum( $taxes );
					$line_subtotal     = $line_price;
				}
			}

			// Calculate totals for items.
			foreach ( $package['contents'] as $cart_item_key => $values ) {
				// To make sure that the 'line_total' will always be declared.
				// In order to fix PHP Notice:  Undefined index: line_total in /wp-includes/class-wp-list-util.php on line 170.
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_total'] = 0;

				if ( ! isset( $cart->cart_contents[ $values['key'] ] ) ) {
					continue;
				}

				$_product = $values['data'];

				// Prices.
				$base_price = $_product->get_price();
				$line_price = $_product->get_price() * $values['quantity'];

				// WC Composite Products.
				if ( isset( $values['composite_data'] ) ) {
					$line_price = 0;

					foreach ( $values['composite_data'] as $composite ) {
						$line_price += $composite['price'];
					}
					$base_price  = $line_price;
					$line_price *= $values['quantity'];
				}

				// Tax data.
				$taxes            = array();
				$discounted_taxes = array();

				if ( ! $_product->is_taxable() ) {
					// Discounted Price (price with any pre-tax discounts applied).
					$discounted_price  = $this->get_discounted_price( $cart, $values );
					$line_subtotal_tax = 0;
					$line_subtotal     = $line_price;
					$line_tax          = 0;
					$line_total        = WC_Tax::round( $discounted_price * $values['quantity'] );

					// Prices include tax.
				} elseif ( $cart->prices_include_tax ) {

					$base_tax_rates = $shop_tax_rates[ $_product->get_tax_class() ];
					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// ADJUST TAX - Calculations when base tax is not equal to the item tax.
					if ( $item_tax_rates !== $base_tax_rates ) {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $base_tax_rates, true, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal = wc_round_tax_total( $line_price - array_sum( $taxes ) );

						// Now add modifed taxes.
						$taxes             = WC_Tax::calc_tax( $line_subtotal, $item_tax_rates );
						$line_subtotal_tax = array_sum( $taxes );

						// Adjusted price (this is the price including the new tax rate).
						$adjusted_price = ( $line_subtotal + $line_subtotal_tax ) / $values['quantity'];

						// Apply discounts.
						$discounted_price = $this->get_discounted_price( $cart, $values );
						$discounted_taxes = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates, false );
						$discounted_taxes = $this->round_line_taxes( $discounted_taxes );
						$line_tax         = array_sum( $discounted_taxes );
						$line_total       = ( $discounted_price * $values['quantity'] ) - $line_tax;

						// Regular tax calculation (customer inside base and the tax class is unmodified.
					} else {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $item_tax_rates, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal     = $line_price - array_sum( $taxes );
						$line_subtotal_tax = array_sum( $taxes );

						// Calc prices and tax (discounted).
						$discounted_price = $this->get_discounted_price( $cart, $values );
						$discounted_taxes = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates, false );
						$discounted_taxes = $this->round_line_taxes( $discounted_taxes );
						$line_tax         = array_sum( $discounted_taxes );
						$line_total       = ( $discounted_price * $values['quantity'] ) - $line_tax;
					}

					// Tax rows - merge the totals we just got.
					foreach ( array_keys( $cart_taxes + $discounted_taxes ) as $key ) {
						$cart_taxes[ $key ] = ( isset( $discounted_taxes[ $key ] ) ? $discounted_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
					}

					// Prices exclude tax.
				} else {

					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// Work out a new base price without the shop's base tax.
					$taxes = WC_Tax::calc_tax( $line_price, $item_tax_rates );

					// Now we have the item price (excluding TAX).
					$line_subtotal     = $line_price;
					$line_subtotal_tax = array_sum( $taxes );

					// Now calc product rates.
					$discounted_price      = $this->get_discounted_price( $cart, $values );
					$discounted_taxes      = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates );
					$discounted_taxes      = $this->round_line_taxes( $discounted_taxes );
					$discounted_tax_amount = array_sum( $discounted_taxes );
					$line_tax              = $discounted_tax_amount;
					$line_total            = $discounted_price * $values['quantity'];

					// Tax rows - merge the totals we just got.
					foreach ( array_keys( $cart_taxes + $discounted_taxes ) as $key ) {
						$cart_taxes[ $key ] = ( isset( $discounted_taxes[ $key ] ) ? $discounted_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
					}
				}

				// Calculate the discount total from cart line data.
				$discount_total     = $line_subtotal - $line_total;
				$discount_tax_total = $line_subtotal_tax - $line_tax;

				// Store costs + taxes for lines.
				if ( ! isset( $item_taxes[ $cart_item_key ] ) ) {
					$item_taxes[ $cart_item_key ]['line_total']          = $line_total;
					$item_taxes[ $cart_item_key ]['line_tax']            = $line_tax;
					$item_taxes[ $cart_item_key ]['line_subtotal']       = $line_subtotal;
					$item_taxes[ $cart_item_key ]['line_subtotal_tax']   = $line_subtotal_tax;
					$item_taxes[ $cart_item_key ]['line_tax_data']       = array(
						'total'    => $discounted_taxes,
						'subtotal' => $taxes,
					);
					$item_taxes[ $cart_item_key ]['line_disc_total']     = $discount_total;
					$item_taxes[ $cart_item_key ]['line_disc_total_tax'] = $discount_tax_total;
				} else {
					$item_taxes[ $cart_item_key ]['line_total']                += $line_total;
					$item_taxes[ $cart_item_key ]['line_tax']                  += $line_tax;
					$item_taxes[ $cart_item_key ]['line_subtotal']             += $line_subtotal;
					$item_taxes[ $cart_item_key ]['line_subtotal_tax']         += $line_subtotal_tax;
					$item_taxes[ $cart_item_key ]['line_tax_data']['total']    += $discounted_taxes;
					$item_taxes[ $cart_item_key ]['line_tax_data']['subtotal'] += $taxes;
					$item_taxes[ $cart_item_key ]['line_disc_total']           += $discount_total;
					$item_taxes[ $cart_item_key ]['line_disc_total_tax']       += $discount_tax_total;
				}

				$packages[ $idx ]['contents'][ $cart_item_key ]['line_total']          = $line_total;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_tax']            = $line_tax;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_subtotal']       = $line_subtotal;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_subtotal_tax']   = $line_subtotal_tax;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_tax_data']       = array(
					'total'    => $discounted_taxes,
					'subtotal' => $taxes,
				);
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_disc_total']     = $discount_total;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_disc_total_tax'] = $discount_tax_total;
			}
		}

		// Calculate taxes for virtual product.
		foreach ( $cart->get_cart() as $cart_item_key => $cart_item ) {

			$_product = $cart_item['data'];

			if ( $_product->is_virtual() ) {

				$item_taxes[ $cart_item_key ]['line_subtotal']       = $cart_item['line_subtotal'];
				$item_taxes[ $cart_item_key ]['line_subtotal_tax']   = $cart_item['line_subtotal_tax'];
				$item_taxes[ $cart_item_key ]['line_total']          = $cart_item['line_total'];
				$item_taxes[ $cart_item_key ]['line_tax']            = $cart_item['line_tax'];
				$item_taxes[ $cart_item_key ]['line_disc_total']     = $cart_item['line_subtotal'] - $cart_item['line_total'];
				$item_taxes[ $cart_item_key ]['line_disc_total_tax'] = $cart_item['line_subtotal_tax'] - $cart_item['line_tax'];

				// Get tax rates total from cart contents if exists.
				if ( isset( $cart_item['line_tax_data']['total'] ) ) {

					$line_taxes = $cart_item['line_tax_data']['total'];

					// Manually calculate tax rates total.
				} else {

					$item_tax_rates = WC_Tax::get_rates( $_product->get_tax_class() );
					$line_taxes     = WC_Tax::calc_tax( $cart_item['line_total'], $item_tax_rates, false );

				}

				// Rounding the line taxes first before summing.
				$line_taxes = $this->round_line_taxes( $line_taxes );

				// Tax rows - merge the totals we just got.
				foreach ( array_keys( $cart_taxes + $line_taxes ) as $key ) {
					$cart_taxes[ $key ] = ( isset( $line_taxes[ $key ] ) ? $line_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
				}
			}
		}

		// Total up/round taxes and shipping taxes.
		if ( $cart->round_at_subtotal ) {
			$cart->tax_total = WC_Tax::get_tax_total( $cart_taxes );
		} else {
			$cart->tax_total = array_sum( $cart_taxes );
		}

		// Get discount tax discount data by calculating the discount with packages instead of cart.
		$coupon_discount_tax_totals = $this->calculate_coupon_discount_tax_amounts( $cart, $packages );
		$cart->set_discount_total( array_sum( $cart->get_coupon_discount_totals() ) );
		$cart->set_coupon_discount_tax_totals( $coupon_discount_tax_totals );

		$cart->set_cart_contents_taxes( array_map( 'WC_Tax::round', $cart_taxes ) );

		// Get total discount total tax by checking on 'line_disc_total_tax' array value.
		$cart_disc_total_tax = array_sum( array_column( $item_taxes, 'line_disc_total_tax' ) );

		// Set the new cart total data to the cart.
		$cart->set_discount_tax( $cart_disc_total_tax );
		$cart->set_subtotal_tax( array_sum( array_column( $item_taxes, 'line_subtotal_tax' ) ) );

		$discounts = array_sum( $cart->coupon_discount_totals ) + array_sum( $cart->coupon_discount_tax_totals );

		$cart->set_total( ( $cart->get_subtotal() + $cart->get_subtotal_tax() ) + $cart->get_shipping_total() + $old_shipping_tax_total - $discounts );
		$cart->set_total_tax( $cart->get_taxes_total() );

		if ( $merge ) {
			WC()->cart = $cart;
		}

		if ( $return_packages ) {
			return $packages;
		}

		// store the modified packages array.
		wcms_session_set( 'wcms_packages', $packages );
		// store the modified packages array to different session.
		wcms_session_set( 'wcms_packages_after_tax_calc', $packages );

		return $cart;
	}

	/**
	 * Apply extra data ( notes, dates, gifts ) for WC blocks.
	 * The data will be retrieved from the session.
	 *
	 * @param array   $packages  Cart packages.
	 * @param boolean $is_return Is return the package value or not.
	 *
	 * @return array.
	 */
	public function apply_extra_data_to_package( $packages, $is_return ) {
		$notes = wcms_session_get( 'wcms_package_notes' ) ?? array();
		$dates = wcms_session_get( 'wcms_delivery_dates' ) ?? array();
		$gifts = wcms_session_get( 'wcms_package_gifts' ) ?? array();

		foreach ( $packages as $idx => $package ) {
			if ( ! empty( $notes[ $idx ] ) && empty( $package['note'] ) ) {
				$package['note'] = esc_html( $notes[ $idx ] );
			}

			if ( ! empty( $dates[ $idx ] ) && empty( $package['date'] ) ) {
				$package['date'] = esc_html( $dates[ $idx ] );
			}

			if ( ! empty( $gifts[ $idx ] ) && empty( $package['gift'] ) ) {
				$package['gift'] = ( 'yes' === $gifts[ $idx ] );
			}

			$packages[ $idx ] = $package;
		}

		if ( true === $is_return ) {
			return $packages;
		}

		wcms_session_set( 'wcms_packages', $packages );
		wcms_session_set( 'wcms_packages_after_tax_calc', $packages );
	}

	/**
	 * Get the coupons from the cart.
	 *
	 * @param WC_Cart $cart Cart object.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function get_coupons_from_cart( $cart ) {
		$coupons = $cart->get_coupons();

		foreach ( $coupons as $coupon ) {
			switch ( $coupon->get_discount_type() ) {
				case 'fixed_product':
					$coupon->sort = 1;
					break;
				case 'percent':
					$coupon->sort = 2;
					break;
				case 'fixed_cart':
					$coupon->sort = 3;
					break;
				default:
					$coupon->sort = 0;
					break;
			}

			/**
			 * Allow plugins to override the default order.
			 *
			 * @param int       Current coupon order.
			 * @param WC_Coupon Coupon object.
			 *
			 * @since 3.3.23
			 */
			$coupon->sort = apply_filters( 'woocommerce_coupon_sort', $coupon->sort, $coupon );
		}

		uasort( $coupons, array( $this, 'sort_coupons_callback' ) );

		return $coupons;
	}

	/**
	 * Sort coupons so discounts apply consistently across installs.
	 *
	 * In order of priority;
	 *  - sort param
	 *  - usage restriction
	 *  - coupon value
	 *  - ID
	 *
	 * @param WC_Coupon $a Coupon object.
	 * @param WC_Coupon $b Coupon object.
	 *
	 * @since 3.6.34
	 * @return int
	 */
	public function sort_coupons_callback( $a, $b ) {
		if ( $a->sort === $b->sort ) {
			if ( $a->get_limit_usage_to_x_items() === $b->get_limit_usage_to_x_items() ) {
				if ( $a->get_amount() === $b->get_amount() ) {
					return $b->get_id() - $a->get_id();
				}
				return ( $a->get_amount() < $b->get_amount() ) ? -1 : 1;
			}
			return ( $a->get_limit_usage_to_x_items() < $b->get_limit_usage_to_x_items() ) ? -1 : 1;
		}
		return ( $a->sort < $b->sort ) ? -1 : 1;
	}

	/**
	 * Recalculate the discount tax rates based on package destination.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $packages Cart packages.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function calculate_coupon_discount_tax_amounts( $cart, $packages ) {
		$coupons                     = $this->get_coupons_from_cart( $cart );
		$coupon_discount_tax_amounts = array();

		// Calculate the line value.
		foreach ( $packages as $idx => $package ) {
			$items = array();

			foreach ( $package['contents'] as $key => $cart_item ) {
				$item                     = new stdClass();
				$item->key                = $key;
				$item->object             = $cart_item;
				$item->product            = $cart_item['data'];
				$item->quantity           = $cart_item['quantity'];
				$item->taxable            = 'taxable' === $cart_item['data']->get_tax_status();
				$item->price_includes_tax = false;
				$item->price              = wc_add_number_precision_deep( $cart_item['line_subtotal'] );
				$item->tax_rates          = $this->get_item_tax_rates( $item, $package );

				$items[ $key ] = $item;
			}

			$discounts = new WC_Discounts( $cart );

			// Set items directly so the discounts class can see any tax adjustments made thus far using subtotals.
			$discounts->set_items( $items );

			foreach ( $coupons as $coupon ) {
				$discounts->apply_coupon( $coupon );
			}

			$coupon_discount_amounts = $discounts->get_discounts_by_coupon( true );

			// See how much tax was 'discounted' per item and per coupon.
			foreach ( $discounts->get_discounts( true ) as $coupon_code => $coupon_discounts ) {
				$coupon_discount_tax_amounts[ $coupon_code ] = isset( $coupon_discount_tax_amounts[ $coupon_code ] ) ? $coupon_discount_tax_amounts[ $coupon_code ] : 0;

				foreach ( $coupon_discounts as $item_key => $coupon_discount ) {
					$item = $items[ $item_key ];

					if ( $item->product->is_taxable() ) {
						// Item subtotals were sent, so set 3rd param.
						$item_tax = array_sum( WC_Tax::calc_tax( $coupon_discount, $item->tax_rates, $item->price_includes_tax ) );

						// Sum total tax.
						$coupon_discount_tax_amounts[ $coupon_code ] += $item_tax;

						// Remove tax from discount total.
						if ( $item->price_includes_tax ) {
							$coupon_discount_amounts[ $coupon_code ] -= $item_tax;
						}
					}
				}
			}
		}

		return wc_remove_number_precision_deep( $coupon_discount_tax_amounts );
	}

	/**
	 * Apply rounding to an array of taxes before summing.
	 *
	 * @param array $item_taxes Item taxes.
	 * @return array
	 */
	public function round_line_taxes( $item_taxes ) {

		foreach ( $item_taxes as $key => $item_tax ) {

			if ( 'yes' !== get_option( 'woocommerce_tax_round_at_subtotal' ) ) {

				$item_tax           = wc_add_number_precision( $item_tax, false );
				$item_tax           = wc_round_tax_total( $item_tax, 0 );
				$item_tax           = wc_remove_number_precision( $item_tax );
				$item_taxes[ $key ] = $item_tax;

			}
		}

		return $item_taxes;
	}

	/**
	 * Apply rounding to an array of shipping taxes before summing.
	 *
	 * @param array $shipping_taxes Shipping taxes.
	 *
	 * @return array
	 */
	public function round_shipping_taxes( $shipping_taxes ) {

		$shipping_taxes = wc_add_number_precision_deep( $shipping_taxes, false );
		$shipping_taxes = array_map( array( $this, 'round_item_subtotal' ), $shipping_taxes );
		$shipping_taxes = wc_remove_number_precision_deep( $shipping_taxes );

		return $shipping_taxes;
	}

	/**
	 * Apply rounding to item subtotal before summing.
	 *
	 * @since 3.9.0
	 * @param float $value Item subtotal value.
	 * @return float
	 */
	public function round_item_subtotal( $value ) {
		if ( 'yes' !== get_option( 'woocommerce_tax_round_at_subtotal' ) ) {
			$value = NumberUtil::round( $value );
		}
		return $value;
	}

	/**
	 * This method manipulate the subtotal item value for price with tax included.
	 *
	 * @param float  $product_subtotal Product subtotal in cart.
	 * @param array  $cart_item Cart item.
	 * @param string $cart_item_key Cart item key.
	 *
	 * @return float
	 */
	public function subtotal_item_include_taxes( $product_subtotal, $cart_item, $cart_item_key ) {
		$packages     = wcms_session_isset( 'wcms_packages_after_tax_calc' ) ? wcms_session_get( 'wcms_packages_after_tax_calc' ) : wcms_session_get( 'wcms_packages' );
		$tax_based_on = get_option( 'woocommerce_tax_based_on', 'billing' );

		// only process subtotal if multishipping is being used.
		if ( ( is_array( $packages ) && count( $packages ) <= 1 ) || 'shipping' !== $tax_based_on ) {
			return $product_subtotal;
		}

		// Value that needs to be updated.
		$package_item = array(
			'line_total'        => 0,
			'line_tax'          => 0,
			'line_subtotal'     => 0,
			'line_subtotal_tax' => 0,
		);

		if ( is_array( $packages ) ) {
			// Calculate the line value.
			$number_of_package = count( $packages );
			for ( $i = 0; $i < $number_of_package; $i++ ) {
				if ( isset( $packages[ $i ]['contents'][ $cart_item_key ] ) ) {
					$new_cart_item                      = $packages[ $i ]['contents'][ $cart_item_key ];
					$package_item['line_total']        += isset( $new_cart_item['line_total'] ) ? $new_cart_item['line_total'] : 0;
					$package_item['line_tax']          += isset( $new_cart_item['line_tax'] ) ? $new_cart_item['line_tax'] : 0;
					$package_item['line_subtotal']     += isset( $new_cart_item['line_subtotal'] ) ? $new_cart_item['line_subtotal'] : 0;
					$package_item['line_subtotal_tax'] += isset( $new_cart_item['line_subtotal_tax'] ) ? $new_cart_item['line_subtotal_tax'] : 0;
				}
			}
		}

		// Replace the cart item with the modified one.
		if ( isset( $new_cart_item ) ) {
			$cart_item = $new_cart_item;

			foreach ( $package_item as $key => $value ) {
				$cart_item[ $key ] = $value;
			}
		}

		$subtotal = $this->wcms->get_cart_item_subtotal( $cart_item );
		$taxable  = $cart_item['data']->is_taxable();

		if ( $taxable && ( $cart_item['line_total'] + $cart_item['line_tax'] ) !== $subtotal ) {

			if ( 'excl' === WC()->cart->get_tax_price_display_mode() ) {
				$row_price = $cart_item['line_subtotal'];

				$product_subtotal = wc_price( $row_price );

				if ( WC()->cart->prices_include_tax && $cart_item['line_tax'] > 0 ) {
					$product_subtotal .= ' <small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>';
				}
			} else {
				$row_price = $cart_item['line_subtotal'] + $cart_item['line_subtotal_tax'];

				$product_subtotal = wc_price( $row_price );

				if ( ! WC()->cart->prices_include_tax && $cart_item['line_tax'] > 0 ) {
					$product_subtotal .= ' <small class="tax_label">' . WC()->countries->inc_tax_or_vat() . '</small>';
				}
			}
		}

		return $product_subtotal;
	}

	/**
	 * This method get item tax rates based on package destination.
	 *
	 * @param array $item Cart item.
	 * @param array $package Cart package.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function get_item_tax_rates( $item, $package ) {

		$customer = new WC_Customer();

		$customer_country  = $package['destination']['country'];
		$customer_state    = $package['destination']['state'];
		$customer_postcode = $package['destination']['postcode'];
		$customer_city     = $package['destination']['city'];

		$customer->set_billing_location( $customer_country, $customer_state, $customer_postcode, $customer_city );
		$customer->set_shipping_location( $customer_country, $customer_state, $customer_postcode, $customer_city );

		$item_tax_rates = WC_Tax::get_rates( $item->product->get_tax_class(), $customer );

		return $item_tax_rates;
	}

	/**
	 * Create order shipments.
	 *
	 * @param int|WC_Order $new_order Either Order ID or Order object.
	 */
	public function create_order_shipments( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		$order_id = $order->get_id();

		$multishipping = $order->get_meta( '_multiple_shipping' );
		$created       = $order->get_meta( '_shipments_created' );
		$packages      = $order->get_meta( '_wcms_packages' );
		$shipment      = $this->wcms->shipments;

		if ( 'yes' !== $multishipping || 'yes' === $created ) {
			return;
		}

		foreach ( $packages as $i => $package ) {
			$shipment->create_from_package( $package, $i, $order_id );
		}

		$order->update_meta_data( '_shipments_created', 'yes' );
		$order->save();
	}

	/**
	 * Prevent updating customer data when the cart has multi shipping.
	 *
	 * @param boolean $update Update.
	 *
	 * @return boolean
	 */
	public function prevent_customer_data_update( $update ) {
		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return false;
		}

		return $update;
	}

	/**
	 * Store shipping address on certain condition.
	 *
	 * @param int|WC_Customer $customer Either customer object or customer ID.
	 */
	public function maybe_store_shipping_address( $customer ) {
		$customer = ( $customer instanceof WC_Customer ) ? $customer : new WC_Customer( $customer );

		if ( ! $customer ) {
			return;
		}

		$customer_id = $customer->get_id();

		if ( ! $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		$checkout = WC()->checkout;

		// Check if we should update customer data.
		remove_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );

		/**
		 * Filter to be able updating the customer data.
		 *
		 * @param boolean     Should update customer data?
		 * @param WC_Checkout Checkout object.
		 *
		 * @since 3.3.23
		 */
		$update_customer_data = apply_filters( 'woocommerce_checkout_update_customer_data', true, $checkout );
		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );

		if ( $update_customer_data ) {

			// Save billing address.
			if ( $checkout->checkout_fields['billing'] ) {
				foreach ( array_keys( $checkout->checkout_fields['billing'] ) as $field ) {
					$field_name = str_replace( 'billing_', '', $field );
					update_user_meta( $customer_id, 'billing_' . $field_name, $checkout->get_posted_address_data( $field_name ) );
				}
			}

			// Get user addresses.
			$addresses = $this->wcms->address_book->get_user_addresses( $customer_id );

			// Add guest addresses (needed for when account is created at checkout).
			$guest_addresses = ( wcms_session_isset( 'user_addresses' ) ) ? wcms_session_get( 'user_addresses' ) : array();
			$addresses       = array_merge( $addresses, $guest_addresses );

			$filtered_addresses = array_filter(
				$addresses,
				function ( $address ) {
					return ! empty( $address['default_address'] );
				}
			);

			if ( count( $filtered_addresses ) > 0 ) {
				$default_address = array_shift( $filtered_addresses );
				wcms_session_set( 'user_default_address', $default_address );
			}

			$this->wcms->address_book->save_user_addresses( $customer_id, $addresses );
		}
	}

	/**
	 * Set the shipping methods from WCMS session when shipping rate is selected from store API.
	 *
	 * @param string|null      $package_id The sanitized ID of the package being updated. Null if all packages are being updated.
	 * @param string           $rate_id The sanitized chosen rate ID for the package.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function select_shipping_rate_on_store_api( $package_id, $rate_id, $request ) {
		$this->set_shipping_methods_on_block();
	}

	/**
	 * Set the shipping methods from WCMS session when customer is being updated from store API.
	 *
	 * @param \WC_Customer     $customer Customer object.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function update_customer_on_store_api( $customer, $request ) {
		$this->set_shipping_methods_on_block();
	}

	/**
	 * Set the shipping methods on WCMS session for WC blocks.
	 */
	public function set_shipping_methods_on_block() {
		// Update chosen shipping methods to match with WooCommerce session variable.
		$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );

		if ( ! is_array( $chosen_shipping_methods ) || empty( $chosen_shipping_methods ) ) {
			return;
		}

		// Will always set new session for shipping methods.
		// It's being done this way because checkout blocks is already compatible with multiple package.
		// And the plugin can just grab the chosen shipping methods and save it into shipping methods wcms session.
		$methods = array();
		foreach ( $chosen_shipping_methods as $key => $method ) {
			if ( 'undefined' === $key ) {
				continue;
			}

			$methods[ $key ] = array(
				'id'    => $method,
				'label' => $method,
			);
		}

		wcms_session_set( 'shipping_methods', $methods );
	}

	/**
	 * Restore default shipping address.
	 *
	 * @param int|WC_Order $new_order Either order object or order ID.
	 */
	public function restore_customer_default_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			wcms_session_delete( 'user_default_address' );
			return;
		}

		$multishipping = $order->get_meta( '_multiple_shipping' );

		if ( 'yes' !== $multishipping ) {
			wcms_session_delete( 'user_default_address' );
			return;
		}

		// Use customer session if it's a guest and use customer data if it's a user.
		$customer_id = $order->get_customer_id();
		$customer    = ( 0 === $customer_id ) ? WC()->customer : new WC_Customer( $customer_id );

		$this->restore_default_shipping_address( $customer );
		wcms_session_delete( 'user_default_address' );
	}

	/**
	 * Force to use billing address when WCMS is active.
	 * It needs to force it in order to hide the shipping address on WC Blocks.
	 *
	 * @param mixed $value  Option value.
	 * @param mixed $option Option.
	 * @param mixed $default_value Option default value.
	 */
	public function force_use_billing_address( $value, $option, $default_value ) {
		// force billing address only on cart blocks or checkout blocks.
		if ( $this->wcms->cart->cart_has_multi_shipping() && wc_get_page_id( 'myaccount' ) !== get_the_ID() ) {
			return 'billing_only';
		}

		return $value;
	}

	/**
	 * Calculate order taxes only for WC blocks.
	 *
	 * @param boolean  $and_taxes And calculate taxes.
	 * @param WC_Order $order Order object.
	 *
	 * @throws RouteException When cart cannot be loaded.
	 */
	public function calculate_order_taxes_for_wc_blocks( $and_taxes, $order ) {
		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return;
		}

		$multishipping = $order->get_meta( '_multiple_shipping' );
		$wcms_packages = $order->get_meta( '_wcms_packages' );

		if ( 'yes' !== $multishipping || ! ( is_array( $wcms_packages ) && count( $wcms_packages ) > 1 ) ) {
			return;
		}

		$cart = WC()->cart;

		if ( ! $cart || ! $cart instanceof WC_Cart ) {
			throw new RouteException( 'woocommerce_rest_cart_error', __( 'Unable to retrieve cart.', 'woocommerce' ), 500 );
		}

		$cart_hashes = array(
			'line_items' => $cart->get_cart_hash(),
			'shipping'   => md5( wp_json_encode( $cart->shipping_methods ) ),
			'fees'       => md5( wp_json_encode( $cart->get_fees() ) ),
			'coupons'    => md5( wp_json_encode( $cart->get_applied_coupons() ) ),
			'taxes'      => md5( wp_json_encode( $cart->get_taxes() ) ),
		);

		if ( $order->get_cart_hash() !== $cart_hashes['line_items'] ) {
			$order->set_cart_hash( $cart_hashes['line_items'] );
			$order->remove_order_items( 'line_item' );
			WC()->checkout->create_order_line_items( $order, $cart );
		}

		if ( $order->get_meta_data( '_shipping_hash' ) !== $cart_hashes['shipping'] ) {
			$order->update_meta_data( '_shipping_hash', $cart_hashes['shipping'] );
			$order->remove_order_items( 'shipping' );
			WC()->checkout->create_order_shipping_lines( $order, WC()->session->get( 'chosen_shipping_methods' ), WC()->shipping()->get_packages() );
		}

		if ( $order->get_meta_data( '_coupons_hash' ) !== $cart_hashes['coupons'] ) {
			$order->remove_order_items( 'coupon' );
			$order->update_meta_data( '_coupons_hash', $cart_hashes['coupons'] );
			WC()->checkout->create_order_coupon_lines( $order, $cart );
		}

		if ( $order->get_meta_data( '_fees_hash' ) !== $cart_hashes['fees'] ) {
			$order->update_meta_data( '_fees_hash', $cart_hashes['fees'] );
			$order->remove_order_items( 'fee' );
			WC()->checkout->create_order_fee_lines( $order, $cart );
		}

		if ( $order->get_meta_data( '_taxes_hash' ) !== $cart_hashes['taxes'] ) {
			$order->update_meta_data( '_taxes_hash', $cart_hashes['taxes'] );
			$order->remove_order_items( 'tax' );
			WC()->checkout->create_order_tax_lines( $order, $cart );
		}

		$cart_tax          = floatval( $cart->get_cart_contents_tax() ) + floatval( $cart->get_fee_tax() );
		$cart_shipping_tax = floatval( $cart->get_shipping_tax() );
		
		$order->set_cart_tax( $cart_tax );
		$order->set_shipping_tax( $cart_shipping_tax );
		
		$cart_total = $cart->get_total( 'edit' );
		$order->set_total( $cart_total );
	}

	/**
	 * Clear session for old cart/checkout page.
	 */
	public function legacy_clear_session() {
		$this->wcms->clear_session();
	}

	/**
	 * Clear session for WC cart/checkout blocks.
	 */
	public function blocks_clear_session() {
		unset( wc()->session->cart_item_addresses );
		unset( wc()->session->wcms_item_addresses );
		unset( wc()->session->cart_address_sigs );
		unset( wc()->session->address_relationships );
		unset( wc()->session->shipping_methods );
		unset( wc()->session->wcms_original_cart );
		unset( wc()->session->wcms_packages );
		unset( wc()->session->wcms_packages_after_tax_calc );
		unset( wc()->session->wcms_item_delivery_dates );
		unset( wc()->session->user_default_address );
		unset( wc()->session->wcms_package_notes );
		unset( wc()->session->wcms_delivery_dates );
		unset( wc()->session->wcms_package_gifts );
	}

	/**
	 * Reset multiple shipping address session.
	 */
	public function reset_multiple_shipping_address() {
		$nonce = isset( $_GET['nonce'] ) ? sanitize_text_field( wp_unslash( $_GET['nonce'] ) ) : '';
		if ( empty( $_GET['wcms_reset_address'] ) || ! wp_verify_nonce( $nonce, 'wcms_reset_address_security' ) ) {
			return;
		}

		$customer = WC()->customer;

		if ( $customer instanceof WC_Customer && 0 === $customer->get_id() ) {
			$this->restore_default_shipping_address( $customer );
		}

		wcms_session_delete( 'cart_item_addresses' );
		wcms_session_delete( 'cart_address_sigs' );
		wcms_session_delete( 'address_relationships' );
		wcms_session_delete( 'shipping_methods' );
		wcms_session_delete( 'wcms_original_cart' );
		wcms_session_delete( 'wcms_item_addresses' );
		wcms_session_delete( 'user_default_address' );
		wcms_session_delete( 'wcms_package_notes' );
		wcms_session_delete( 'wcms_delivery_dates' );
		wcms_session_delete( 'wcms_package_gifts' );
	}

	/**
	 * Restore default shipping address.
	 *
	 * @param WC_Customer $customer Customer object.
	 */
	public function restore_default_shipping_address( $customer ) {
		// Get user addresses.
		$user_default_address = ( wcms_session_isset( 'user_default_address' ) ) ? wcms_session_get( 'user_default_address' ) : array();
		$legacy_address_keys  = array(
			'address_2',
			'city',
			'state',
			'postcode',
			'country',
		);

		foreach ( $user_default_address as $key => $value ) {
			// Use setters where available.
			$key = in_array( $key, $legacy_address_keys ) ? 'shipping_' . $key : $key;

			if ( is_callable( array( $customer, "set_{$key}" ) ) ) {
				$customer->{"set_{$key}"}( $value );

				// Store custom fields prefixed with wither billing_.
			} elseif ( 0 === stripos( $key, 'billing_' ) ) {
				$customer->update_meta_data( $key, $value );
			}
		}

		$customer->save();
	}
}
