<?php
/**
 * Compatibility for Customer/Order CSV export plugin.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Plugin compatibility with `woocommerce-customer-order-csv-export`.
 */
class WC_MS_Customer_Order_Csv_Export {

	/**
	 * Plugin reference.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_filter( 'wc_customer_order_csv_export_order_headers', array( $this, 'modify_column_headers' ), 10, 1 );

		add_filter( 'wc_customer_order_export_csv_order_line_item', array( $this, 'sv_wc_csv_export_add_cart_id_to_order_line_item' ), 10, 2 );
		add_filter( 'wc_customer_order_export_csv_order_row_one_row_per_item', array( $this, 'sv_wc_csv_export_add_package_multiple_address' ), 10, 2 );
	}

	/**
	 * Method for adding an additional header for S2MA.
	 *
	 * @param  array $column_headers Existing column headers.
	 *
	 * @return array
	 *
	 * @since 3.6.0
	 */
	public function modify_column_headers( $column_headers ) {
		$column_headers['wcms'] = __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' );
		return $column_headers;
	}

	/**
	 * Adds the line item's cart key to the line item data for use by the one row per item
	 * filter.
	 *
	 * @param array                 $line_item {
	 *       line item data in key => value format
	 *       the keys are for convenience and not necessarily used for exporting. Make
	 *       sure to prefix the values with the desired line item entry name
	 * }.
	 * @param WC_Order_Item_Product $item WC order item data.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	public function sv_wc_csv_export_add_cart_id_to_order_line_item( $line_item, $item ) {
		$line_item['cart_key'] = wc_get_order_item_meta( $item->get_id(), '_wcms_cart_key', true );

		return $line_item;
	}

	/**
	 * Adds the corresponding package address to each line item.
	 *
	 * @param array $order_data array of order data.
	 * @param array $item array of item data.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	public function sv_wc_csv_export_add_package_multiple_address( $order_data, $item ) {
		$order_id = $order_data['order_id'];
		$order    = wc_get_order( $order_id );

		if ( ! $order ) {
			return $order_data;
		}

		$shipping_addresses = $order->get_meta( '_shipping_addresses' );

		// Quit if it's not multiple addresses.
		if ( empty( $shipping_addresses ) ) {
			return $order_data;
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $this->find_package( $packages, $item['cart_key'] );

		// Quit if destination does not exist in the package.
		if ( empty( $package['destination'] ) ) {
			return $order_data;
		}

		$address  = wcms_get_address( $package['destination'] );

		foreach ( $address as $addr_key => $addr_val ) {
			// Only add shipping address to csv.
			if ( isset( $order_data[ $addr_key ] ) && false !== strpos( $addr_key, 'shipping_' ) ) {
				$order_data[ $addr_key ] = $addr_val;
			}
		}

		$address = implode(
			'|',
			array_map(
				function ( $key, $value ) {
					return sprintf( '%s:%s', $key, $value );
				},
				array_keys( $address ),
				$address
			)
		);

		$order_data['wcms'] = $address;

		return $order_data;
	}

	/**
	 * Helper function to check the export format.
	 *
	 * @param \WC_Customer_Order_CSV_Export_Generator $csv_generator the generator instance.
	 *
	 * @return bool - true if this is a one row per item format
	 *
	 * @since 3.6.0
	 */
	public function is_one_row( $csv_generator ) {
		$one_row_per_item = false;
		if ( version_compare( wc_customer_order_csv_export()->get_version(), '4.0.0', '<' ) ) {
			// pre 4.0 compatibility.
			$one_row_per_item = ( 'default_one_row_per_item' === $csv_generator->order_format || 'legacy_one_row_per_item' === $csv_generator->order_format );
		} elseif ( isset( $csv_generator->format_definition ) ) {
			// post 4.0 (requires 4.0.3+).
			$one_row_per_item = 'item' === $csv_generator->format_definition['row_type'];
		}
		return $one_row_per_item;
	}

	/**
	 * Finds the package with the corresponding cart key.
	 *
	 * @param array  $packages Cart package.
	 * @param string $cart_key Cart key.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	private function find_package( $packages, $cart_key ) {
		foreach ( $packages as $package ) {
			if ( array_key_exists( $cart_key, $package['contents'] ) ) {
				return $package;
			}
		}

		return null;
	}
}
