<?php
/**
 * Class WC_MS_Order file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_MS_Order class.
 */
class WC_MS_Order {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// Update package status.
		add_action( 'wp_ajax_wcms_update_package_status', array( $this, 'update_package_status' ) );
		add_action( 'woocommerce_order_status_completed', array( $this, 'update_package_on_completed_order' ) );

		// Order preview parameter override.
		add_filter( 'woocommerce_admin_order_preview_get_order_details', array( $this, 'update_preview_order_details' ), 20, 2 );

		// Order page shipping address override.
		add_action( 'woocommerce_admin_order_data_after_shipping_address', array( $this, 'override_order_shipping_address' ) );

		// Compatibility action for displaying order shipping packages.
		add_action( 'wcms_order_shipping_packages_table', array( $this, 'display_order_shipping_addresses' ), 10, 3 );

		add_action( 'manage_shop_order_posts_custom_column', array( $this, 'show_multiple_addresses_line' ), 1, 2 );
		add_action( 'manage_woocommerce_page_wc-orders_custom_column', array( $this, 'show_multiple_addresses_line' ), 1, 2 );

		// meta box.
		add_action( 'add_meta_boxes', array( $this, 'order_meta_box' ), 10, 2 );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_css' ) );
		add_action( 'woocommerce_process_shop_order_meta', array( $this, 'update_order_addresses' ), 10, 1 );
		add_action( 'woocommerce_saved_order_items', array( $this, 'update_order_taxes' ), 1, 2 );

		add_filter( 'woocommerce_order_get_items', array( $this, 'order_item_taxes' ), 30, 2 );

		// Hide metadata in order line items.
		add_filter( 'woocommerce_hidden_order_itemmeta', array( $this, 'hidden_order_item_meta' ) );

		// WC PIP.
		add_filter( 'woocommerce_pip_template_body', array( $this, 'pip_template_body' ), 10, 2 );

		add_filter( 'woocommerce_order_needs_shipping_address', array( $this, 'manipulate_needs_shipping' ), 10, 3 );
	}

	/**
	 * Update package status.
	 *
	 * @return void
	 */
	public function update_package_status() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, 'wcms_update_package_status_save' ) ) {
			die( esc_html__( 'Permission denied: Security check failed', 'woocommerce-shipping-multiple-addresses' ) );
		}

		if ( ! current_user_can( 'manage_woocommerce' ) ) { // phpcs:ignore -- WooCommerce capability.
			exit;
		}

		$pkg_idx  = isset( $_POST['package'] ) ? sanitize_text_field( wp_unslash( $_POST['package'] ) ) : '';
		$order_id = isset( $_POST['order'] ) ? intval( $_POST['order'] ) : 0;
		$order    = wc_get_order( $order_id );
		$status   = '';
		if ( $order instanceof WC_Order ) {
			$post_status = isset( $_POST['status'] ) ? sanitize_text_field( wp_unslash( $_POST['status'] ) ) : '';
			$packages    = $order->get_meta( '_wcms_packages' );
			$email       = isset( $_POST['email'] ) && ( 'true' === $_POST['email'] || true === $_POST['email'] );

			foreach ( $packages as $x => $package ) {
				if ( '' !== $pkg_idx && intval( $pkg_idx ) === $x ) {
					$packages[ $x ]['status'] = $post_status;

					if ( 'Completed' === $post_status && $email ) {
						self::send_package_email( $order, $pkg_idx );
					}

					break;
				}
			}

			$order->update_meta_data( '_wcms_packages', $packages );
			$order->save();

			$status = $post_status;
		}

		die( esc_html( $status ) );
	}

	/**
	 * Update package status on completed order.
	 *
	 * @param int $order_id Order ID.
	 * @return void
	 */
	public function update_package_on_completed_order( $order_id ) {
		$order = wc_get_order( $order_id );

		if ( ! $order ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( $packages ) {
				foreach ( $packages as $x => $package ) {
					$packages[ $x ]['status'] = 'Completed';
				}

				$order->update_meta_data( '_wcms_packages', $packages );
				$order->save();
			}
		}
	}

	/**
	 * Manipulate the order details value if it has multiple addresses.
	 *
	 * @param array    $order_details Order details.
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function update_preview_order_details( $order_details, $order ) {
		if ( is_callable( array( $order, 'get_meta' ) ) ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( is_array( $packages ) && 1 < count( $packages ) ) {
				$order_details['formatted_shipping_address'] = $this->generate_formatted_multiple_addresses( $order );
			}
		}

		return $order_details;
	}

	/**
	 * Generate address map url for package destination.
	 *
	 * @param array    $package Package.
	 * @param WC_Order $order   Order object.
	 *
	 * @return string
	 */
	public static function generate_address_map_url( $package, $order ) {

		$address_map_url = '';

		if ( isset( $package['destination'] ) && is_array( $package['destination'] ) ) {
			$destination        = $package['destination'];
			$destination_pieces = implode(
				', ',
				array_filter(
					array(
						$destination['address_1'],
						$destination['address_2'],
						$destination['city'],
						$destination['state'],
						$destination['postcode'],
						$destination['country'],
					)
				)
			);

			/**
			 * Allow modifying shipping address map URL.
			 *
			 * @param string $url Map URL.
			 * @param WC_Order $order Order object.
			 * @return string Map URL.
			 *
			 * @since 3.6.29
			 */
			$address_map_url = apply_filters( 'woocommerce_shipping_address_map_url', 'https://maps.google.com/maps?&q=' . rawurlencode( $destination_pieces ) . '&z=16', $order );
		}

		return $address_map_url;
	}

	/**
	 * Generate formatted shipping address for multiple addresses.
	 *
	 * @param WC_Order $order   Order object.
	 *
	 * @return string
	 */
	public function generate_formatted_multiple_addresses( $order ) {

		if ( ! $order || ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return;
		}

		$item_addresses_html = '';

		ob_start();
		?>

		<div class="item-addresses-holder">
			<?php foreach ( $packages as $x => $package ) { ?>

				<?php $address_map_url = self::generate_address_map_url( $package, $order ); ?>

				<div class="item-address-box package-<?php echo esc_attr( $x ); ?>-box" style="margin-bottom:20px;">
					<a href="<?php echo esc_url( $address_map_url ); ?>" target="_blank">
						<?php self::display_shipping_package_address( $order, $package, $x, false ); ?>
					</a>
				</div>

			<?php } ?>
		</div>

		<?php
		$item_addresses_html .= ob_get_contents();
		ob_end_clean();

		return $item_addresses_html;
	}

	/**
	 * Order page shipping address override.
	 *
	 * @param \WC_Order $order Order object.
	 * @return void
	 */
	public function override_order_shipping_address( $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages  = $order->get_meta( '_wcms_packages' );
		$multiship = $order->get_meta( '_multiple_shipping' );

		if ( ( ! $order->get_formatted_shipping_address() && ( is_array( $packages ) && count( $packages ) > 1 ) ) || 'yes' === $multiship ) :
			?>
			<script type="text/javascript">
				jQuery( document ).ready( function( $ ) {
					var $order_data = $( 'div.order_data_column' ).eq( 2 );
					$order_data.find( 'a.edit_address' ).remove();
					$order_data.find( 'div.address' ).prepend( '<a href="#wc_multiple_shipping"><?php esc_html_e( 'Ships to multiple addresses', 'woocommerce-shipping-multiple-addresses' ); ?></a>' );
					$order_data.find( 'div.address p' ).not( '.order_note' ).remove();
				} );
			</script>
			<?php
		endif;
	}

	/**
	 * Get shipping method name for the current package.
	 *
	 * @param int   $idx Index of the package.
	 * @param array $methods Array of shipping method.
	 * @param array $available_methods Array of available method.
	 *
	 * @return string
	 */
	public function get_shipping_method_name( $idx, $methods, $available_methods ) {
		$method = $methods[ $idx ]['label'];

		foreach ( $available_methods as $ship_method ) {
			if ( $ship_method->get_method_id() . ':' . $ship_method->get_instance_id() === $methods[ $idx ]['id']
				|| $ship_method->get_method_id() === $methods[ $idx ]['id']
			) {
				$method = $ship_method->get_name();
				break;
			}
		}

		return $method;
	}

	/**
	 * Get shipping address data from cart package.
	 *
	 * @param array $package Array of information about the package.
	 *
	 * @return string
	 */
	public function get_package_shipping_address( $package ) {
		return ! empty( $package['destination'] ) ? WC()->countries->get_formatted_address( $package['destination'] ) : '';
	}

	/**
	 * Get products out of the cart package.
	 *
	 * @param array    $products Array of products.
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_package_products( $products, $order ) {

		$package_items  = $order->get_meta( '_packages_item_ids' );
		$order_items    = $order->get_items();
		$cart_item_keys = $this->get_cart_item_keys( $order );

		$product_infos = array();

		foreach ( $products as $i => $product ) {

			// Get a matching order item.
			$item = false;
			if ( ! empty( $product['cart_key'] ) && ! empty( $cart_item_keys[ $product['cart_key'] ] ) && isset( $order_items[ $cart_item_keys[ $product['cart_key'] ] ] ) ) {
				$item = $order_items[ $cart_item_keys[ $product['cart_key'] ] ];
			} elseif ( ! empty( $package_items[ $i ] ) && isset( $order_items[ $package_items[ $i ] ] ) ) {
				// Fallback for items stored before WC 3.0.
				$item = $order_items[ $package_items[ $i ] ];
			}

			// Get item name and meta.
			if ( empty( $item ) ) {
				$id = empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'];

				/**
				 * Allow plugins to modify product title.
				 *
				 * @param string     Product title.
				 * @param WC_Product Product object.
				 *
				 * @since 3.3
				 */
				$name = apply_filters( 'wcms_product_title', get_the_title( $id ), $product );

				/**
				 * Allow plugins to modify package item meta.
				 *
				 * @param mix        Item meta.
				 * @param WC_Product Product object.
				 *
				 * @since 3.3
				 */
				$meta = apply_filters( 'wcms_package_item_meta', self::get_item_meta( $product ), $product );
			} else {
				$name = is_callable( array( $item, 'get_name' ) ) ? $item->get_name() : get_the_title( empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'] );

				/**
				 * Allow plugins to modify product title.
				 *
				 * @param string     Product title.
				 * @param WC_Product Product object.
				 * @param string     Item meta HTML.
				 *
				 * @since 3.3
				 */
				$name = apply_filters( 'wcms_product_title', $name, $product, $item );
				$meta = wc_display_item_meta( $item, array( 'echo' => false ) );

				/**
				 * Allow plugins to modify package item meta.
				 *
				 * @param mix        Item meta.
				 * @param WC_Product Product object.
				 * @param string     Item meta HTML.
				 *
				 * @since 3.3
				 */
				$meta = apply_filters( 'wcms_package_item_meta', $meta, $product, $item );
			}

			$product_infos[] = array(
				'name'    => $name,
				'qty'     => $product['quantity'],
				'meta'    => $meta,
				'product' => $product,
			);
		}

		return $product_infos;
	}

	/**
	 * Get all the order items (and generate a unique key for each)
	 *
	 * @param WC_Order $order WC Order object.
	 *
	 * @return array
	 */
	public function get_cart_item_keys( $order ) {
		$order_items    = $order->get_items();
		$cart_item_keys = array();
		foreach ( $order_items as $item_id => $item ) {
			if ( ! empty( $item['wcms_cart_key'] ) ) {
				$cart_item_keys[ $item['wcms_cart_key'] ] = $item_id;
			}
		}

		return $cart_item_keys;
	}

	/**
	 * Display shipping address in email.
	 *
	 * @param WC_Order $order Current order.
	 * @param bool     $email Flag to check whether the display is for email or not.
	 * @param bool     $plain_text Flag to check if the email is using plain text or not.
	 *
	 * @return void
	 */
	public function display_order_shipping_addresses( $order, $email = false, $plain_text = false ) {
		$allowed_html = array(
			'a'      => array(
				'href'  => array(),
				'title' => array(),
			),
			'br'     => array(),
			'em'     => array(),
			'strong' => array(),
		);

		if ( $order instanceof WC_Order ) {
			$order_id = $order->get_id();
		} else {
			$order_id = $order;
			$order    = wc_get_order( $order );
		}

		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		/**
		 * Allow other plugins to unlist the order item addresses.
		 *
		 * @param boolean Flag to unlist the order item addresses. Set `false` to unlist.
		 * @param int     Order ID.
		 *
		 * @since 3.3
		 */
		if ( false === apply_filters( 'wcms_list_order_item_addresses', true, $order_id ) ) {
			return;
		}

		$methods           = $order->get_meta( '_shipping_methods' );
		$packages          = $order->get_meta( '_wcms_packages' );
		$available_methods = $order->get_shipping_methods();

		if ( empty( $packages ) || ! is_array( $packages ) || 1 === count( $packages ) ) {
			return;
		}

		if ( $plain_text && $email ) {
			$this->display_order_shipping_addresses_plain_email( $packages, $order );
			return;
		}

		// Get all the order items (and generate a unique key for each).
		$cart_item_keys = $this->get_cart_item_keys( $order );

		if ( $email ) {
			$table_style = 'width: 100%; border: 1px solid #eee;';
			$th_style    = 'text-align:left; border: 1px solid #eee;';
			$td_style    = 'text-align:left; vertical-align:middle; border: 1px solid #eee;';
		} else {
			$table_style = '';
			$th_style    = '';
			$td_style    = '';
		}
		?>

		<h2><strong><?php esc_html_e( 'Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></strong></h2>
		<table class="shop_table shipping_packages" cellspacing="0" cellpadding="6" style="<?php echo esc_attr( $table_style ); ?>">
			<thead>
				<tr>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Products', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Address', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					<?php
					/**
					 * Allow plugins to add table head column.
					 *
					 * @since 3.3
					 */
					do_action( 'wc_ms_shop_table_head' );
					?>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Notes', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				</tr>
			</thead>
			<tbody>

		<?php

		foreach ( $packages as $x => $package ) {
			$products      = $package['contents'];
			$method        = $this->get_shipping_method_name( $x, $methods, $available_methods );
			$address       = $this->get_package_shipping_address( $package );
			$product_infos = $this->get_package_products( $products, $order );

			// Products.
			echo '<tr><td style="' . esc_attr( $td_style ) . '"><ul>';

			foreach ( $product_infos as $i => $info ) {
				$info_text = $info['name'] . ' &times; ' . $info['qty'];

				if ( ! empty( $info['meta'] ) ) {
					$info_text .= '<br />' . $info['meta'];
				}

				echo '<li>' . wp_kses( $info_text, $allowed_html ) . '</li>';
			}

			echo '</ul></td>';

			// Address.
			echo '<td style="' . esc_attr( $td_style ) . '">';
				echo wp_kses( $address, $allowed_html ) . '<br/>';

			if ( ! empty( $method ) ) {
				echo '<em>(' . esc_html( $method ) . ')</em>';
			}

			echo '</td>';

			/**
			 * Allow plugins to add table cell column.
			 *
			 * @param array    Cart package.
			 * @param int      Order ID.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_shop_table_row', $package, $order_id );

			// Notes.
			echo '<td style="' . esc_attr( $td_style ) . '">';
			if ( ! empty( $package['note'] ) ) {
				echo wp_kses( nl2br( $package['note'] ), $allowed_html );
			} else {
				echo '&ndash;';
			}

			if ( ! empty( $package['date'] ) ) {
				// translators: %s is replaced with the package date.
				echo '<p>' . sprintf( esc_html__( 'Delivery date: %s', 'woocommerce-shipping-multiple-addresses' ), esc_html( $package['date'] ) ) . '</p>';
			}
			echo '</td>';

			echo '</tr>';
		}
		echo '</table>';
	}

	/**
	 * Display shipping address in plain email.
	 *
	 * @param array    $packages Array of packages.
	 * @param WC_Order $order Current order object.
	 *
	 * @return void
	 */
	public function display_order_shipping_addresses_plain_email( $packages, $order ) {
		$methods           = $order->get_meta( '_shipping_methods' );
		$available_methods = $order->get_shipping_methods();

		echo esc_html__( 'This order ships to multiple addresses.', 'woocommerce-shipping-multiple-addresses' ) . "\n\n";

		foreach ( $packages as $x => $package ) {
			$products      = $package['contents'];
			$method        = $this->get_shipping_method_name( $x, $methods, $available_methods );
			$address       = str_replace( '<br/>', "\n", $this->get_package_shipping_address( $package ) );
			$product_infos = $this->get_package_products( $products, $order );

			// translators: %d is replaced with the address index.
			echo sprintf( esc_html__( 'Address %d', 'woocommerce-shipping-multiple-addresses' ), ( intval( $x ) + 1 ) ) . "\n\n";

			// Products.
			echo esc_html__( 'Products:', 'woocommerce-shipping-multiple-addresses' ) . "\n";

			foreach ( $product_infos as $i => $info ) {
				echo esc_html( ( $i + 1 ) . '. ' . $info['name'] . ' x ' . $info['qty'] );
				if ( ! empty( $info['meta'] ) ) {
					echo "\n\t" . esc_html( $info['meta'] );
				}
				echo "\n\n";
			}

			echo esc_html__( 'Address:', 'woocommerce-shipping-multiple-addresses' ) . "\n";

			// Address.
			echo esc_html( $address ) . ' (' . esc_html( $method ) . ')' . "\n\n";

			/**
			 * Allow plugins to add table row.
			 *
			 * @param array    Cart package.
			 * @param int      Order ID.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_shop_table_row', $package, $order->get_id() );

			// Notes.
			if ( ! empty( $package['note'] ) ) {
				echo esc_html( $package['note'] );
			} else {
				echo '&ndash;';
			}

			if ( ! empty( $package['date'] ) ) {
				// translators: %s is replaced with the package date.
				echo "\n" . sprintf( esc_html__( 'Delivery date: %s', 'woocommerce-shipping-multiple-addresses' ), esc_html( $package['date'] ) );
			}

			echo "\n\n\n";
		}
	}

	/**
	 * Show custom column for multiple address.
	 *
	 * @param array        $column List of column in the admin table.
	 * @param int|WC_Order $post_id_or_order Post ID or order object.
	 */
	public function show_multiple_addresses_line( $column, $post_id_or_order ) {
		$order = ( $post_id_or_order instanceof WC_Order ) ? $post_id_or_order : wc_get_order( $post_id_or_order );

		if ( ! ( $order instanceof WC_Order ) ) {
			return;
		}

		if ( 'shipping_address' === $column ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( ! $order->get_formatted_shipping_address() && is_array( $packages ) && count( $packages ) > 1 ) {
				esc_html_e( 'Ships to multiple addresses ', 'woocommerce-shipping-multiple-addresses' );
			}
		}
	}

	/**
	 * Add meta box in the order admin page.
	 *
	 * @param string           $post_type Type of the post.
	 * @param WP_Post|WC_Order $post_or_order_object Either WP_Post or WC_Order object.
	 */
	public function order_meta_box( $post_type, $post_or_order_object ) {
		if ( ! ( $post_or_order_object instanceof WP_Post ) && ! ( $post_or_order_object instanceof WC_Order ) ) {
			return;
		}

		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$methods   = $order->get_meta( '_shipping_methods' );
		$multiship = $order->get_meta( '_multiple_shipping' );

		if ( 'yes' === $multiship || ( is_array( $methods ) && count( $methods ) > 1 ) ) {
			add_meta_box(
				'wc_multiple_shipping',
				esc_html__( 'Order Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
				array( $this, 'packages_meta_box' ),
				WC_MS_Compatibility::get_meta_box_screen(),
				'normal',
				'core'
			);
		}
	}

	/**
	 * Enqueue admin css.
	 */
	public function admin_css() {
		$screen = get_current_screen();

		$order_screen_id = WC_MS_Compatibility::get_meta_box_screen();
		if ( in_array( $screen->id, array( $order_screen_id, 'woocommerce_page_wc-settings' ), true ) ) {
			wp_enqueue_style( 'wc-ms-admin-css', plugins_url( 'assets/css/admin.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );
		}
	}

	/**
	 * Display a shipping package inside order meta box.
	 *
	 * @param WC_Order|WP_Post $post_or_order_object Either post or order object.
	 */
	public function packages_meta_box( $post_or_order_object ) {
		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return;
		}

		$package_items          = $order->get_meta( '_packages_item_ids' );
		$methods                = $order->get_meta( '_shipping_methods' );
		$settings               = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$partial_orders         = isset( $settings['partial_orders'] ) && 'yes' === $settings['partial_orders'];
		$send_email             = isset( $settings['partial_orders_email'] ) && 'yes' === $settings['partial_orders_email'];
		$order_shipping_methods = $order->get_shipping_methods();

		// Get all the order items (and match the cart keys).
		$order_items    = $order->get_items();
		$cart_item_keys = array();
		foreach ( $order_items as $item_id => $item ) {
			if ( ! empty( $item['wcms_cart_key'] ) ) {
				$cart_item_keys[ $item['wcms_cart_key'] ] = $item_id;
			}
		}

		echo '<div class="item-addresses-holder">';

		foreach ( $packages as $x => $package ) {
			$products = $package['contents'];
			echo '<div class="item-address-box package-' . esc_attr( $x ) . '-box">';

			if ( $partial_orders && isset( $package['status'] ) && 'Completed' === $package['status'] ) {
				echo '<span class="complete">&nbsp;</span>';
			}

			foreach ( $products as $i => $product ) {

				// Get a matching order item.
				$item = false;
				if ( ! empty( $product['cart_key'] ) && ! empty( $cart_item_keys[ $product['cart_key'] ] ) && isset( $order_items[ $cart_item_keys[ $product['cart_key'] ] ] ) ) {
					$item = $order_items[ $cart_item_keys[ $product['cart_key'] ] ];
				} elseif ( ! empty( $package_items[ $i ] ) && isset( $order_items[ $package_items[ $i ] ] ) ) {
					// Fallback for items stored before WC 3.0.
					$item = $order_items[ $package_items[ $i ] ];
				}

				// Get item name and meta.
				if ( empty( $item ) ) {
					$id = empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'];
					/**
					 * Allow modifying product title.
					 *
					 * @var string $product_title Product title.
					 * @var WC_Product $product Product object.
					 * @return string
					 * @since 3.3.26
					 */
					$name = apply_filters( 'wcms_product_title', get_the_title( $id ), $product );

					/**
					 * Allow modifying item meta list.
					 *
					 * @var string $item_meta list of item meta (HTML).
					 * @var WC_Product $product Product object.
					 * @return string
					 * @since 3.3.26
					 */
					$meta = apply_filters( 'wcms_package_item_meta', self::get_item_meta( $product ), $product );
				} else {
					$name = is_callable( array( $item, 'get_name' ) ) ? $item->get_name() : get_the_title( empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'] );
					/**
					 * Allow modifying product title.
					 *
					 * @var string $product_title Product title.
					 * @var WC_Product $product Product object.
					 * @var WC_Order_Item $item Item object.
					 * @return string
					 * @since 3.3.26
					 */
					$name = apply_filters( 'wcms_product_title', $name, $product, $item );
					$meta = wc_display_item_meta( $item, array( 'echo' => false ) );
					/**
					 * Allow modifying item meta list.
					 *
					 * @var string $item_meta list of item meta (HTML).
					 * @var WC_Product $product Product object.
					 * @var WC_Order_Item $item Item object.
					 * @return string
					 * @since 3.3.26
					 */
					$meta = apply_filters( 'wcms_package_item_meta', $meta, $product, $item );
				}

				// Display product info.
				echo '<h4>' . esc_html( $name ) . ' &times; ' . esc_html( $product['quantity'] ) . '</h4>';
				if ( ! empty( $meta ) ) {
					echo wp_kses_post( $meta );
				}
			}

			self::display_shipping_package_address( $order, $package, $x, true );

			// Get Shipping method for this package.
			$method = isset( $methods[ $x ]['label'] ) ? $methods[ $x ]['label'] : '';

			if ( isset( $methods[ $x ]['id'] ) && empty( $method ) ) {
				foreach ( $order_shipping_methods as $ship_method ) {
					$method_info = explode( ':', $methods[ $x ]['id'] );
					$method_id   = $method_info[0];
					$method_inst = isset( $method_info[1] ) ? $method_info[1] : '';

					if ( $ship_method->get_method_id() . ':' . $ship_method->get_instance_id() === $methods[ $x ]['id']
						|| $ship_method->get_method_id() === $methods[ $x ]['id']
						|| ( $method_id === $ship_method->get_method_id() && $method_inst === $ship_method->get_instance_id() )
					) {
						$method = $ship_method->get_name();
						break;
					}
				}
			}
			if ( empty( $method ) ) {
				$order_method = current( $order_shipping_methods );
				$method       = $order_method['name'];
			}
			echo '<em>' . esc_html( $method ) . '</em>';

			// If partial orders are enabled then show package status.
			if ( $partial_orders ) {
				$current_status = isset( $package['status'] ) ? $package['status'] : 'Pending';

				if ( 'Completed' === $current_status ) {
					$select_css = 'display: none;';
					$status_css = '';
				} else {
					$select_css = '';
					$status_css = 'display: none;';
				}

				echo '<p id="package_' . esc_attr( $x ) . '_select_p" style="' . esc_attr( $select_css ) . '">
							<select id="package_' . esc_attr( $x ) . '_status">
								<option value="Pending" ' . selected( $current_status, 'Pending', false ) . '>' . esc_html__( 'Pending', 'woocommerce-shipping-multiple-addresses' ) . '</option>
								<option value="Completed" ' . selected( $current_status, 'Completed', false ) . '>' . esc_html__( 'Completed', 'woocommerce-shipping-multiple-addresses' ) . '</option>
							</select>
							<a class="button save-package-status" data-order="' . esc_attr( $order->get_id() ) . '" data-package="' . esc_attr( $x ) . '" href="#" title="Apply">' . esc_html__( 'GO', 'woocommerce-shipping-multiple-addresses' ) . '</a>
						</p>';

				echo '<p id="package_' . esc_attr( $x ) . '_status_p" style="' . esc_attr( $status_css ) . '"><strong>' . esc_html__( 'Completed', 'woocommerce-shipping-multiple-addresses' ) . '</strong> (<a href="#" class="edit_package" data-package="' . esc_attr( $x ) . '">' . esc_html__( 'Change', 'woocommerce-shipping-multiple-addresses' ) . '</a>)</p>';
			}

			/**
			 * Allow plugins to add element in package block.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block', $order, $package, $x );

			echo '</div>';
		}
		echo '</div>';
		echo '<div class="clear"></div>';

		$update_pkg_status_nonce = wp_create_nonce( 'wcms_update_package_status_save' );
		$email_enabled           = ( $send_email ) ? 'true' : 'false';
		$inline_js               = '
			var email_enabled = ' . $email_enabled . ';
			jQuery(".shipping_data a.edit_shipping_address").click(function(e) {
				e.preventDefault();
				jQuery(this).closest(".shipping_data").find("div.edit_shipping_address").show();
			});

			jQuery(".save-package-status").click(function(e) {
				e.preventDefault();
				var pkg_id      = jQuery(this).data("package");
				var order_id    = jQuery(this).data("order");
				var status      = jQuery("#package_"+ pkg_id +"_status").val();
				var email       = false;

				if ( status == "Completed" && email_enabled ) {
					if ( confirm("' . __( 'Do you want to send an email to the customer?', 'woocommerce-shipping-multiple-addresses' ) . '") ) {
						email = true;
					}
				}

				jQuery(".package-"+ pkg_id +"-box").block({ message: null, overlayCSS: { background: "#fff url(' . WC()->plugin_url() . '/assets/images/ajax-loader.gif) no-repeat center", opacity: 0.6 } });

				jQuery.post(ajaxurl, {action: "wcms_update_package_status", security: "' . esc_js( $update_pkg_status_nonce ) . '", "status": status, package: pkg_id, order: order_id, email: email}, function(resp) {
					if ( resp == "Completed" ) {
						jQuery(".package-"+ pkg_id +"-box").prepend("<span class=\'complete\'>&nbsp;</span>");
						jQuery("#package_"+ pkg_id +"_status_p").show();
						jQuery("#package_"+ pkg_id +"_select_p").hide();
					} else {
						jQuery(".package-"+ pkg_id +"-box").find("span.complete").remove();
					}

					jQuery(".package-"+ pkg_id +"-box").unblock();
				});

			});

			jQuery(".edit_package").click(function(e) {
				e.preventDefault();

				var pkg_id = jQuery(this).data("package");

				jQuery("#package_"+ pkg_id +"_status_p").hide();
				jQuery("#package_"+ pkg_id +"_select_p").show();
			});
		';

		wc_enqueue_js( $inline_js );
	}

	/**
	 * Display shipping package address.
	 *
	 * @param WC_Order $order Order object.
	 * @param array    $package Cart package.
	 * @param int      $index Package index.
	 * @param boolean  $edit Check if the address is editable.
	 */
	public static function display_shipping_package_address( $order, $package, $index, $edit = false ) {
		if ( empty( $package['destination'] ) ) {
			return;
		}

		$address_map_url = self::generate_address_map_url( $package, $order );
		?>
		<div class="shipping_data">

			<?php
			/**
			 * Allow plugins to add element before the address.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block_before_address', $order, $package, $index );
			?>

			<div class="address">
				<p>
					<a href="<?php echo esc_url( $address_map_url ); ?>" target="_blank">
						<?php // No need to escape. It's already escaped on `WC_Countries::get_formatted_address()`. ?>
						<?php echo WC()->countries->get_formatted_address( $package['destination'] ); // phpcs:ignore ?>
					</a>
				</p>
			</div>

			<?php
			/**
			 * Allow plugins to add element after the address.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block_after_address', $order, $package, $index );
			?>

			<?php
			if ( $edit ) {

				// Get local shipping fields.
				$shipping_fields = WC()->countries->get_address_fields( $package['destination']['country'], 'shipping_' );

				if ( ! empty( $shipping_fields ) ) {
					echo '<a class="edit_shipping_address" href="#">( ' . esc_html__( 'Edit', 'woocommerce-shipping-multiple-addresses' ) . ' )</a><br />';

					// Display form.
					echo '<div class="edit_shipping_address" style="display:none;">';

					foreach ( $shipping_fields as $key => $field ) {
						$key      = str_replace( 'shipping_', '', $key );
						$addr_key = $key;
						$key      = 'pkg_' . $key . '_' . $index;

						if ( ! isset( $field['type'] ) ) {
							$field['type'] = 'text';
						}
						if ( ! isset( $field['label'] ) ) {
							$field['label'] = '';
						}
						switch ( $field['type'] ) {
							case 'select':
								woocommerce_wp_select(
									array(
										'id'      => $key,
										'label'   => $field['label'],
										'options' => $field['options'],
										'value'   => $package['destination'][ $addr_key ],
									)
								);
								break;
							default:
								woocommerce_wp_text_input(
									array(
										'id'    => $key,
										'label' => $field['label'],
										'value' => $package['destination'][ $addr_key ],
									)
								);
								break;
						}
					}

					echo '<input type="hidden" name="edit_address[]" value="' . esc_attr( $index ) . '" />';
					echo '</div>';

				}
			}
			?>

		</div>
		<?php
	}



	/**
	 * Update order addresses.
	 *
	 * @param int $post_id Order ID.
	 * @return void
	 */
	public function update_order_addresses( $post_id ) {
		$order = wc_get_order( $post_id );

		if ( ! $order ) {
			return;
		}

		// No need to verify nonce. it's already verified on `WC_Admin_Meta_Boxes::save_meta_boxes()`.
		$packages       = $order->get_meta( '_wcms_packages' );
		$edit_addresses = ( isset( $_POST['edit_address'] ) ) ? wc_clean( $_POST['edit_address'] ) : array(); //phpcs:ignore

		if ( $packages && is_array( $edit_addresses ) && 0 < count( $edit_addresses ) ) {
			foreach ( $edit_addresses as $idx ) {
				if ( ! isset( $packages[ $idx ] ) ) {
					continue;
				}

				// Get the shipping fields.
				$shipping_fields = WC()->countries->get_address_fields( $packages[ $idx ]['destination']['country'], 'shipping_' );
				$address         = array();

				// Assign value to respective address key.
				foreach ( $shipping_fields as $key => $field ) {
					$addr_key             = str_replace( 'shipping_', '', $key );
					$post_key             = 'pkg_' . $addr_key . '_' . $idx;
					$address[ $addr_key ] = isset( $_POST[ $post_key ] ) ? sanitize_text_field( wp_unslash( $_POST[ $post_key ] ) ) : ''; // phpcs:ignore
				}

				$packages[ $idx ]['destination'] = $address;
			}

			$order->update_meta_data( '_wcms_packages', $packages );
			$order->save();
		}
	}

	/**
	 * Update order taxes.
	 *
	 * @param int   $order_id Order ID.
	 * @param array $items Order items.
	 * @return void
	 */
	public function update_order_taxes( $order_id, $items ) {
		$order_taxes = isset( $items['order_taxes'] ) ? $items['order_taxes'] : array();
		$tax_total   = array();
		$order       = wc_get_order( $order_id );

		if ( ! $order ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! is_array( $packages ) ) {
			return;
		}

		foreach ( $order_taxes as $item_id => $rate_id ) {
			foreach ( $packages as $package ) {
				foreach ( $package['contents'] as $item ) {
					if ( isset( $item['line_tax_data']['total'][ $rate_id ] ) ) {
						if ( ! isset( $tax_total[ $item_id ] ) ) {
							$tax_total[ $item_id ] = 0;
						}
						$tax_total[ $item_id ] += $item['line_tax_data']['total'][ $rate_id ];
					}
				}
			}
		}

		$total_tax = 0;
		foreach ( $tax_total as $item_id => $total ) {
			$total_tax += $total;
			wc_update_order_item_meta( $item_id, 'tax_amount', $tax_total[ $item_id ] );
		}

		$old_total_tax = floatval( $order->get_meta( '_order_tax' ) );

		if ( $total_tax > $old_total_tax ) {
			$order_total  = floatval( $order->get_meta( '_order_total' ) );
			$order_total -= $old_total_tax;
			$order_total += $total_tax;

			$order->update_meta_data( '_order_total', $order_total );
		}

		$order->update_meta_data( '_order_tax', $total_tax );
		$order->save();
	}

	/**
	 * Set order items taxes.
	 *
	 * @param array    $items Order items.
	 * @param WC_Order $order Order object.
	 * @return array
	 */
	public function order_item_taxes( $items, $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return $items;
		}

		if ( 'yes' !== $order->get_meta( '_multiple_shipping' ) ) {
			return $items;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return $items;
		}

		foreach ( $items as $item_id => $item ) {
			if ( 'line_item' !== $item['type'] ) {
				continue;
			}

			$item_tax_subtotal = 0;
			$item_tax_total    = 0;
			$item_tax_data     = array();
			$modified          = false;

			$item_line_tax_data = ! is_array( $item['line_tax_data'] ) ? unserialize( $item['line_tax_data'] ) : $item['line_tax_data'];// phpcs:ignore --- need to unserialize.
			$tax_rate_ids       = array_keys( $item_line_tax_data['total'] );

			foreach ( $packages as $package ) {
				foreach ( $package['contents'] as $package_item ) {

					if ( (int) $item['product_id'] === (int) $package_item['product_id'] && (int) $item['variation_id'] === (int) $package_item['variation_id'] ) {
						$modified = true;

						$item_tax_subtotal += $package_item['line_subtotal_tax'];
						$item_tax_total    += $package_item['line_tax'];

						if ( $item instanceof WC_Order_Item_Product ) {
							$item_rate_ids = array_keys( $package_item['line_tax_data']['total'] );
							$tax_rate_ids  = array_unique( array_merge( $tax_rate_ids, $item_rate_ids ) );
						}

						foreach ( $tax_rate_ids as $rate_id ) {
							if ( isset( $package_item['line_tax_data']['total'][ $rate_id ] ) ) {
								$item_tax_data['total'][ $rate_id ] = $package_item['line_tax_data']['total'][ $rate_id ];
							}

							if ( isset( $package_item['line_tax_data']['subtotal'][ $rate_id ] ) ) {
								$item_tax_data['subtotal'][ $rate_id ] = $package_item['line_tax_data']['subtotal'][ $rate_id ];
							}
						}
					}
				}
			}

			if ( $modified && is_array( $tax_rate_ids ) ) {
				if ( $item instanceof WC_Order_Item_Product ) {
					$items[ $item_id ]->set_taxes( $item_tax_data );
				} else {
					$items[ $item_id ]['line_tax']          = $item_tax_total;
					$items[ $item_id ]['line_subtotal_tax'] = $item_tax_subtotal;
					$items[ $item_id ]['line_tax_data']     = serialize( $item_tax_data );// phpcs:ignore --- Need to be serialized.
				}
			}
		}

		return $items;
	}

	/**
	 * Load a custom template body for orders with multishipping
	 *
	 * @param string   $template PIP Template path.
	 * @param WC_Order $order Order object.
	 *
	 * @return string $template
	 */
	public function pip_template_body( $template, $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return $template;
		}

		$packages = $order->get_meta( '_shipping_packages' );

		if ( is_array( $packages ) && 1 < count( $packages ) ) {
			$template = dirname( WC_Ship_Multiple::FILE ) . '/templates/pip-template-body.php';
		}

		return $template;
	}

	/**
	 * Send package email.
	 *
	 * @param int $order_id Order ID.
	 * @param int $package_index Loop index.
	 * @return void
	 */
	public static function send_package_email( $order_id, $package_index ) {
		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$order    = wc_get_order( $order_id );

		$subject = empty( $settings['email_subject'] ) ? esc_html__( 'Part of your order has been shipped', 'woocommerce-shipping-multiple-addresses' ) : $settings['email_subject'];
		$message = empty( $settings['email_message'] ) ? self::get_default_email_body() : $settings['email_message'];

		$mailer  = WC()->mailer();
		$message = $mailer->wrap_message( $subject, $message );

		$ts         = strtotime( WC_MS_Compatibility::get_order_prop( $order, 'order_date' ) );
		$order_date = gmdate( get_option( 'date_format' ), $ts );
		$order_time = gmdate( get_option( 'time_format' ), $ts );

		$search       = array( '{order_id}', '{order_date}', '{order_time}', '{customer_first_name}', '{customer_last_name}', '{products_table}', '{addresses_table}' );
		$replacements = array(
			$order->get_order_number(),
			$order_date,
			$order_time,
			$order->get_billing_first_name(),
			$order->get_billing_last_name(),
			self::render_products_table( $order, $package_index ),
			self::render_addresses_table( $order, $package_index ),
		);
		$message      = str_replace( $search, $replacements, $message );

		$mailer->send( $order->get_billing_email(), $subject, $message );
	}

	/**
	 * Get default email body.
	 *
	 * @return string
	 */
	public static function get_default_email_body() {
		ob_start();
		?>
		<?php // translators: %s is a blog or site name. ?>
		<p><?php printf( esc_html__( 'Hi there. Part of your recent order on %s has been completed. Your order details are shown below for your reference:', 'woocommerce-shipping-multiple-addresses' ), esc_html( get_option( 'blogname' ) ) ); ?></p>

		<h2><?php echo esc_html__( 'Order:', 'woocommerce-shipping-multiple-addresses' ) . ' {order_id}'; ?></h2>

		{products_table}

		{addresses_table}

		<?php
		return ob_get_clean();
	}

	/**
	 * Render order products table.
	 *
	 * @param WC_Order $order Order object.
	 * @param int      $idx Package index.
	 * @return string
	 */
	public static function render_products_table( $order, $idx ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return '';
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $packages[ $idx ];
		$products = $package['contents'];

		ob_start();
		?>
		<table cellspacing="0" cellpadding="6" style="width: 100%; border: 1px solid #eee;" border="1" bordercolor="#eee">
			<thead>
			<tr>
				<th scope="col" style="text-align:left; border: 1px solid #eee;"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th scope="col" style="text-align:left; border: 1px solid #eee;"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
			</tr>
			</thead>
			<tbody>
			<?php
			foreach ( $products as $item ) :
				$_product             = wc_get_product( $item['product_id'] );
				$attachment_image_src = wp_get_attachment_image_src( get_post_thumbnail_id( $_product->get_id() ), 'thumbnail' );
				$image                = $attachment_image_src ? '<img src="' . esc_url( current( $attachment_image_src ) ) . '" alt="Product Image" height="32" width="32" style="vertical-align:middle; margin-right: 10px;" />' : '';
				?>
				<tr>
					<td style="text-align:left; vertical-align:middle; border: 1px solid #eee; word-wrap:break-word;">
					<?php
					// Show title/image etc.
					/**
					 * Allow plugins to modify product image output.
					 *
					 * @param string Product image HTML output.
					 * @param WC_Product Product object.
					 * @param boolean As variable.
					 *
					 * @since 3.3
					 */
					$output_image      = apply_filters( 'woocommerce_order_product_image', $image, $_product, true );
					$allowed_image_tag = array(
						'img' => array(
							'src'    => array(),
							'alt'    => array(),
							'height' => array(),
							'width'  => array(),
							'style'  => array(),
						),
					);
					echo wp_kses( $output_image, $allowed_image_tag ); //phpcs:ignore

					// Product name.
					/**
					 * Allow plugins to modify product title.
					 *
					 * @param string     Product title,
					 * @param WC_Product Product object.
					 *
					 * @since 3.3
					 */
					$output_product_name = apply_filters( 'woocommerce_order_product_title', $_product->get_title(), $_product );
					echo wp_kses_post( $output_product_name );

					// SKU.
					echo ( $_product->get_sku() ? esc_html( ' (#' . $_product->get_sku() . ')' ) : '' );

					// File URLs.
					if ( $_product->exists() && $_product->is_downloadable() ) {

						$download_file_urls = $order->get_downloadable_file_urls( $item['product_id'], $item['variation_id'], $item );

						$i = 0;

						foreach ( $download_file_urls as $file_url => $download_file_url ) {
							echo '<br/><small>';

							$filename = wc_get_filename_from_url( $file_url );

							if ( count( $download_file_urls ) > 1 ) {
								// translators: %d is an index of the URLS array.
								printf( esc_html__( 'Download %d:', 'woocommerce-shipping-multiple-addresses' ), intval( $i ) + 1 );
							} elseif ( 0 === $i ) {
								echo esc_html__( 'Download:', 'woocommerce-shipping-multiple-addresses' );
							}

								echo ' <a href="' . esc_url( $download_file_url ) . '" target="_blank">' . esc_html( $filename ) . '</a></small>';

								++$i;
						}
					}
					?>
					</td>
					<td style="text-align:left; vertical-align:middle; border: 1px solid #eee;"><?php echo esc_html( $item['quantity'] ); ?></td>
				</tr>
			<?php endforeach; ?>
			</tbody>
		</table>
		<?php

		return ob_get_clean();
	}

	/**
	 * Render order addresses table.
	 *
	 * @param WC_Order $order Order object.
	 * @param int      $index Package index.
	 * @return string
	 */
	public static function render_addresses_table( $order, $index ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return '';
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $packages[ $index ];

		ob_start();
		?>
		<table cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top;" border="0">
			<tr>
				<td valign="top" width="50%">
					<h3><?php esc_html_e( 'Billing address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php // no need to escape. It's already escape from `WC_Order::get_formatted_billing_address()`. ?>
					<p><?php echo $order->get_formatted_billing_address(); //phpcs:ignore ?></p>
				</td>
				<td valign="top" width="50%">
					<h3><?php esc_html_e( 'Shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php self::display_shipping_package_address( $order, $package, $index ); ?>
				</td>
			</tr>
		</table>
		<?php

		return ob_get_clean();
	}

	/**
	 * Gets and formats a list of item meta for display (fallback function for when we can't find order item)
	 *
	 * @param array $item Cart item.
	 *
	 * @return string
	 */
	public static function get_item_meta( $item ) {

		$item_data = array();

		// Variation data.
		if ( ! empty( $item['data']->variation_id ) && is_array( $item['variation'] ) ) {

			foreach ( $item['variation'] as $name => $value ) {

				if ( empty( $value ) ) {
					continue;
				}

				$taxonomy = wc_attribute_taxonomy_name( str_replace( 'attribute_pa_', '', urldecode( $name ) ) );

				// If this is a term slug, get the term's nice name.
				if ( taxonomy_exists( $taxonomy ) ) {
					$term = get_term_by( 'slug', $value, $taxonomy );
					if ( ! is_wp_error( $term ) && $term && $term->name ) {
						$value = $term->name;
					}
					$label = wc_attribute_label( $taxonomy );

					// If this is a custom option slug, get the options name.
				} else {
					/**
					 * Allow plugins to modify variation option name.
					 *
					 * @param mixed variation option value.
					 *
					 * @since 3.3
					 */
					$value = apply_filters( 'woocommerce_variation_option_name', $value );
					$label = wc_attribute_label( str_replace( 'attribute_', '', $name ), $item['data'] );
				}

				$item_data[] = array(
					'key'   => $label,
					'value' => $value,
				);
			}
		}

		$output = '';
		if ( ! empty( $item_data ) ) {
			$output .= '<ul>';
			foreach ( $item_data as $data ) {
				$output .= '<li>' . esc_html( $data['key'] ) . ': ' . wp_kses_post( $data['value'] ) . '</li>';
			}
			$output .= '</ul>';
		}

		return $output;
	}

	/**
	 * Hides metadata.
	 *
	 * @param  array $hidden Hidden meta strings.
	 * @return array Modified hidden meta strings
	 */
	public function hidden_order_item_meta( $hidden ) {
		return array_merge( $hidden, array( '_wcms_cart_key' ) );
	}

	/**
	 * Hide shipping address if order has multiple address.
	 *
	 * @param bool     $needs_shipping Whether the order need shipping or not.
	 * @param array    $hide List of shipping method that will hide the shipping address.
	 * @param WC_Order $order Order object.
	 *
	 * @return bool
	 */
	public function manipulate_needs_shipping( $needs_shipping, $hide, $order ) {
		if ( ! $order || ! is_view_order_page() ) {
			return $needs_shipping;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! empty( $packages ) && 1 < count( $packages ) ) {
			return false;
		}

		return $needs_shipping;
	}
}
