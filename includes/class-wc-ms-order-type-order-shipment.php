<?php
/**
 * Class WC_MS_Order_Type_Order_Shipment file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Order_Type_Order_Shipment
 */
class WC_MS_Order_Type_Order_Shipment extends WC_Order {
	/**
	 * Get internal type (post type.)
	 *
	 * @return string
	 */
	public function get_type() {
		return 'order_shipment';
	}

	/**
	 * Set order key.
	 *
	 * @param string $value Max length 37 chars.
	 * @throws WC_Data_Exception Throws exception when invalid data is found.
	 */
	public function set_order_key( $value ) {
		$this->set_prop( 'order_key', substr( $value, 0, 37 ) );
	}
}
