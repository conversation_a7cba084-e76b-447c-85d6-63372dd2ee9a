<?php
/**
 * Class WC_MS_Address_Book file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class for managing address book.
 */
class WC_MS_Address_Book {
	/**
	 * Multiple address class.
	 *
	 * @var WC_Ship_Multiple.
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Multiple address object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_action( 'template_redirect', array( $this, 'save_shipping_addresses' ) );
		add_action( 'template_redirect', array( $this, 'save_account_shipping_addresses' ) );
		add_action( 'template_redirect', array( $this, 'save_addresses_book_from_post' ) );

		add_action( 'wp_ajax_wc_ms_delete_address', array( $this, 'ajax_delete_address' ) );
		add_action( 'wp_ajax_nopriv_wc_ms_delete_address', array( $this, 'ajax_delete_address' ) );
	}

	/**
	 * Get the user's default address
	 *
	 * @param int $user_id User ID.
	 *
	 * @return array
	 */
	public function get_user_default_address( $user_id ) {
		$shipping_address_fields = array(
			// current field => backwards compatibility field.
			'shipping_first_name' => 'first_name',
			'shipping_last_name'  => 'last_name',
			'shipping_company'    => 'company',
			'shipping_address_1'  => 'address_1',
			'shipping_address_2'  => 'address_2',
			'shipping_city'       => 'city',
			'shipping_state'      => 'state',
			'shipping_postcode'   => 'postcode',
			'shipping_country'    => 'country',
		);

		$customer        = new WC_Customer( $user_id );
		$default_address = array( 'default_address' => true );

		foreach ( $shipping_address_fields as $key => $backwards_key ) {
			$default_address[ $key ] = $customer->{ 'get_' . $key }();
			
			// Backwards compatibility.
			$default_address[ $backwards_key ] = $default_address[ $key ];
		}

		/**
		 * Filter to manipulate the user default address.
		 *
		 * @param array $default_address User's default address.
		 *
		 * @since 3.3.16
		 */
		return apply_filters( 'wc_ms_default_user_address', $default_address );
	}

	/**
	 * Validate multi shipping items to make sure it has the same quantity as the real cart items.
	 *
	 * @param array $items Multi shipping items.
	 *
	 * @return boolean
	 */
	public function validate_items_quantity( array $items ) {
		$cart_items = wcms_get_real_cart_items();
		foreach ( $items as $cart_key => $item ) {
			$real_qty = isset( $cart_items[ $cart_key ]['quantity'] ) ? intval( $cart_items[ $cart_key ]['quantity'] ) : 0;

			if ( ! isset( $item['qty'] ) ) {
				return false;
			}

			$ms_qty = 0;

			foreach ( $item['qty'] as $qty ) {
				$ms_qty += intval( $qty );
			}

			if ( $ms_qty !== $real_qty ) {
				return false;
			}
		}
		return true;
	}

	/**
	 * Save shipping addresses to the current cart session.
	 */
	public function save_shipping_addresses() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'shipping_address_action' ) ) {
			return;
		}

		if ( ! isset( $_POST['shipping_address_action'] ) || 'save' !== $_POST['shipping_address_action'] ) {
			return;
		}

		$cart           = WC()->cart;
		$user_addresses = $this->get_user_addresses( get_current_user_id() );
		$customer       = WC()->customer;

		// Save the guest's shipping address temporarily.
		if ( $customer instanceof WC_Customer && 0 === $customer->get_id() ) {
			$default_address = array();

			foreach ( WC()->customer->get_shipping() as $key => $value ) {
				$default_address[ 'shipping_' . $key ] = $value;
			}

			foreach ( WC()->customer->get_billing() as $key => $value ) {
				$default_address[ 'billing_' . $key ] = $value;
			}

			wcms_session_set( 'user_default_address', $default_address );
		}

		$fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

		$cart->get_cart_from_session();
		$cart_items = wcms_get_real_cart_items();

		$data = array();
		$rel  = array();

		if ( isset( $_POST['items'] ) ) {

			$items = wc_clean( wp_unslash( $_POST['items'] ) );

			if ( ! is_array( $items ) || ! $this->validate_items_quantity( $items ) ) {
				wc_add_notice( __( 'The quantity on shipping addresses does not match with real cart quantity. Please make sure all item quantity are set.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
				wp_safe_redirect( get_permalink( wc_get_page_id( 'multiple_addresses' ) ) );
				exit;
			}

			// handler for quantities update.
			foreach ( $items as $cart_key => $item ) {
				$qtys           = $item['qty'];
				$item_addresses = $item['address'];
				$cart_items     = wcms_get_real_cart_items();
				$new_qty        = 0;

				if ( empty( $cart_items[ $cart_key ] ) ) {
					continue;
				}

				foreach ( $item_addresses as $idx => $item_address ) {
					if ( intval( $qtys[ $idx ] ) > 0 ) {
						// decrement the cart item quantity by one.
						$new_qty += intval( $qtys[ $idx ] );
					}
				}

				$cart->set_quantity( $cart_key, $new_qty );
			}

			$cart_items = wcms_get_real_cart_items();
			foreach ( $items as $cart_key => $item ) {
				$qtys           = $item['qty'];
				$item_addresses = $item['address'];

				if ( empty( $cart_items[ $cart_key ] ) ) {
					continue;
				}

				$product_id = $cart_items[ $cart_key ]['product_id'];
				$sig        = $cart_key . '_' . $product_id . '_';
				$_sig       = '';

				foreach ( $item_addresses as $idx => $item_address ) {
					$address_id   = $item_address;
					$user_address = $user_addresses[ $address_id ];

					$i = 1;
					for ( $x = 0; $x < $qtys[ $idx ]; $x++ ) {

						$rel[ $address_id ][] = $cart_key;

						while ( isset( $data[ 'shipping_first_name_' . $sig . $i ] ) ) {
							++$i;
						}
						$_sig = $sig . $i;

						if ( $fields ) {
							foreach ( $fields as $key => $field ) :
								$data[ $key . '_' . $_sig ] = $user_address[ $key ];
						endforeach;
						}
					}
				}

				$cart_address_ids_session = (array) wcms_session_get( 'cart_address_ids' );

				if ( ! empty( $_sig ) && ! wcms_session_isset( 'cart_address_ids' ) || ! in_array( $_sig, $cart_address_ids_session, true ) ) {
					$cart_address_sigs_session          = wcms_session_get( 'cart_address_sigs' );
					$cart_address_sigs_session[ $_sig ] = $address_id;
					wcms_session_set( 'cart_address_sigs', $cart_address_sigs_session );
				}
			}
		}

		wcms_session_set( 'cart_item_addresses', $data );
		wcms_session_set( 'address_relationships', $rel );
		wcms_session_set( 'wcms_item_addresses', $rel );

		if ( isset( $_POST['update_quantities'] ) || isset( $_POST['delete_line'] ) ) {
			$next_url = get_permalink( wc_get_page_id( 'multiple_addresses' ) );
		} else {
			// redirect to the checkout page.
			$next_url = wc_get_checkout_url();
		}

		$this->wcms->clear_packages_cache();
		wp_safe_redirect( $next_url );
		exit;
	}

	/**
	 * Saving the shipping addresses to user meta.
	 */
	public function save_account_shipping_addresses() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'shipping_account_address_action' ) ) {
			return;
		}

		if ( ! isset( $_POST['shipping_account_address_action'] ) || 'save' !== $_POST['shipping_account_address_action'] ) {
			return;
		}

		$user = wp_get_current_user();
		$idx  = isset( $_POST['idx'] ) ? intval( $_POST['idx'] ) : 0;

		$addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

		if ( ! is_array( $addresses ) ) {
			$addresses = array();
		}

		if ( -1 === $idx ) {
			$idx = count( $addresses );

			while ( array_key_exists( $idx, $addresses ) ) {
				++$idx;
			}
		}

		unset( $_POST['shipping_account_address_action'], $_POST['set_addresses'], $_POST['idx'] );

		foreach ( $_POST as $key => $value ) {
			$addresses[ $idx ][ $key ] = $value;
		}

		update_user_meta( $user->ID, 'wc_other_addresses', $addresses );

		wc_add_notice( __( 'Address saved', 'woocommerce-shipping-multiple-addresses' ), 'success' );

		$page_id = wc_get_page_id( 'myaccount' );
		wp_safe_redirect( get_permalink( $page_id ) );
		exit;
	}

	/**
	 * Validating the address fields.
	 *
	 * @param array $ship_fields Ship fields.
	 *
	 * @return array.
	 */
	public function validate_addresses_book( $ship_fields ) {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'save_to_address_book' ) ) {
			return;
		}

		if ( ! isset( $_POST['address'] ) ) {
			return;
		}
		// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		$address = wc_clean( wp_unslash( $_POST['address'] ) );
		$errors  = array();

		foreach ( $ship_fields as $key => $field ) {

			if ( isset( $field['required'] ) && $field['required'] && empty( $address[ $key ] ) ) {
				if ( 'shipping_state' === $key && empty( WC()->countries->get_states( $address['shipping_country'] ) ) ) {
					continue;
				}

				$errors[] = $key;
			}

			if ( ! empty( $address[ $key ] ) ) {

				// Validation rules.
				if ( ! empty( $field['validate'] ) && is_array( $field['validate'] ) ) {
					foreach ( $field['validate'] as $rule ) {
						switch ( $rule ) {
							case 'postcode':
								$address[ $key ] = trim( $address[ $key ] );

								if ( ! WC_Validation::is_postcode( $address[ $key ], $address['shipping_country'] ) ) :
									$errors[] = $key;
									wc_add_notice( __( 'Please enter a valid postcode/ZIP.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								else :
									$address[ $key ] = wc_format_postcode( $address[ $key ], $address['shipping_country'] );
								endif;
								break;
							case 'phone':
								$address[ $key ] = wc_format_phone_number( $address[ $key ] );

								if ( ! WC_Validation::is_phone( $address[ $key ] ) ) {
									$errors[] = $key;

									wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not a valid phone number.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								}

								break;
							case 'email':
								$address[ $key ] = strtolower( $address[ $key ] );

								if ( ! is_email( $address[ $key ] ) ) {
									$errors[] = $key;

									wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not a valid email address.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								}

								break;
							case 'state':
								// Get valid states.
								$valid_states = WC()->countries->get_states( $address['shipping_country'] );
								if ( $valid_states ) {
									$valid_state_values = array_flip( array_map( 'strtolower', $valid_states ) );
								}

								// Convert value to key if set.
								if ( isset( $valid_state_values[ strtolower( $address[ $key ] ) ] ) ) {
									$address[ $key ] = $valid_state_values[ strtolower( $address[ $key ] ) ];
								}

								// Only validate if the country has specific state options.
								if ( is_array( $valid_states ) && count( $valid_states ) > 0 ) {
									if ( ! in_array( $address[ $key ], array_keys( $valid_states ), true ) ) {
										$errors[] = $key;

										wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not valid. Please enter one of the following:', 'woocommerce-shipping-multiple-addresses' ) . ' ' . implode( ', ', $valid_states ), 'error' );
									}
								}
								break;
						}
					}
				}
			}
		}

		return array(
			'errors'  => $errors,
			'address' => $address,
		);
	}

	/**
	 * Save addresses book from POST.
	 */
	public function save_addresses_book_from_post() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'save_to_address_book' ) ) {
			return;
		}

		if ( ! isset( $_POST['id'] ) || ! isset( $_POST['address'] ) ) {
			return;
		}

		$user         = wp_get_current_user();
		$id           = intval( $_POST['id'] );
		$address      = wc_clean( wp_unslash( $_POST['address'] ) );
		$addresses    = $this->get_user_addresses( $user );
		$ship_fields  = WC()->countries->get_address_fields( $address['shipping_country'], 'shipping_' );
		$redirect_url = ( isset( $_POST['next'] ) ) ? esc_url_raw( wp_unslash( $_POST['next'] ) ) : get_permalink( wc_get_page_id( 'multiple_addresses' ) );

		$validation = $this->validate_addresses_book( $ship_fields );

		if ( count( $validation['errors'] ) > 0 ) {
			wc_add_notice( __( 'Please enter the complete address', 'woocommerce-shipping-multiple-addresses' ), 'error' );
			$next = add_query_arg( $address, $redirect_url );
			$next = add_query_arg( 'address-form', 1, $next );
			wp_safe_redirect( esc_url( $next ) );
			exit;
		}

		$address = $validation['address'];

		// address is unique, save!
		if ( -1 === $id ) {
			$vals = '';
			foreach ( $address as $key => $value ) {
				$vals .= $value;
			}
			$md5 = md5( $vals );

			foreach ( $addresses as $addr ) {
				$vals = '';
				if ( ! is_array( $addr ) ) {
					continue; }
				foreach ( $addr as $key => $value ) {
					$vals .= $value;
				}
				$addr_md5 = md5( $vals );

				if ( $md5 === $addr_md5 ) {
					// duplicate address!
					wc_add_notice( __( 'Address is already in your address book', 'woocommerce-shipping-multiple-addresses' ), 'error' );
					$next = add_query_arg( $address, $redirect_url );
					$next = add_query_arg( 'address-form', 1, $next );
					wp_safe_redirect( esc_url( $next ) );
					exit;
				}
			}

			$addresses[] = $address;
		} else {
			$addresses[ $id ] = $address;
		}

		// Update the default address and remove it from the $addresses array.
		if ( $user->ID > 0 ) {
			if ( 0 === $id ) {
				$default_address = $addresses[0];
				unset( $addresses[0] );

				if ( $default_address['shipping_address_1'] && $default_address['shipping_postcode'] ) {
					update_user_meta( $user->ID, 'shipping_first_name', $default_address['shipping_first_name'] );
					update_user_meta( $user->ID, 'shipping_last_name', $default_address['shipping_last_name'] );
					update_user_meta( $user->ID, 'shipping_company', $default_address['shipping_company'] );
					update_user_meta( $user->ID, 'shipping_address_1', $default_address['shipping_address_1'] );
					update_user_meta( $user->ID, 'shipping_address_2', $default_address['shipping_address_2'] );
					update_user_meta( $user->ID, 'shipping_city', $default_address['shipping_city'] );
					update_user_meta( $user->ID, 'shipping_state', $default_address['shipping_state'] );
					update_user_meta( $user->ID, 'shipping_postcode', $default_address['shipping_postcode'] );
					update_user_meta( $user->ID, 'shipping_country', $default_address['shipping_country'] );
				}
				unset( $addresses[0] );
			}
		}

		$this->save_user_addresses( $user->ID, $addresses );

		if ( $id >= 0 ) {
			$next = add_query_arg( 'updated', '1', $redirect_url );
		} else {
			$next = add_query_arg( 'new', '1', $redirect_url );
		}

		wp_safe_redirect( esc_url( $next ) );
		exit;
	}

	/**
	 * Delete address using ajax.
	 */
	public function ajax_delete_address() {
		if ( ! isset( $_POST['_wcmsnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wcmsnonce'] ), 'wcms-action_' . WC()->session->get_customer_unique_id() ) ) {
			exit;
		}

		if ( ! isset( $_POST['idx'] ) ) {
			exit;
		}

		$user      = wp_get_current_user();
		$idx       = absint( sanitize_text_field( wp_unslash( $_POST['idx'] ) ) );
		$addresses = $this->get_user_addresses( $user );

		unset( $addresses[ $idx ] );

		$this->save_user_addresses( $user->ID, $addresses );

		wp_send_json( array( 'ack' => 'OK' ) );
		exit;
	}

	/**
	 * Get user addresses from user meta.
	 *
	 * @param WP_User $user User object.
	 * @param boolean $include_default Include default address from WooCommerce.
	 *
	 * @return array.
	 */
	public function get_user_addresses( $user, $include_default = true ) {
		if ( ! $user instanceof WP_User ) {
			$user = new WP_User( $user );
		}

		if ( 0 !== $user->ID ) {
			$addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

			if ( ! $addresses ) {
				$addresses = array();
			}

			if ( $include_default ) {
				$default_address = $this->get_user_default_address( $user->ID );

				if ( $default_address['address_1'] && $default_address['postcode'] ) {
					array_unshift( $addresses, $default_address );
				}
			}
		} else {
			// Guest address - using sessions to store the address.
			$addresses = ( wcms_session_isset( 'user_addresses' ) ) ? wcms_session_get( 'user_addresses' ) : array();
		}

		return $this->array_sort( $addresses, 'shipping_first_name' );
	}

	/**
	 * Get available user addresses by filtering the user addresses using avaiable shipping countries.
	 *
	 * @param WP_User $user            User object.
	 * @param boolean $include_default Include default address from WooCommerce.
	 *
	 * @return array.
	 */
	public function get_available_user_addresses( $user, $include_default = true ) {
		$addresses           = $this->get_user_addresses( $user, $include_default );
		$available_countries = array_keys( WC()->countries->get_shipping_countries() );

		return array_filter(
			$addresses,
			function( $addr ) use ( $available_countries ) {
				return isset( $addr['shipping_country'] ) && in_array( $addr['shipping_country'], $available_countries, true );
			}
		);
	}

	/**
	 * Sorting the array.
	 *
	 * @param array    $existing_array Array value.
	 * @param string   $on type of address field.
	 * @param constant $order type of sorting.
	 *
	 * @return array Sorted array.
	 */
	public function array_sort( $existing_array, $on, $order = SORT_ASC ) {
		$new_array      = array();
		$sortable_array = array();

		if ( is_array( $existing_array ) && 0 < count( $existing_array ) ) {
			foreach ( $existing_array as $k => $v ) {
				if ( is_array( $v ) ) {
					foreach ( $v as $k2 => $v2 ) {
						if ( $k2 === $on ) {
							$sortable_array[ $k ] = $v2;
						}
					}
				} else {
					$sortable_array[ $k ] = $v;
				}
			}

			switch ( $order ) {
				case SORT_ASC:
					asort( $sortable_array, SORT_NATURAL | SORT_FLAG_CASE );
					break;
				case SORT_DESC:
					arsort( $sortable_array, SORT_NATURAL | SORT_FLAG_CASE );
					break;
			}

			foreach ( $sortable_array as $k => $v ) {
				$new_array[ $k ] = $existing_array[ $k ];
			}
		}

		return $new_array;
	}

	/**
	 * Save user addresses to account or session
	 * Removes the default addresses and any duplicate addresses
	 *
	 * @param  integer $user_id    Customer user ID.
	 * @param  array   $addresses  List of user addresses.
	 */
	public function save_user_addresses( $user_id, $addresses ) {

		$keys = array();

		foreach ( $addresses as $index => $address ) {
			$key = $this->unique_address_key( $address );

			if ( ! empty( $address['default_address'] ) ) {
				// Remove default address.
				unset( $addresses[ $index ] );
			} elseif ( false !== $key ) {
				// Save unique address key.
				$keys[ $index ] = $key;
			} else {
				// Remove empty address.
				unset( $addresses[ $index ] );
			}
		}

		// Remove any duplicate addresses.
		$duplicates = array_diff_assoc( $keys, array_unique( $keys ) );
		foreach ( array_keys( $duplicates ) as $index ) {
			unset( $addresses[ $index ] );
		}

		if ( $user_id > 0 ) {
			update_user_meta( $user_id, 'wc_other_addresses', $addresses );
		} else {
			wcms_session_set( 'user_addresses', $addresses );
		}
	}

	/**
	 * Check if the checkout address fields is eligible for saving or not.
	 *
	 * @return boolean.
	 */
	public function is_checkout_address_eligible_for_saving() {
		$shipping_address_fields = wc()->checkout->get_checkout_fields( 'shipping' );
		$count_unempty_address   = 0;

		foreach ( $shipping_address_fields as $key => $value ) {
			$address_value = wc()->checkout->get_value( $key );
			if ( empty( $address_value ) || 'phone' === $key ) {
				continue;
			}

			$count_unempty_address++;
		}

		// At least 5 address fields are not empty.
		return $count_unempty_address >= 5;
	}

	/**
	 * Save checkout address value.
	 *
	 * @param int $user_id User ID.
	 *
	 * @return void.
	 */
	public function save_checkout_address( int $user_id ) {
		$shipping_address_fields = wc()->checkout->get_checkout_fields( 'shipping' );

		if ( ! $this->is_checkout_address_eligible_for_saving() ) {
			return;
		}

		if ( 0 !== $user_id ) {
			// For logged-in user.
			$customer = new WC_Customer( $user_id );

			foreach ( $shipping_address_fields as $key => $value ) {
				$address_value = wc()->checkout->get_value( $key );
				$customer->{ 'set_' . $key }( $address_value );
			}

			$customer->save();
		} else {
			// For guest user.
			$addresses = array();
			foreach ( $shipping_address_fields as $key => $value ) {
				$address_value = wc()->checkout->get_value( $key );
				$addresses[0][ $key ] = $address_value;
			}
			
			$this->save_user_addresses( $user_id, $addresses );
		}
	}

	/**
	 * Check if user has saved shipping address or not.
	 *
	 * @param int $user_id User ID.
	 *
	 * @return boolean.
	 */
	public function user_has_shipping_address( int $user_id ) {
		$customer        = new WC_Customer( $user_id );
		$unempty_address = array_filter(
			$customer->get_shipping(),
			function( $shipping_value, $key ) {
				return ! empty( $shipping_value ) && 'phone' !== $key;
			},
			ARRAY_FILTER_USE_BOTH
		);

		// At least 5 address fields not empty to be valid.
		return count( $unempty_address ) >= 5;
	}

	/**
	 * Generate a unique key for an address
	 *
	 * @param  array $address Address field values.
	 *
	 * @return string Unique key
	 */
	public function unique_address_key( $address ) {

		if ( empty( $address ) || ! is_array( $address ) ) {
			return false;
		}

		return md5( implode( '_', $address ) );
	}
}
