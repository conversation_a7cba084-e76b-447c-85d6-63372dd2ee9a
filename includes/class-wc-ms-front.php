<?php
/**
 * Class WC_MS_Front file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WC_MS_Front class.
 */
class WC_MS_Front {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Country
	 *
	 * @var string
	 */
	private $country;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// WCMS Front.
		add_filter( 'body_class', array( $this, 'output_body_class' ) );
		add_action( 'wp_enqueue_scripts', array( $this, 'front_scripts' ), 11 );
		add_action( 'woocommerce_email_after_order_table', array( $this, 'email_order_item_addresses' ), 10, 3 );
		add_action( 'woocommerce_order_details_after_order_table', array( $this, 'list_order_item_addresses' ) );

		// cleanup.
		add_action( 'wp_logout', array( $this->wcms, 'clear_session' ) );

		add_action( 'plugins_loaded', array( $this, 'load_account_addresses' ), 11 );

		// inline script.
		add_action( 'wp_footer', array( $this, 'inline_scripts' ) );
	}

	/**
	 * Load account addresses by adding a filter and action hooks.
	 */
	public function load_account_addresses() {
		add_filter( 'woocommerce_my_account_get_addresses', array( $this, 'account_address_labels' ), 10, 2 );
		add_filter( 'woocommerce_my_account_my_address_formatted_address', array( $this, 'account_address_formatted' ), 10, 3 );

		add_filter( 'woocommerce_my_account_edit_address_field_value', array( $this, 'edit_address_field_value' ), 10, 3 );
		add_action( 'template_redirect', array( $this, 'save_address' ), 1 );

		// Delete address in edit address page.
		add_action( 'woocommerce_before_edit_account_address_form', array( $this, 'delete_address_button' ) );
		add_action( 'wp_loaded', array( $this, 'delete_address_action' ), 20 );

		// Add address button on my account addresses page.
		add_action( 'woocommerce_account_edit-address_endpoint', array( $this, 'add_address_button' ), 90 );

		// Initialize address fields.
		add_action( 'woocommerce_account_content', array( $this, 'init_address_fields' ), 1 );
	}

	/**
	 * Add woocommerce and woocommerce-page classes to the body tag of WCMS pages
	 *
	 * @param array $classes Registered body css class.
	 *
	 * @return array
	 */
	public function output_body_class( $classes ) {
		if ( is_page( wc_get_page_id( 'multiple_addresses' ) ) || is_page( wc_get_page_id( 'account_addresses' ) ) ) {
			$classes[] = 'woocommerce';
			$classes[] = 'woocommerce-page';
		}

		if ( WC_Blocks_Utils::has_block_in_page( get_the_ID(), 'woocommerce/checkout' ) ) {
			$classes[] = 'wcms-checkout-blocks';
		}

		if ( WC_Blocks_Utils::has_block_in_page( get_the_ID(), 'woocommerce/cart' ) ) {
			$classes[] = 'wcms-cart-blocks';
		}

		if ( $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() ) {
			$classes[] = 'wcms-activated';
		}

		return $classes;
	}

	/**
	 * Enqueue scripts and styles for the frontend
	 */
	public function front_scripts() {
		global $post;

		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		$page_ids = array(
			wc_get_page_id( 'account_addresses' ),
			wc_get_page_id( 'multiple_addresses' ),
			wc_get_page_id( 'myaccount' ),
			wc_get_page_id( 'checkout' ),
			wc_get_page_id( 'cart' ),
		);

		if ( ! $post || ( $post && ! in_array( $post->ID, $page_ids, true ) ) ) {
			return;
		}

		$user = wp_get_current_user();

		wp_enqueue_script( 'jquery' );
		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'jquery-ui-mouse' );
		wp_enqueue_script( 'jquery-ui-draggable' );
		wp_enqueue_script( 'jquery-ui-droppable' );
		wp_enqueue_script( 'jquery-ui-datepicker' );
		wp_enqueue_script( 'jquery-masonry' );
		wp_enqueue_script( 'thickbox' );
		wp_enqueue_style( 'thickbox' );
		wp_enqueue_script( 'jquery-blockui' );

		// Touchpunch to support mobile browsers.
		wp_enqueue_script( 'jquery-ui-touch-punch', plugins_url( 'assets/js/jquery.ui.touch-punch' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery-ui-mouse', 'jquery-ui-widget' ), '0.2.3' );// phpcs:ignore --- This third party library need to be on top.

		if ( 0 !== $user->ID ) {
			$page_id = wc_get_page_id( 'account_addresses' );
			$url     = get_permalink( $page_id );
			$url     = add_query_arg( 'height', '400', add_query_arg( 'width', '400', add_query_arg( 'addressbook', '1', $url ) ) );
			?>
			<script type="text/javascript">
				var address = null;
				var wc_ship_url = '<?php echo esc_url( $url ); ?>';
			</script>
			<?php
		}

		wp_enqueue_script( 'jquery-tiptip', plugins_url( 'assets/js/jquery.tiptip' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery', 'jquery-ui-core' ), '1.3' );// phpcs:ignore --- This third party library need to be on top.

		wp_enqueue_script( 'modernizr', plugins_url( 'assets/js/modernizr' . $suffix . '.js', WC_Ship_Multiple::FILE ), array(), '2.6.2' );// phpcs:ignore --- This third party library need to be on top.

		$id                = wc_get_page_id( 'multiple_addresses' );
		$reset_url         = add_query_arg(
			array(
				'wcms_reset_address' => true,
				'nonce'              => wp_create_nonce( 'wcms_reset_address_security' ),
			),
			wc_get_checkout_url()
		);
		$modify_addr_link  = get_permalink( $id );
		$add_addr_link     = add_query_arg( 'cart', 1, get_permalink( $id ) );
		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		$sess_cart_address = wcms_session_get( 'cart_addresses' );
		$has_item_address  = ( ! wcms_session_isset( 'cart_item_addresses' ) || empty( $sess_item_address ) ) ? false : true;
		$has_cart_address  = ( ! wcms_session_isset( 'cart_addresses' ) || empty( $sess_cart_address ) ) ? false : true;
		$no_multi_address  = ( ! $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() );

		wp_enqueue_script( 'multiple_shipping_checkout', plugins_url( 'assets/js/woocommerce-checkout' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'woocommerce', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-mouse' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_localize_script(
			'multiple_shipping_checkout',
			'WCMS',
			/**
			 * Filter to manipulate WCMS js variable on checkout page.
			 *
			 * @param array List of JS variable.
			 *
			 * @since 3.3.16
			 */
			apply_filters(
				'wc_ms_checkout_locale',
				array(
					// URL to wp-admin/admin-ajax.php to process the request.
					'ajaxurl'           => admin_url( 'admin-ajax.php' ),
					'base_url'          => plugins_url( '', WC_Ship_Multiple::FILE ),
					'wc_url'            => WC()->plugin_url(),
					'countries'         => wp_json_encode( array_merge( WC()->countries->get_allowed_country_states(), WC()->countries->get_shipping_country_states() ) ),
					'select_state_text' => esc_attr__( 'Select an option&hellip;', 'woocommerce-shipping-multiple-addresses' ),
					'_wcmsnonce'        => wp_create_nonce( 'wcms-action_' . WC()->session->get_customer_unique_id() ),
					'not_eligible_wcms' => ( ! $this->wcms->cart->cart_is_eligible_for_multi_shipping() ),
					'has_item_address'  => $has_item_address,
					'has_cart_address'  => $has_cart_address,
					'no_multi_address'  => $no_multi_address,
					'reset_url'         => esc_url( $reset_url ),
					'modify_addr_link'  => esc_url( $modify_addr_link ),
					'add_addr_link'     => esc_url( $add_addr_link ),
					'save_nonce'        => wp_create_nonce( 'wcms_save_billing_fields_nonce' ),
					'modify_title_text' => esc_html__( 'Shipping Address', 'woocommerce-shipping-multiple-addresses' ),
					'modify_addr_text'  => esc_html__( 'Modify/Add Address', 'woocommerce-shipping-multiple-addresses' ),
					'reset_addr_text'   => esc_html__( 'Reset Address', 'woocommerce-shipping-multiple-addresses' ),
				)
			)
		);

		if ( ! is_checkout() ) {
			wp_register_script( 'wcms-country-select', plugins_url( 'assets/js/country-select' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery', 'selectWoo', 'select2' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
			wp_localize_script(
				'wcms-country-select',
				'wcms_country_select_params',
				/**
				 * Filter to manipulate the countries parameters on JS.
				 *
				 * @param array JSON encoded list of countries and select text.
				 *
				 * @since 3.3.19
				 */
				apply_filters(
					'wc_country_select_params',
					array(
						'countries'              => wp_json_encode( array_merge( WC()->countries->get_allowed_country_states(), WC()->countries->get_shipping_country_states() ) ),
						'i18n_select_state_text' => esc_attr__( 'Select an option&hellip;', 'woocommerce-shipping-multiple-addresses' ),
					)
				)
			);
			wp_enqueue_script( 'wc-address-i18n' );
			wp_enqueue_script( 'wcms-country-select' );
			wp_enqueue_style( 'select2', WC()->plugin_url() . '/assets/css/select2.css', array(), WC_VERSION );
		}

		wp_enqueue_style( 'multiple_shipping_styles', plugins_url( 'assets/css/front.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );
		wp_enqueue_style( 'tiptip', plugins_url( 'assets/css/jquery.tiptip.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );

		global $wp_scripts;
		$ui_version = $wp_scripts->registered['jquery-ui-core']->ver;
		wp_enqueue_style( 'jquery-ui-css', "//ajax.googleapis.com/ajax/libs/jqueryui/{$ui_version}/themes/ui-lightness/jquery-ui.min.css", array(), $ui_version );

		// Address validation support.
		if ( class_exists( 'WC_Address_Validation' ) && is_page( wc_get_page_id( 'multiple_addresses' ) ) ) {
			$this->enqueue_address_validation_scripts();
		}

		// On the thank you page, remove the Shipping Address block if the order ships to multiple addresses.
		$order_id = isset( $_GET['view-order'] ) ? intval( $_GET['view-order'] ) : 0; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$order_id = isset( $_GET['order-received'] ) ? intval( $_GET['order-received'] ) : $order_id; // phpcs:ignore WordPress.Security.NonceVerification.Recommended

		if ( ! empty( $order_id ) ) {
			$order = wc_get_order( $order_id );

			if ( ! $order ) {
				return;
			}

			$packages  = $order->get_meta( '_wcms_packages' );
			$multiship = $order->get_meta( '_multiple_shipping' );

			if ( ( is_array( $packages ) && 1 < count( $packages ) ) || 'yes' === $multiship ) {
				wp_enqueue_script( 'wcms_shipping_address_override', plugins_url( 'assets/js/address-override' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
			}
		}

		wp_register_script( 'wcms-address-selection', plugins_url( 'assets/js/address-selection' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'woocommerce' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
	}

	/**
	 * Address Validation scripts
	 */
	public function enqueue_address_validation_scripts() {
		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		if ( function_exists( 'wc_address_validation' ) ) {
			$validator = wc_address_validation();
			$handler   = $validator->get_handler_instance();
		} else {
			$validator = $GLOBALS['wc_address_validation'];
			$handler   = $validator->handler;
		}

		$params = array(
			'nonce'                 => wp_create_nonce( 'wc_address_validation' ),
			'debug_mode'            => 'yes' === get_option( 'wc_address_validation_debug_mode' ),
			'force_postcode_lookup' => 'yes' === get_option( 'wc_address_validation_force_postcode_lookup' ),
			'ajax_url'              => admin_url( 'admin-ajax.php', 'relative' ),
		);

		// Load postcode lookup JS.
		$provider = $handler->get_active_provider();

		if ( $provider && $provider->supports( 'postcode_lookup' ) ) {
			wp_enqueue_script( 'wc_address_validation_postcode_lookup', $validator->get_plugin_url() . '/assets/js/frontend/wc-address-validation-postcode-lookup' . $suffix . '.js', array( 'jquery', 'woocommerce' ), WC_Address_Validation::VERSION, true );
			wp_localize_script( 'wc_address_validation_postcode_lookup', 'wc_address_validation_postcode_lookup', $params );
		}

		// Load address validation JS.
		if ( $provider && $provider->supports( 'address_validation' ) && 'WC_Address_Validation_Provider_SmartyStreets' === get_class( $provider ) ) {

			// Load SmartyStreets LiveAddress jQuery plugin.
			wp_enqueue_script( 'wc_address_validation_smarty_streets', '//d79i1fxsrar4t.cloudfront.net/jquery.liveaddress/2.4/jquery.liveaddress.min.js', array( 'jquery' ), '2.4', true );

			wp_enqueue_script( 'wcms_address_validation', plugins_url( 'assets/js/address-validation' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );

			$params['smarty_streets_key'] = $provider->html_key;

			wp_localize_script( 'wcms_address_validation', 'wc_address_validation', $params );

			// Add a bit of CSS to fix address correction popup from expanding to page width because of Chosen selects.
			echo '<style type="text/css">.chzn-done{position:absolute!important;visibility:hidden!important;display:block!important;width:120px!important;</style>';
		}

		/**
		 * Allow other providers to load JS.
		 *
		 * @param array  $provider Third party provider.
		 * @param object $handler Validator handler.
		 * @param string $suffix Suffix of the script or style file. Usually '.min'.
		 *
		 * @since 3.3.16
		 */
		do_action( 'wc_address_validation_load_js', $provider, $handler, $suffix );
	}

	/**
	 * Prints the email table of items and their shipping addresses.
	 *
	 * @param WC_Order $order Order object or order ID.
	 * @param boolean  $sent_to_admin Whether it is sent to admin or not.
	 * @param boolean  $plain_text Whether the format is plain or HTML.
	 */
	public function email_order_item_addresses( $order, $sent_to_admin, $plain_text ) {
		/**
		 * Action to add element on shipping package table.
		 *
		 * @param int     $order_id Order ID.
		 * @param boolean Is email or not.
		 * @param boolean Is plain text or not.
		 *
		 * @since 2.1.8
		 */
		do_action( 'wcms_order_shipping_packages_table', $order, true, $plain_text );
	}

	/**
	 * Prints the table of items and their shipping addresses.
	 *
	 * @param int|WC_Order $order_id Order ID or order object.
	 */
	public function list_order_item_addresses( $order_id ) {
		/**
		 * Action to add element on shipping package table.
		 *
		 * @param int     $order_id Order ID.
		 * @param boolean Is email or not.
		 * @param boolean Is plain text or not.
		 *
		 * @since 2.1.8
		 */
		do_action( 'wcms_order_shipping_packages_table', $order_id, false, false );
	}

	/**
	 * Get account address labels.
	 *
	 * @param array $labels Address labels.
	 * @param int   $customer_id Customer ID.
	 *
	 * @return array
	 */
	public function account_address_labels( $labels, $customer_id ) {
		$user      = get_user_by( 'id', $customer_id );
		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		$address_id = 0;

		foreach ( $addresses as $index => $address ) {
			++$address_id;

			// translators: %d is address id.
			$labels[ 'wcms_address_' . $index ] = sprintf( __( 'Shipping address %d', 'woocommerce-shipping-multiple-addresses' ), $address_id );
		}

		return $labels;
	}

	/**
	 * Get account address in formatted array.
	 *
	 * @param array  $address Current address.
	 * @param int    $customer_id Customer ID.
	 * @param string $address_id Address ID.
	 *
	 * @return array
	 */
	public function account_address_formatted( $address, $customer_id, $address_id ) {
		if ( strpos( $address_id, 'wcms_address_' ) === 0 ) {
			$user      = get_user_by( 'id', $customer_id );
			$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

			$parts = explode( '_', $address_id );
			$index = $parts[2];

			if ( isset( $addresses[ $index ] ) ) {
				$account_address = $addresses[ $index ];

				foreach ( $account_address as $key => $value ) {
					$key                     = str_replace( 'shipping_', '', $key );
					$account_address[ $key ] = $value;
				}

				$address = $account_address;
			}
		}

		return $address;
	}

	/**
	 * Edit the address field value.
	 *
	 * @param mixed  $value Field value.
	 * @param string $key Field key.
	 * @param string $load_address Load address.
	 *
	 * @return mixed
	 */
	public function edit_address_field_value( $value, $key, $load_address ) {
		if ( strpos( $load_address, 'wcms_address_' ) === 0 ) {
			$parts = explode( '_', $load_address );
			$index = $parts[2];

			if ( 'new' === $index ) {
				// No need to do nonce verification. No saving data operation here.
				return empty( $_POST[ $key ] ) ? '' : wc_clean( $_POST[ $key ] );// phpcs:ignore
			}

			$user      = wp_get_current_user();
			$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

			if ( ! isset( $addresses[ $index ] ) ) {
				return $value;
			}

			$key   = str_replace( $load_address, 'shipping', $key );
			$value = $addresses[ $index ][ $key ];
		}

		return $value;
	}

	/**
	 * Save and update a billing or shipping address if the
	 * form was submitted through the user account page.
	 * Copied from WC_Form_Handler::save_address and modified to save to address book
	 */
	public function save_address() {
		global $wp;

		$request_method = isset( $_SERVER['REQUEST_METHOD'] ) ? sanitize_text_field( wp_unslash( $_SERVER['REQUEST_METHOD'] ) ) : '';
		if ( 'POST' !== strtoupper( $request_method ) ) {
			return;
		}

		$nonce = isset( $_POST['woocommerce-edit-address-nonce'] ) ? sanitize_text_field( wp_unslash( $_POST['woocommerce-edit-address-nonce'] ) ) : '';
		if ( ! wp_verify_nonce( $nonce, 'woocommerce-edit_address' ) ) {
			return;
		}

		if ( empty( $_POST['action'] ) || 'edit_address' !== $_POST['action'] ) {
			return;
		}

		$user_id = get_current_user_id();

		if ( $user_id <= 0 ) {
			return;
		}

		$load_address = isset( $wp->query_vars['edit-address'] ) ? wc_edit_address_i18n( sanitize_title( $wp->query_vars['edit-address'] ), true ) : 'billing';

		// Only save our own addresses.
		if ( strpos( $load_address, 'wcms_address_' ) !== 0 ) {
			return;
		}

		$shipping_country  = isset( $_POST['shipping_country'] ) ? sanitize_text_field( wp_unslash( $_POST['shipping_country'] ) ) : '';
		$address           = WC()->countries->get_address_fields( esc_attr( $shipping_country ), 'shipping_' );
		$post_load_country = isset( $_POST[ $load_address . '_country' ] ) ? wc_clean( wp_unslash( $_POST[ $load_address . '_country' ] ) ) : '';
		$post_fields       = array();

		foreach ( $address as $key => $field ) {

			if ( ! isset( $field['type'] ) ) {
				$field['type'] = 'text';
			}

			// Get Value.
			switch ( $field['type'] ) {
				case 'checkbox':
					$post_fields[ $key ] = intval( isset( $_POST[ $key ] ) );
					break;
				default:
					$post_fields[ $key ] = isset( $_POST[ $key ] ) ? wc_clean( wp_unslash( $_POST[ $key ] ) ) : '';
					break;
			}

			/**
			 * Filter to manipulate the POST field value.
			 *
			 * @param mixed POST value of the field.
			 *
			 * @since 3.3.18
			 */
			$post_fields[ $key ] = apply_filters( 'woocommerce_process_myaccount_field_' . $key, $post_fields[ $key ] );

			// Validation: Required fields.
			if ( ! empty( $field['required'] ) && empty( $post_fields[ $key ] ) ) {
				// translators: %s is a field label.
				wc_add_notice( sprintf( __( '%s is a required field.', 'woocommerce-shipping-multiple-addresses' ), $field['label'] ), 'error' );
			}

			if ( ! empty( $post_fields[ $key ] ) ) {

				// Validation rules.
				if ( ! empty( $field['validate'] ) && is_array( $field['validate'] ) ) {
					foreach ( $field['validate'] as $rule ) {
						switch ( $rule ) {
							case 'postcode':
								$post_fields[ $key ] = trim( $post_fields[ $key ] );

								if ( empty( $post_load_country ) ) {
									continue 2;
								}

								if ( ! WC_Validation::is_postcode( $post_fields[ $key ], $post_load_country ) ) {
									wc_add_notice( esc_html__( 'Please enter a valid postcode / ZIP.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								} else {
									$post_fields[ $key ] = wc_format_postcode( $post_fields[ $key ], $post_load_country );
								}
								break;
							case 'phone':
								$post_fields[ $key ] = wc_format_phone_number( $post_fields[ $key ] );

								if ( ! WC_Validation::is_phone( $post_fields[ $key ] ) ) {
									// translators: %s is a label name.
									wc_add_notice( sprintf( esc_html__( '%s is not a valid phone number.', 'woocommerce-shipping-multiple-addresses' ), '<strong>' . $field['label'] . '</strong>' ), 'error' );
								}
								break;
							case 'email':
								$post_fields[ $key ] = strtolower( $post_fields[ $key ] );

								if ( ! is_email( $post_fields[ $key ] ) ) {
									// translators: %s is a field label.
									wc_add_notice( sprintf( esc_html__( '%s is not a valid email address.', 'woocommerce-shipping-multiple-addresses' ), '<strong>' . $field['label'] . '</strong>' ), 'error' );
								}
								break;
						}
					}
				}
			}
		}

		$customer = new WC_Customer( $user_id );

		/**
		 * Hook: woocommerce_after_save_address_validation.
		 *
		 * Allow developers to add custom validation logic and throw an error to prevent save.
		 *
		 * @since 3.6.0
		 * @param int         $user_id User ID being saved.
		 * @param string      $load_address Type of address; 'billing' or 'shipping'.
		 * @param array       $address The address fields.
		 * @param WC_Customer $customer The customer object being saved.
		 */
		do_action( 'woocommerce_after_save_address_validation', $user_id, $load_address, $address, $customer );

		if ( 0 === wc_notice_count( 'error' ) ) {

			$user        = new WP_User( $user_id );
			$addresses   = $this->wcms->address_book->get_user_addresses( $user, false );
			$parts       = explode( '_', $load_address );
			$index       = $parts[2];
			$new_address = array();

			foreach ( $address as $key => $field ) {
				$new_address[ $key ] = sanitize_text_field( wp_unslash( $_POST[ $key ] ) );
			}

			if ( 'new' === $index ) {
				$addresses[] = $new_address;
				end( $addresses );
				$index = key( $addresses );
				wc_add_notice( __( 'Address added successfully.', 'woocommerce-shipping-multiple-addresses' ) );
			} else {
				$addresses[ $index ] = $new_address;
				wc_add_notice( __( 'Address changed successfully.', 'woocommerce-shipping-multiple-addresses' ) );
			}

			$default_address = $this->wcms->address_book->get_user_default_address( $user->ID );

			if ( $default_address['address_1'] && $default_address['postcode'] ) {
				array_unshift( $addresses, $default_address );
			}

			$this->wcms->address_book->save_user_addresses( $user_id, $addresses );

			/**
			 * Action after user address is saved to WCMS address book.
			 *
			 * @param int    $user_id User ID.
			 * @param string $load_address Address key that's being loaded.
			 *
			 * @since 3.3.18
			 */
			do_action( 'woocommerce_customer_save_address', $user_id, $load_address );

			wp_safe_redirect( wc_get_endpoint_url( 'edit-address', '', wc_get_page_permalink( 'myaccount' ) ) );
			exit;
		}

		// Prevent WC_Form_Handler::save_address.
		unset( $_POST['action'] );
	}

	/**
	 * Generate inline scripts for wp_footer
	 */
	public function inline_scripts() {
		$order_id = isset( $_GET['order'] ) ? intval( $_GET['order'] ) : false; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$order    = wc_get_order( $order_id );

		if ( $order ) {
			if ( method_exists( $order, 'get_checkout_order_received_url' ) ) {
				$page_id = $order->get_checkout_order_received_url();
			} else {
				$page_id = wc_get_page_id( get_option( 'woocommerce_thanks_page_id', 'thanks' ) );
			}

			$shipping_addresses = $order->get_meta( '_shipping_addresses', false );

			if ( is_page( $page_id ) && ! empty( $shipping_addresses ) ) {
				$html     = '<div>';
				$packages = $order->get_meta( '_wcms_packages' );

				foreach ( $packages as $package ) {
					$html .= '<address>' . wcms_get_formatted_address( $package['destination'] ) . '</address><br /><hr/>';
				}
				$html .= '</div>';
				$html  = str_replace( '"', '\"', $html );
				$html  = str_replace( "\n", ' ', $html );
				?>
				<script type="text/javascript">
					jQuery(document).ready(function() {
						jQuery(jQuery("address")[1]).replaceWith("<?php echo esc_js( $html ); ?>");
					});
				</script>
				<?php
			}
		}
	}

	/**
	 * Add a delete address button on the edit address page
	 */
	public function delete_address_button() {
		$address      = get_query_var( 'edit-address' );
		$edit_address = wc_get_endpoint_url( 'edit-address' );

		// Only show on multiple addresses.
		if ( 0 !== strpos( $address, 'wcms_address_' ) || empty( $edit_address ) ) {
			return;
		}

		$remove_link = wp_nonce_url( add_query_arg( 'remove_address', $address, $edit_address ), 'wcms-delete-address' );
		printf( '<a href="%1$s" class="remove delete-address-button" aria-label="%2$s">&times;</a>', esc_url( $remove_link ), esc_html__( 'Delete address', 'woocommerce-shipping-multiple-addresses' ) );
	}

	/**
	 * Handle the delete address action
	 */
	public function delete_address_action() {
		$nonce = isset( $_GET['_wpnonce'] ) ? sanitize_text_field( wp_unslash( $_GET['_wpnonce'] ) ) : '';
		if ( ! wp_verify_nonce( $nonce, 'wcms-delete-address' ) ) {
			return;
		}

		if ( ! empty( $_GET['remove_address'] ) ) {

			$user = wp_get_current_user();
			if ( $user->ID ) {
				$address   = wc_clean( wp_unslash( $_GET['remove_address'] ) );
				$index     = ( 0 === strpos( $address, 'wcms_address_' ) ) ? substr( $address, 13 ) : '';
				$addresses = $this->wcms->address_book->get_user_addresses( $user );

				if ( isset( $addresses[ $index ] ) ) {
					unset( $addresses[ $index ] );
					$this->wcms->address_book->save_user_addresses( $user->ID, $addresses );
					wc_add_notice( __( 'Deleted address', 'woocommerce-shipping-multiple-addresses' ) );
				} else {
					wc_add_notice( __( 'Address could not be found', 'woocommerce-shipping-multiple-addresses' ), 'error' );
				}

				// Redirect to edit address page.
				wp_safe_redirect( wc_get_account_endpoint_url( 'edit-address' ) );
				exit;
			}
		}
	}

	/**
	 * Add address button on my account page
	 */
	public function add_address_button() {
		$address = get_query_var( 'edit-address' );

		if ( empty( $address ) ) {
			$url = wc_get_endpoint_url( 'edit-address', 'wcms_address_new' );
			printf( '<a href="%s" class="button">%s</a>', esc_url( $url ), esc_html__( 'Add address', 'woocommerce-shipping-multiple-addresses' ) );
		}
	}

	/**
	 * Init address fields
	 */
	public function init_address_fields() {
		$address = get_query_var( 'edit-address' );

		if ( 0 === strpos( $address, 'wcms_address_' ) ) {
			add_filter( 'woocommerce_' . $address . '_fields', array( $this, 'country_address_fields' ), 10, 2 );

			// Override checkout value for states field, see following issue for better way to resolve this:
			// https://github.com/woocommerce/woocommerce/issues/15632.
			add_filter( 'woocommerce_checkout_get_value', array( $this, 'country_address_value' ), 10, 2 );
		}
	}

	/**
	 * Override address fields with country specific ones.
	 *
	 * @param array  $address_fields Address fields.
	 * @param string $country Address country.
	 *
	 * @return string
	 */
	public function country_address_fields( $address_fields, $country ) {
		$address_country = $this->get_address_country();
		if ( false !== $address_country ) {
			$country = $address_country;
		}

		return WC()->countries->get_address_fields( $country, 'shipping_' );
	}


	/**
	 * Override address country field (to show correct list of states).
	 *
	 * @param mixed  $value Filter value.
	 * @param string $input Input field name.
	 *
	 * @return mixed
	 */
	public function country_address_value( $value, $input ) {
		if ( 'shipping_country' === $input ) {
			$country = $this->get_address_country();
			if ( false !== $country ) {
				return $country;
			}
		}
		return $value;
	}


	/**
	 * Helper function to get address country
	 * Saves it in the class to prevent multiple lookups
	 */
	public function get_address_country() {
		if ( ! empty( $this->country ) ) {
			return $this->country;
		}

		$user      = wp_get_current_user();
		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		$address = get_query_var( 'edit-address' );
		$parts   = explode( '_', $address );
		$index   = $parts[2];

		if ( isset( $addresses[ $index ] ) && ! empty( $addresses[ $index ]['shipping_country'] ) ) {
			$this->country = $addresses[ $index ]['shipping_country'];
			return $this->country;
		}

		return false;
	}
}
