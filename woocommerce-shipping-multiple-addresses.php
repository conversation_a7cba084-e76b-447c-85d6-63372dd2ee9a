<?php
/**
 * Plugin Name: WooCommerce Ship to Multiple Addresses
 * Plugin URI: https://woocommerce.com/products/shipping-multiple-addresses/
 * Description: Allow customers to ship orders with multiple products or quantities to separate addresses instead of forcing them to place multiple orders for different delivery addresses.
 * Version: 4.2.9
 * Author: WooCommerce
 * Author URI: https://woocommerce.com
 * Text Domain: woocommerce-shipping-multiple-addresses
 * Domain Path: /languages
 * Requires Plugins: woocommerce
 * Requires PHP: 7.4
 * Requires at least: 6.7
 * Tested up to: 6.8
 * WC requires at least: 9.9
 * WC tested up to: 10.1
 * Woo: 18741:aa0eb6f777846d329952d5b891d6f8cc
 *
 * Copyright 2020 WooCommerce.
 * License: GNU General Public License v3.0
 * License URI: http://www.gnu.org/licenses/gpl-3.0.html
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! defined( 'WC_MS_FILE' ) ) {
	define( 'WC_MS_FILE', __FILE__ );
}

if ( ! defined( 'WC_MS_ABSPATH' ) ) {
	define( 'WC_MS_ABSPATH', trailingslashit( __DIR__ ) );
}

define( 'WC_MS_PLUGIN_URL', trailingslashit( plugins_url( '', __FILE__ ) ) );
define( 'WC_MS_DIST_DIR', WC_MS_ABSPATH . 'dist/' );
define( 'WC_MS_DIST_URL', WC_MS_PLUGIN_URL . 'dist/' );

require_once WC_MS_ABSPATH . 'class-wc-ms-compatibility.php';

/**
 * WooCommerce fallback notice.
 *
 * @since 3.6.15
 * @return void
 */
function woocommerce_shipping_multiple_addresses_missing_wc_notice() {
	/* translators: %s WC download URL link. */
	echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'Multiple Addresses requires WooCommerce to be installed and active. You can download %s here.', 'woocommerce-shipping-multiple-addresses' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</strong></p></div>';
}

if ( ! class_exists( 'WC_Ship_Multiple' ) ) :
	define( 'WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION', '4.2.9' ); // WRCS: DEFINED_VERSION.
	include_once WC_MS_ABSPATH . 'includes/class-wc-ship-multiple.php';
endif;

add_action( 'plugins_loaded', 'woocommerce_shipping_multiple_addresses_init' );

/**
 * Declare compatibility with WC Cart and Checkout blocks.
 */
function woocommerce_shipping_multiple_addresses_blocks_compatibility() {
	if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'cart_checkout_blocks', WC_MS_FILE, true );
	}
}

/**
 * Initializes the extension.
 *
 * @since 3.6.15
 * @return void
 */
function woocommerce_shipping_multiple_addresses_init() {
	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'woocommerce_shipping_multiple_addresses_missing_wc_notice' );
		return;
	}

	add_action( 'before_woocommerce_init', 'woocommerce_shipping_multiple_addresses_blocks_compatibility' );

	$GLOBALS['wcms'] = new WC_Ship_Multiple();
}
