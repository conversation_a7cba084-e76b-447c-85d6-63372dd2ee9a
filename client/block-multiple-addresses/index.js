/**
 * Multiple Addresses Shipping Options Block
 *
 * This file is responsible for rendering selected shipping addresses and options from the Multiple Addresses Shipping plugin.
 *
 * @package WC_Shipping_Multiple_Addresses
 */

const { registerPlugin }                                         = window.wp.plugins;
const { ExperimentalOrderShippingPackages, extensionCartUpdate } = window.wc.blocksCheckout;
const { CART_STORE_KEY }                                         = window.wc.wcBlocksData;
const { RawHTML, useState, useEffect }                           = window.wp.element;
const { dispatch }                                               = window.wp.data;
const { Button, Textarea, CheckboxControl, TextInput }           = window.wc.blocksComponents;

import './style.scss';

const onClickSetWCMSButton = ( data_url ) => {
	localStorage.removeItem( 'ms_note' );
	localStorage.removeItem( 'ms_date' );
	localStorage.removeItem( 'ms_gift' );

	location.href = data_url;
}

const maybeGetWCMSButtonHTML = ( staticVar ) => {
	if ( staticVar.has_multi_address || ! staticVar.is_eligible_wcms ) {
		return '';
	}

	return (
		<div className="wcms-set-button-container">
			<p className="wcms-block-notification" href={ staticVar.modify_addr_link }>{ staticVar.lang_notification }</p>
			<div className="wcms-block-button">
				<Button
					className="wcms-block-button-set"
					onClick={ ( e ) => onClickSetWCMSButton( staticVar.modify_addr_link ) }
					label={ staticVar.lang_button }
				>{ staticVar.lang_button }</Button>
			</div>
		</div>
	);
};

const getProductLI = ( product, index ) => {
	return (
		<li key={index}>
			<div className="product-label"><RawHTML>{ product.title }</RawHTML></div>
			<div className="product-attribute"><RawHTML>{ product.attribute }</RawHTML></div>
		</li>
	);
};

const getShippingMethodHTML = ( chosenShippingMethod, noMethodText ) => {
	if ( chosenShippingMethod.length < 1 ) {
		return (
			<div className="shipping-error">{ noMethodText }</div>
		);
	}

	return (
		<div className="shipping-method">
			<span className="shipping-method-label"><RawHTML>{ chosenShippingMethod.option_label }</RawHTML></span>
		</div>
	);
};

var delayTimer = 0;

const maybeGetShippingFieldsHTML = ( data, index, staticVar, namespace ) => {
	const msNote = ( localStorage.getItem( 'ms_note' ) ) ? JSON.parse( localStorage.getItem( 'ms_note' ) ) : {};
	const msDate = ( localStorage.getItem( 'ms_date' ) ) ? JSON.parse( localStorage.getItem( 'ms_date' ) ) : {};
	const msGift = ( localStorage.getItem( 'ms_gift' ) ) ? JSON.parse( localStorage.getItem( 'ms_gift' ) ) : {};

	const [ shippingNote, setShippingNote ] = useState( data['note'] );
	const [ shippingDate, setShippingDate ] = useState( data['date'] );
	const [ shippingGift, setShippingGift ] = useState( data['gift'] );
	const [ errorDate, setErrorDate ]       = useState( '' );

	const updateCartData = () => {
		const savedNotes = ( localStorage.getItem( 'ms_note' ) ) ? JSON.parse( localStorage.getItem( 'ms_note' ) ) : {};
		const savedDates = ( localStorage.getItem( 'ms_date' ) ) ? JSON.parse( localStorage.getItem( 'ms_date' ) ) : {};
		const savedGifts = ( localStorage.getItem( 'ms_gift' ) ) ? JSON.parse( localStorage.getItem( 'ms_gift' ) ) : {};

		extensionCartUpdate(
			{
				namespace,
				data: {
					'notes': savedNotes,
					'dates': savedDates,
					'gifts': savedGifts
				}
			}

		);

		dispatch( CART_STORE_KEY ).updateCustomerData();
	};

	const onNoteChange = ( textNote ) => {
		if ( Number.isInteger( staticVar.note_limit ) && 0 !== staticVar.note_limit && textNote.length > staticVar.note_limit ) {
			return;
		}

		setShippingNote( textNote );
		msNote[ index ] = textNote;
		localStorage.setItem( 'ms_note', JSON.stringify( msNote ) );

		clearTimeout( delayTimer );

		delayTimer = setTimeout( updateCartData, 1000 );
	};

	const validateDateValue = ( textDate ) => {
		const validDates = staticVar.valid_dates.map( ( date ) => parseInt( date ) );
		const excludedDates = staticVar.excluded_dates;
		const dateObj = new Date( textDate );
		const day = dateObj.getDay();
		const date = dateObj.getDate();
		const month = ( '0' + ( dateObj.getMonth() + 1 ) ).slice( -2 );
		const year = dateObj.getFullYear();
		const newDateString = month + '-' + date + '-' + year;

		if ( validDates.length > 0 && ! validDates.includes( day ) ) {
    		return false;
		}

		if ( excludedDates.length > 0 && excludedDates.includes( newDateString ) ) {
			return false;
		}

		return true;
	};

	const onDateChange = ( textDate ) => {
		if ( ! validateDateValue( textDate ) ) {
			setErrorDate( staticVar.date_error_text + ' ' + textDate );
			return;
		}

		setErrorDate( '' );
		setShippingDate( textDate );
		msDate[ index ] = textDate;
		localStorage.setItem( 'ms_date', JSON.stringify( msDate ) );

		updateCartData();
	};

	const onGiftChange = ( checked ) => {
		const isGift = checked ? 'yes' : 'no';

		setShippingGift( isGift );
		msGift[ index ] = isGift;
		localStorage.setItem( 'ms_gift', JSON.stringify( msGift ) );

		updateCartData();
	};

	const maybeGetNoteFieldHTML = () => {

		if ( ! staticVar.show_notes ) {
			return '';
		}

		return (
			<div className="wcms-input-container wcms-input-note">
				<div className="wcms-shipping-note-label"><span>{ staticVar.note_label_text }</span></div>
				<Textarea
					className="wcms-shipping-note"
					name={ 'shipping_note[' + index + ']' }
					onTextChange={ ( value ) => onNoteChange( value ) }
					value={ shippingNote }
				/>
			</div>
		);
	}

	const maybeGetDateFieldHTML = () => {

		if ( ! staticVar.show_datepicker ) {
			return '';
		}

		const currentDate = new Date();
		const curDateString = currentDate.getFullYear() + '-' + ( '0' + ( currentDate.getMonth() + 1 ) ).slice( -2 ) + '-' + ( '0' + ( currentDate.getDate() ) ).slice( -2 );

		return (
			<div className="wcms-input-container wcms-input-date">
				<div className="wcms-shipping-date-label"><span>{ staticVar.date_label_text }</span></div>
				<TextInput
					type="date"
					className="wcms-shipping-date"
					min={ curDateString }
					onChange={ ( value ) => onDateChange( value ) }
					value={ shippingDate }
				/>
				<span className="error_date">{ errorDate }</span>
			</div>
		);
	}

	const maybeGetGiftFieldHTML = () => {

		if ( ! staticVar.show_gifts ) {
			return '';
		}

		const isChecked = ( 'yes' === shippingGift ) ? true : false;

		return (
			<div className="wcms-input-container wcms-input-gift">
				<CheckboxControl
					className="wcms-shipping-gift"
					label={ staticVar.gifts_text }
					onChange={ ( checked ) => onGiftChange( checked ) }
					checked={ isChecked }
				/>
			</div>
		);
	}

	const noteFieldHTML = maybeGetNoteFieldHTML();
	const dateFieldHTML = maybeGetDateFieldHTML();
	const giftFieldHTML = maybeGetGiftFieldHTML();

	if ( ! noteFieldHTML && ! dateFieldHTML && ! giftFieldHTML ) {
		return '';
	}

	return (
		<div className="wcms-shipping-note-container">
			{ noteFieldHTML }
			{ dateFieldHTML }
			{ giftFieldHTML }
		</div>
	);
}

const getAddressInfoHTML = ( { data, index, staticVar, namespace } ) => {
	let productListHTML = [];
	const shippingMethodHTML = getShippingMethodHTML( data['chosen_shipping_method'], staticVar.no_method_text );
	const shippingFieldsHTML = maybeGetShippingFieldsHTML( data, index, staticVar, namespace );

	productListHTML = productListHTML.concat( data.products.map( ( product, index ) => getProductLI( product, index ) ) );

	return (
		<div key={index} className="wcms-block-addresses">
			<div className="wcms-block-package-name">{ data.package_name }</div>
			<div className="wcms-block-address"><RawHTML>{ data.formatted_address }</RawHTML></div>
			<div className="wcms-block-products">
				<ul>
					{ productListHTML }
				</ul>
			</div>
			<div className="wcms-block-shipping-methods">
				{ shippingMethodHTML }
			</div>
			<div>
				{ shippingFieldsHTML }
			</div>
		</div>
	);
};

const onClickModifyButton = ( modify_addr_link ) => {
	location.href = modify_addr_link;
};

const getModifyResetButtonHTML = ( staticVar ) => {
	return (
		<div className="wcms-block-button">
			<Button
				className="wcms-block-button-modify"
				onClick={ ( e ) => onClickModifyButton( staticVar.modify_addr_link ) }
				label={ staticVar.modify_addr_text }
			>{ staticVar.modify_addr_text }</Button>
			<a className="wcms-block-button-reset" href={ staticVar.reset_url }>{ staticVar.reset_addr_text }</a>
		</div>
	);
};

/**
 * Multiple Addresses shipping component.
 *
 * @param extensions
 * @returns {JSX.Element}
 * @constructor
 */
const MultipleAddresses = ( { extensions } ) => {
	const namespace = 'wc_shipping_multiple_addresses';
	let has_errors  = false;

	// First check for the namespace and multi_shipping_info existence. Skip if we don't have any relevant data.
	if ( ! extensions[ namespace ] || ! extensions[ namespace ][ 'multi_shipping_info' ] || ! extensions[ namespace ][ 'multi_shipping_info' ]['data'] ) {
		return <div className="woocommerce-shipping-multiple-addresses-info"></div>;
	}

	const datas = extensions[ namespace ][ 'multi_shipping_info' ]['data'];
	const staticVar = extensions[ namespace ][ 'multi_shipping_info' ]['static_var'];

	if ( ! staticVar.is_eligible_wcms ) {
		return <div className="woocommerce-shipping-multiple-addresses-info"></div>;
	}

	const WCMSButtonHTML = maybeGetWCMSButtonHTML( staticVar );
	
	// If cart is eligible for WCMS and multiple shipping is not being set yet,
	// Then it will display WCMS button to set the multiple shipping.
	if ( WCMSButtonHTML ) {
		return ( 
			<div className="woocommerce-shipping-multiple-addresses-info">
				{ WCMSButtonHTML }
			</div>
		);
	}

	let multiShippingHTML = [];
	const modifyResetButtonHTML = getModifyResetButtonHTML( staticVar );

	multiShippingHTML = multiShippingHTML.concat(
		datas.map( ( data, index ) => {
				const addressInfo = {
					data,
					index,
					staticVar,
					namespace
				};

				return getAddressInfoHTML( addressInfo );
			}
		) 
	);

	// Use effect to trigger `updateCustomerData` dispatch in order to update the tax for the first time.
	useEffect( () => {
		setTimeout( function() {
			dispatch( CART_STORE_KEY ).updateCustomerData();
		}, 400 );
	}, [] );

	return ( 
		<div className="woocommerce-shipping-multiple-addresses-info">
			<div>{ multiShippingHTML }</div>
			<div>{ modifyResetButtonHTML }</div>
		</div>
	);
}

const render = () => {
	return (
		<ExperimentalOrderShippingPackages>
			<MultipleAddresses />
		</ExperimentalOrderShippingPackages>
	);
};

registerPlugin( 'wcms-block-multiple-addresses', {
	render,
	scope: 'woocommerce-checkout',
} );