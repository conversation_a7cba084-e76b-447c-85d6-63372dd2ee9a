.wcms-checkout-blocks.wcms-activated .wc-block-components-totals-item__description .wc-block-components-shipping-address {
	display:none;
}

.woocommerce-shipping-multiple-addresses-info {
	.wcms-block-addresses {
		margin-bottom:13px;
		border:1px solid hsla(0,0%,7%,.11);
		border-radius:4px;
		padding:12px;
	}

	.wcms-block-package-name {
		font-size:1.1em;
		font-weight:700;
		margin-bottom:8px;
	}

	.wcms-block-address {
		margin-bottom:7px;
		padding-bottom:5px;
		border-bottom:1px solid hsla(0,0%,7%,.11);
	}

	.wcms-block-products {
		margin-bottom:7px;
		padding-bottom:5px;
		border-bottom:1px solid hsla(0,0%,7%,.11);

		ul {
			list-style-type:disc;
			margin:0px 0px 0px 15px;
			padding:0px 0px 0px 15px;
		}
	}

	.wcms-block-shipping-methods {
		font-weight:700;
	}

	.wcms-input-container {
		padding-top:8px;
		margin-bottom:6px;
	}

	.wc-block-components-text-input.wcms-shipping-date {
		display:inline-block;
		margin-top:0px;
	}

	.wcms-block-button {
		display:flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width:100%;

		.wcms-block-button-modify {
			font-weight:700;
			padding-left:30px;
			padding-right:30px;
		}
	}

	.wcms-save-note-button {
		display:inline-block;
		margin-top:10px;
	}

	.wcms-set-button-container {
		border:1px solid hsla(0,0%,7%,.11);
		border-radius:4px;
		padding:12px;
	}

	.wcms-block-button-set {
		font-weight:700;
		padding-left:30px;
		padding-right:30px;
		width:100%;
	}

	.error_date {
		font-weight:700;
		color:#ff0000;
		display:inline-block;
		margin-left:8px;
	}
}