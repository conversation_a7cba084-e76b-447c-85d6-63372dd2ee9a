/**
 * Duplicate <PERSON>t <PERSON> Block
 *
 * This file is responsible for rendering duplicate cart button from the Multiple Addresses Shipping plugin.
 *
 * @package WC_Shipping_Multiple_Addresses
 */

const { registerPlugin }            = window.wp.plugins;
const { Button }                    = window.wc.blocksComponents;

import './style.scss';

/**
 * Duplicate cart button component.
 *
 * @param extensions
 * @returns {JSX.Element}
 * @constructor
 */
const DuplicateCartButton = ( { extensions } ) => {
	const namespace = 'wc_shipping_multiple_addresses';

	// First check for the namespace and duplicate_cart existence. And skip if there are no text and url for this type.
	if ( ! extensions[ namespace ] || ! extensions[ namespace ][ 'duplicate_cart' ] || ! extensions[ namespace ][ 'duplicate_cart' ]['text'] || ! extensions[ namespace ][ 'duplicate_cart' ]['url'] ) {
		return <></>;
	}

	const datas = extensions[ namespace ][ 'duplicate_cart' ];

	return ( 
		<div className="wcms-duplicate-cart">
			<Button
				className="wcms-duplicate-cart-button"
				onClick={ ( e ) => buttonOnClick( datas.url ) }
				label={ datas.text }
			>{ datas.text }</Button>
		</div>
	);
}

const buttonOnClick = ( data_url ) => {
	location.href = data_url;
};

const render = () => {
	return (
		<ExperimentalDiscountsMeta>
			<DuplicateCartButton />
		</ExperimentalDiscountsMeta>
	);
};

registerPlugin( 'wcms-duplicate-cart-button', {
	render,
	scope: 'woocommerce-checkout',
} );