This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: languages/, assets/, dist/
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
.github/
  ISSUE_TEMPLATE/
    config.yml
  workflows/
    build.yml
    cron_qit.yml
    manual_qit.yml
    merge_to_trunk.yml
    qit_runner.yml
    update-requires-headers.yml
  ISSUE_TEMPLATE.md
  PULL_REQUEST_TEMPLATE.md
client/
  block-multiple-addresses/
    index.js
    style.scss
  duplicate-cart-button/
    index.js
    style.scss
includes/
  compat/
    class-wc-pip-compat.php
  integrations/
    class-wc-ms-customer-order-csv-export.php
  class-blocks-integration.php
  class-store-api-extension.php
  class-wc-ms-address-book.php
  class-wc-ms-admin-user-addresses-list-table.php
  class-wc-ms-admin.php
  class-wc-ms-api.php
  class-wc-ms-cart.php
  class-wc-ms-checkout.php
  class-wc-ms-front.php
  class-wc-ms-gifts.php
  class-wc-ms-notes.php
  class-wc-ms-order-shipment.php
  class-wc-ms-order-type-order-shipment.php
  class-wc-ms-order.php
  class-wc-ms-packages.php
  class-wc-ms-post-types.php
  class-wc-ms-privacy.php
  class-wc-ms-shipping-easy.php
  class-wc-ms-shipworks.php
  class-wc-multiple-shipping-settings.php
  class-wc-ship-multiple.php
  functions.php
templates/
  account-address-form.php
  address-block.php
  address-form.php
  my-account-addresses.php
  pip-template-body.php
  shipping-address-table.php
.gitignore
.npmrc
.nvmrc
.phpcs.security.xml
babel.config.js
changelog.txt
class-wc-ms-compatibility.php
composer.json
package.json
README.md
webpack.config.js
woocommerce-shipping-multiple-address.php
woocommerce-shipping-multiple-addresses.php
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path=".github/ISSUE_TEMPLATE.md">
Before submitting a bug report be sure you have checked out [our support page](https://extendomattic.wordpress.com/extendables-support/) for information on labeling and other support channels.

<!-- You MUST add labels or this issue will be ignored. Please add a type (bug/enhancement/technical debt) and a priority (high/low). If these are not added, the issue will not be responded to or addressed. -->

**Describe the bug**
A clear and concise description of what the bug is. Please be as descriptive as possible.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Isolating the problem (mark completed items with an [x]):**
- [ ] I have deactivated other plugins and confirmed this bug occurs when only the extension is active.
- [ ] I can reproduce this bug consistently using the steps above.

**WordPress Environment**
<details>
```
Copy and paste the system status report from **WooCommerce > System Status** in WordPress admin.
```
</details>
</file>

<file path=".github/PULL_REQUEST_TEMPLATE.md">
### All Submissions:

* [ ] Does your code follow [WooCommerce](https://docs.woocommerce.com/document/create-a-plugin/) and [WordPress](https://make.wordpress.org/core/handbook/best-practices/coding-standards/) standards?
* [ ] Have you written new tests for your changes, as applicable?
* [ ] Have you successfully run tests with your changes locally?

<!-- Mark completed items with an [x] -->

<!-- You can erase any parts of this template not applicable to your Pull Request. -->

### Changes proposed in this Pull Request:

<!-- Describe the changes made to this Pull Request and the reason for such changes. -->

Closes # .

### How to test the changes in this Pull Request:

1.
2.
3.

### Other information:

* [ ] Have you checked to ensure there aren't other open [Pull Requests](../../pulls) for the same update/change?

<!-- Mark completed items with an [x] -->

### Changelog entry

> Enter a summary of all changes on this Pull Request. This will appear in the changelog if accepted.
</file>

<file path=".github/workflows/cron_qit.yml">
name: Cron QIT

on:
  schedule:
    # Run at 02:00 on Sundays.
    - cron: '0 2 * * 0'

jobs:

  build:
    name: Build project
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-shipping-multiple-addresses

  qit-tests:
    name: QIT
    needs: build
    uses: ./.github/workflows/qit_runner.yml
    secrets: inherit
    with:
      extension: ${{ needs.build.outputs.plugin_name }}
      artifact: ${{ needs.build.outputs.plugin_name }}
      wp-version: 'stable'
      wc-version: 'nightly'
      test-activation: true
      test-security: true
      test-phpcompatibility: true
      test-validation: true
      test-phpstan: false # TODO: Enable this once we've fixed the PHPStan issues.
      test-woo-api: true
      test-woo-e2e: true
      test-malware: true
      test-plugin-check: false # only run for WPORG

  handle-success:
    if: ${{ success() }}
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> passed.'

  handle-error:
    if: ${{ failure() }}
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: 'Scheduled workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> failed. You can find the results in the artifacts section.'
</file>

<file path=".github/workflows/manual_qit.yml">
name: Manual Test Runner

on:
  workflow_dispatch:
    inputs:
      wp-version:
        description: 'WordPress Version to be tested: `latest` or a specific version number.'
        type: string
        default: 'latest'
      wc-version:
        description: 'WooCommerce Version to be tested: `latest`, `nightly` or a specific version number.'
        type: string
        default: 'latest'
      php-version:
        description: |
          PHP version. Default is `8.4`.
        type: string
        default: '8.4'
      extension-tests:
        description: 'Extension Tests'
        type: choice
        default: 'All'
        options:
          #   - 'E2E Tests' // enable this once we have E2E tests
          - 'Unit Tests'
          - 'All'
          - 'None'
      qit-tests:
        description: 'QIT Tests'
        type: choice
        options:
          - 'WooCommerce Pre-Release Tests (includes Activation, WooCommerce E2E and API tests)'
          - 'Code Quality Checks (includes Security, Validation, Malware, PHPStan, and PHP Compatibility tests)'
          - 'Activation'
          - 'Security'
          - 'Validation'
          - 'Malware'
          - 'PHPStan'
          - 'PHP Compatibility'
          - 'WooCommerce E2E and API tests'
          - 'Plugin Checks (for dotOrg metadata)'
          - 'None'
      qit-wait:
        description: 'Wait for QIT? Requires additional time for the QIT tests to complete within GHA.'
        type: boolean
        default: true
      options:
        description: 'QIT Additional options for `qit` command, like `--optional_features=hpos`.'
        type: string
        default: ''

run-name: Tests with WP-${{ inputs.wp-version }} - WC-${{ inputs.wc-version }} - PHP ${{ inputs.php-version }} - Tests ${{ inputs.extension-tests }} - QIT ${{ inputs.qit-tests }}

jobs:
  build_project:
    name: Package
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-shipment-tracking

  qit-tests:
    name: QIT
    needs: build_project
    uses: ./.github/workflows/qit_runner.yml
    with:
      extension: ${{ needs.build_project.outputs.plugin_name }}
      artifact: ${{ needs.build_project.outputs.plugin_name }}
      wp-version: ${{ inputs.wp-version == 'latest' && 'stable' || inputs.wp-version }}
      wc-version: ${{ inputs.wc-version == 'latest' && 'stable' || contains( inputs.wc-version, 'rc' ) && 'rc' || inputs.wc-version }}
      php-version: ${{ inputs.php-version }}
      test-activation: ${{ contains(inputs.qit-tests, 'Activation') && true || false }}
      test-security: ${{ contains(inputs.qit-tests, 'Security') && true || false }}
      test-phpcompatibility: ${{ contains(inputs.qit-tests, 'Compatibility') && true || false }}
      test-phpstan: ${{ contains(inputs.qit-tests, 'PHPStan') && true || false }}
      test-woo-api: ${{ contains(inputs.qit-tests, 'API') && true || false }}
      test-woo-e2e: ${{ contains(inputs.qit-tests, 'WooCommerce E2E') && true || false }}
      test-malware: ${{ contains(inputs.qit-tests, 'Malware') && true || false }}
      test-validation: ${{ contains(inputs.qit-tests, 'Validation') && true || false }}
      test-plugin-check: ${{ contains(inputs.qit-tests, 'dotOrg') && true || false }}
      options: ${{ inputs.options }}
      wait: ${{ inputs.qit-wait }}
    secrets: inherit

  # e2e-tests: // enable this once we have E2E tests
  #     if: contains(inputs.extension-tests, 'All') || contains(inputs.extension-tests, 'E2E')
  #     name: E2E tests
  #     needs: build_project
  #     uses: ./.github/workflows/e2e_runner.yml
  #     with:
  #       wp_version: '[ "${{ inputs.wp-version }}" ]'
  #       wc_version: '[ "${{ inputs.wc-version }}" ]'
  #       php_version: '[ "${{ inputs.php-version }}" ]'
  #       test_mode: '[ "legacy", "blocks" ]'
  #     secrets: inherit

  handle-success:
    if: |
      always() &&
      (needs.qit-tests.result == 'success' || needs.qit-tests.result == 'skipped')
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> passed.'

  handle-cancelled:
    if: cancelled()
    needs: qit-tests
    name: Handle cancellation
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: cancelled
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> cancelled.'

  handle-error:
    if: failure()
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: 'Test Runner Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> failed.'
</file>

<file path=".github/workflows/merge_to_trunk.yml">
name: Merge to Trunk CI

on:
  pull_request:
    branches:
      - trunk
    types:
      - closed

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build:
    name: Build project
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-shipping-multiple-addresses

  qit-tests:
    if: github.event.pull_request.merged == true
    name: QIT tests
    needs: build
    uses: ./.github/workflows/qit_runner.yml
    secrets: inherit
    with:
      extension: ${{ needs.build.outputs.plugin_name }}
      artifact: ${{ needs.build.outputs.plugin_name }}
      test-activation: true
      test-security: true
      test-phpcompatibility: true
      test-validation: true
      test-phpstan: false # TODO: Enable this once the PHPStan tests are fixed

  handle-success:
    if: ${{ success() }}
    needs: qit-tests
    name: Handle success
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: success
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> passed."

  handle-cancelled:
    if: ${{ cancelled() }}
    needs: qit-tests
    name: Handle cancellation
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: cancelled
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> cancelled."

  handle-error:
    if: ${{ failure() }}
    needs: qit-tests
    name: Handle failure
    runs-on: ubuntu-latest
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    steps:
      - uses: act10ns/slack@v2
        with:
          status: failure
          message: "Workflow <{{workflowUrl}}|{{workflow}}> with run ID <{{workflowRunUrl}}|{{runId}}> in PR <{{refUrl}}|{{ref}}> failed."
</file>

<file path=".github/workflows/qit_runner.yml">
name: Run QIT

on:
    workflow_call:
        inputs:
            extension:
                required: true
                type: string
            artifact:
                type: string
                required: true
            wp-version:
                type: string
                default: 'stable'
            wc-version:
                type: string
                default: 'stable'
            php-version:
                type: string
                default: '8.4'
            # Customize which QIT test types to run.
            test-activation:
                type: string
                default: 'false'
            test-security:
                type: string
                default: 'false'
            test-phpcompatibility:
                type: string
                default: 'false'
            test-phpstan:
                type: string
                default: 'false'
            test-woo-api:
                type: string
                default: 'false'
            test-woo-e2e:
                type: string
                default: 'false'
            test-e2e:
                type: string
                default: 'false'
            test-additional-plugins:
                type: string
                default: ''
            test-malware:
                type: string
                default: 'false'
            test-plugin-check:
                type: string
                default: 'false'
            test-validation:
                type: string
                default: 'false'
            options:
                type: string
                default: ''
            # End of QIT tests.
            wait:
                type: boolean
                default: true

env:
    NO_COLOR: 1
    QIT_DISABLE_ONBOARDING: yes
    EXTENSION_SLUG: ${{ inputs.extension }}
    WP_VERSION: ${{ inputs.wp-version && inputs.wp-version || 'stable' }}
    WC_VERSION: ${{ inputs.wc-version && inputs.wc-version || 'stable' }}
    PHP_VERSION: ${{ inputs.php-version && inputs.php-version || '8.4' }}
    ADDITIONAL_PLUGINS: ${{ inputs.test-additional-plugins && format( '--additional_plugins={0}', inputs.test-additional-plugins ) || '' }}
    WAIT_FLAG: ${{ inputs.wait && '--wait' || '' }}
    EXTRA_OPTIONS: ${{ inputs.options }}

jobs:
    qit-tests:
        name: Run QIT Tests
        runs-on: ubuntu-latest
        steps:
            - name: Install QIT via composer
              shell: bash
              run: composer require woocommerce/qit-cli

            - name: Add Partner and Prepare Environment
              shell: bash
              run: |

                  # Add Partner.
                  ./vendor/bin/qit partner:add \
                    --user='${{ secrets.PARTNER_USER }}' \
                    --qit_token='${{ secrets.PARTNER_SECRET }}'

                  # Export ZIP file path to GITHUB_ENV.
                  echo "ZIP_FILE_PATH=${{ github.workspace }}/$EXTENSION_SLUG.zip" >> $GITHUB_ENV

                  # Create a directory to store QIT Results.
                  mkdir ${{ github.workspace }}/qit-results

            - name: Download the zip
              uses: actions/download-artifact@v4
              with:
                  name: ${{ inputs.artifact }}
                  path: ${{ github.workspace }}

            ##############################
            # Activation Tests.
            ##############################

            - name: Activation test
              id: run-activation-test
              if: ${{ inputs.test-activation == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  echo "Running activation test for ${{ env.EXTENSION_SLUG }}"
                  ./vendor/bin/qit run:activation \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wp=${{ env.WP_VERSION }} \
                    --woo=${{ env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-activation-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo activation results
              if: ${{ inputs.test-activation == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-activation-results.txt
                  exit ${{ steps.run-activation-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Security Tests.
            ##############################

            - name: Run security test
              id: run-security-test
              if: ${{ inputs.test-security == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:security \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-security-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo security results
              if: ${{ inputs.test-security == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-security-results.txt
                  exit ${{ steps.run-security-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # PHP Compatibility Tests.
            ##############################

            - name: Run PHP compatibility test
              id: run-php-compatibility-test
              if: ${{ inputs.test-phpcompatibility == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:phpcompatibility \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-php-compatibility-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo PHP compatibility results
              if: ${{ inputs.test-phpcompatibility == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-php-compatibility-results.txt
                  exit ${{ steps.run-php-compatibility-test.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # PHPStan Tests.
            ##############################
            - name: Run PHPStan Tests
              id: run-phpstan-tests
              if: ${{ inputs.test-phpstan == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:phpstan \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }}  \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }}  \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-phpstan-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo PHPStan results
              if: ${{ inputs.test-phpstan == 'true' }}
              run: |

                  cat ${{ github.workspace }}/qit-results/qit-phpstan-results.txt
                  exit ${{ steps.run-phpstan-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # WC API Tests.
            ##############################
            - name: Run WC API Tests
              id: run-woo-api-tests
              if: ${{ inputs.test-woo-api == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:woo-api \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }} \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-woo-api-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo WC API results
              if: ${{ inputs.test-woo-api == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-woo-api-results.txt
                  exit ${{ steps.run-woo-api-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Malware Tests.
            ##############################
            - name: Run Malware Tests
              id: run-malware-tests
              if: ${{ inputs.test-malware == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:malware \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-malware-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo Malware results
              if: ${{ inputs.test-malware == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-malware-results.txt
                  exit ${{ steps.run-malware-tests.outputs.exitcode == 1 && 1 || 0 }}

            ##############################
            # Validation Tests.
            ##############################
            - name: Run Validation Tests
              id: run-validation-tests
              if: ${{ inputs.test-validation == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:validation \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-validation-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Upload results
              if: always()
              uses: actions/upload-artifact@v4
              with:
                  name: qit-results
                  path: ${{ github.workspace }}/qit-results/

            ##############################
            # Plugin Check Tests (for dotOrg).
            ##############################
            - name: Run Plugin Check Tests
              id: run-plugin-check-tests
              if: ${{ inputs.test-plugin-check == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:plugin-check \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-plugin-check-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo Plugin Check results
              if: ${{ inputs.test-plugin-check == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-plugin-check-results.txt
                  exit ${{ steps.run-plugin-check-tests.outputs.exitcode == 1 && 1 || 0 }}
                                    
            ##############################
            # WC E2E Tests.
            ##############################
            - name: Run WC E2E Tests
              id: run-woo-e2e-tests
              if: ${{ inputs.test-woo-e2e == 'true' }}
              # Do not fail when the `qit` fails, so we can annotate the results.
              shell: bash --noprofile --norc {0}
              run: |
                  ./vendor/bin/qit run:woo-e2e \
                    ${{ env.EXTENSION_SLUG }} \
                    --zip=${{ env.ZIP_FILE_PATH }} \
                    --wordpress_version=${{ env.WP_VERSION == 'nightly' && 'rc' || env.WP_VERSION }} \
                    --woocommerce_version=${{ env.WC_VERSION == 'nightly' && 'rc' || env.WC_VERSION }} \
                    --php_version=${{ env.PHP_VERSION }} \
                    ${{ env.ADDITIONAL_PLUGINS }} \
                    ${{ env.WAIT_FLAG }} \
                    ${{ env.EXTRA_OPTIONS }} \
                    -n \
                    > ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt
                  echo "exitcode=${PIPESTATUS[0]}" >> "$GITHUB_OUTPUT"

            - name: Echo WC E2E results
              if: ${{ inputs.test-woo-e2e == 'true' }}
              run: |
                  cat ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt
                  exit ${{ steps.run-woo-e2e-tests.outputs.exitcode == 1 && 1 || 0 }}
</file>

<file path="client/block-multiple-addresses/style.scss">
.wcms-checkout-blocks.wcms-activated .wc-block-components-totals-item__description .wc-block-components-shipping-address {
	display:none;
}

.woocommerce-shipping-multiple-addresses-info {
	.wcms-block-addresses {
		margin-bottom:13px;
		border:1px solid hsla(0,0%,7%,.11);
		border-radius:4px;
		padding:12px;
	}

	.wcms-block-package-name {
		font-size:1.1em;
		font-weight:700;
		margin-bottom:8px;
	}

	.wcms-block-address {
		margin-bottom:7px;
		padding-bottom:5px;
		border-bottom:1px solid hsla(0,0%,7%,.11);
	}

	.wcms-block-products {
		margin-bottom:7px;
		padding-bottom:5px;
		border-bottom:1px solid hsla(0,0%,7%,.11);

		ul {
			list-style-type:disc;
			margin:0px 0px 0px 15px;
			padding:0px 0px 0px 15px;
		}
	}

	.wcms-block-shipping-methods {
		font-weight:700;
	}

	.wcms-input-container {
		padding-top:8px;
		margin-bottom:6px;
	}

	.wc-block-components-text-input.wcms-shipping-date {
		display:inline-block;
		margin-top:0px;
	}

	.wcms-block-button {
		display:flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width:100%;

		.wcms-block-button-modify {
			font-weight:700;
			padding-left:30px;
			padding-right:30px;
		}
	}

	.wcms-save-note-button {
		display:inline-block;
		margin-top:10px;
	}

	.wcms-set-button-container {
		border:1px solid hsla(0,0%,7%,.11);
		border-radius:4px;
		padding:12px;
	}

	.wcms-block-button-set {
		font-weight:700;
		padding-left:30px;
		padding-right:30px;
		width:100%;
	}

	.error_date {
		font-weight:700;
		color:#ff0000;
		display:inline-block;
		margin-left:8px;
	}
}
</file>

<file path="client/duplicate-cart-button/style.scss">
.wcms-duplicate-cart {
	display:flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	width:100%;

	&-button {
		font-weight:700;
		padding-left:30px;
		padding-right:30px;
		width:100%;
	}
}
</file>

<file path="includes/compat/class-wc-pip-compat.php">
<?php
/**
 * Compatibility for PIP class file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Compatibility class for PIP.
 */
class WC_Pip_Compat {
	/**
	 * Constructor
	 *
	 * @since 3.6.13
	 */
	public function __construct() {
		add_action( 'wc_pip_after_body', array( $this, 'add_shipping_addresses_to_pip' ), 10, 4 );
		add_filter( 'wc_pip_document_show_shipping_address', array( $this, 'maybe_show_top_shipping_from_pip' ), 10, 3 );
	}

	/**
	 * Add multiple addresses at the bottom of the order invoice.
	 *
	 * @since 3.6.13
	 * @param string           $type document type.
	 * @param string           $action current action running on Document.
	 * @param \WC_PIP_Document $document document object.
	 * @param \WC_Order        $order order object.
	 * @return void
	 */
	public function add_shipping_addresses_to_pip( $type, $action, $document, $order ) {
		global $wcms;

		if ( 'invoice' === $type ) {
			$wcms->order->display_order_shipping_addresses( $order );
		}
	}
	/**
	 * Hide Shipping Address heading when there are multiple addresses.
	 *
	 * @since 3.6.13
	 * @param bool      $show_shipping_address show shipping address.
	 * @param string    $type current action running on Document.
	 * @param \WC_Order $order order object.
	 * @return bool
	 */
	public function maybe_show_top_shipping_from_pip( $show_shipping_address, $type, $order ) {

		if ( 'invoice' === $type && $this->has_multiple_shipping_and_multiple_packages( $order ) ) {
			return false;
		}
		return true;
	}
	/**
	 * True if the order contains multiple addresses and packages.
	 *
	 * @since 3.6.13
	 * @param \WC_Order $order order object.
	 * @return bool
	 */
	private function has_multiple_shipping_and_multiple_packages( $order ) {
		if ( ! $order instanceof WC_Order ) {
			$order = wc_get_order( $order );
		}

		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return false;
		}

		$multiple_shipping = $order->get_meta( '_multiple_shipping' );
		$packages          = $order->get_meta( '_wcms_packages' );

		if ( 'yes' === $multiple_shipping && $packages && is_array( $packages ) && 1 < count( $packages ) ) {
			return true;
		}
		return false;
	}
}
</file>

<file path="includes/class-wc-ms-api.php">
<?php
/**
 * Class WC_MS_API file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_API.
 * To be able adding a shipping packages using rest API.
 */
class WC_MS_API {

	/**
	 * Class constructor.
	 */
	public function __construct() {
		// Initialize API filters. Need to be called after plugins_loaded because of WC_VERSION check.
		add_action( 'plugins_loaded', array( $this, 'init_api_filters' ), 11 );
	}

	/**
	 * API filters
	 *
	 * @since 3.3.23
	 * @return void
	 */
	public function init_api_filters() {
		add_filter( 'woocommerce_rest_prepare_shop_order_object', array( $this, 'add_shipping_packages' ), 10, 2 );
	}

	/**
	 * Add shipping packages to data.
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @since 3.3.23
	 *
	 * @return array
	 */
	private function calculate_shipping_packages( $order ) {
		$packages  = $order->get_meta( '_wcms_packages' );
		$multiship = is_array( $packages ) && 1 < count( $packages );

		$retval = array(
			'multiple_shipping' => $multiship,
		);

		if ( ! $multiship ) {
			return $retval;
		}

		$shipping_packages = array();

		foreach ( $packages as $i => $package ) {
			$package['contents'] = array_values( $package['contents'] );
			foreach ( $package['contents'] as $item_key => $item ) {
				$package['contents'][ $item_key ]['name'] = $item['data']->get_title();

				unset( $package['contents'][ $item_key ]['data'], $package['full_address'] );

				$shipping_packages[] = $package;
			}
		}

		$retval['shipping_packages'] = $shipping_packages;

		return $retval;
	}

	/**
	 * Add shipping packages to the order response array
	 *
	 * @param WP_REST_Response $response   The response object.
	 * @param WC_Data          $obj        Order object.
	 *
	 * @return WP_REST_Response $data
	 */
	public function add_shipping_packages( $response, $obj ) {
		$order             = wc_get_order( $obj->get_id() );
		$order_data        = $response->get_data();
		$shipping_packages = $this->calculate_shipping_packages( $order );

		$response->set_data( array_merge( $order_data, $shipping_packages ) );

		return $response;
	}
}
</file>

<file path="includes/functions.php">
<?php
/**
 * Functions Collection.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Get Cheapest shipping rate.
 *
 * @param array $rates Shipping rates.
 *
 * @return array Cheapest shipping rate.
 */
function wcms_get_cheapest_shipping_rate( $rates = array() ) {
	$cheapest  = false;
	$last_cost = false;

	foreach ( $rates as $rate ) {
		if ( false === $last_cost ) {
			$last_cost = $rate->cost;
			$cheapest  = $rate;
			continue;
		}

		if ( $rate->cost < $last_cost ) {
			$last_cost = $rate->cost;
			$cheapest  = $rate;
		}
	}

	if ( $cheapest ) {
		$cheapest = (array) $cheapest;
	}

	return $cheapest;
}

/**
 * Get address.
 *
 * @param array $address Destination address.
 *
 * @return array Shipping address.
 */
function wcms_get_address( $address ) {
	foreach ( $address as $key => $value ) {
		if ( strpos( $key, 'shipping_' ) === false ) {
			$address[ 'shipping_' . $key ] = $value;
		}

		$addr_key             = str_replace( 'shipping_', '', $key );
		$address[ $addr_key ] = $value;
	}

	return $address;
}

/**
 * Get formatted address.
 *
 * @param array $address Shipping Address.
 *
 * @return string Formatted address.
 */
function wcms_get_formatted_address( $address ) {
	$address = wcms_get_address( $address );

	/**
	 * Allow modifying the formatted address.
	 *
	 * @param string $formatted_address Formatted address.
	 * @param array  $address           Shipping address.
	 * @since 3.3
	 */
	return apply_filters( 'wc_ms_formatted_address', WC()->countries->get_formatted_address( $address ), $address );
}

/**
 * Get real cart items count.
 *
 * @return int Cart items count.
 */
function wcms_count_real_cart_items() {

	$count = 0;

	foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {

		if ( ! $cart_item['data']->needs_shipping() ) {
			continue;
		}

		if ( isset( $cart_item['bundled_by'] ) && ! empty( $cart_item['bundled_by'] ) ) {
			continue;
		}

		if ( isset( $cart_item['composite_parent'] ) && ! empty( $cart_item['composite_parent'] ) ) {
			continue;
		}

		++$count;
	}

	return $count;
}

/**
 * Get real cart items.
 *
 * @return array Cart items.
 */
function wcms_get_real_cart_items() {

	$items = array();

	foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {

		if ( ! $cart_item['data']->needs_shipping() ) {
			continue;
		}

		if ( isset( $cart_item['bundled_by'] ) && ! empty( $cart_item['bundled_by'] ) ) {
			continue;
		}

		if ( isset( $cart_item['composite_parent'] ) && ! empty( $cart_item['composite_parent'] ) ) {
			continue;
		}

		$items[ $cart_item_key ] = $cart_item;
	}

	return $items;
}

/**
 * Get Session.
 *
 * @param string $name Session name.
 *
 * @return mixed Session.
 */
function wcms_session_get( $name ) {
	if ( isset( WC()->session->$name ) ) {
		return WC()->session->$name;
	}

	return null;
}

/**
 * Check if session isset.
 *
 * @param string $name Session name.
 *
 * @return bool True if isset.
 */
function wcms_session_isset( $name ) {
	return ( isset( WC()->session->$name ) );
}

/**
 * Set session.
 *
 * @param string $name Session name.
 * @param string $value Session value.
 *
 * @return void
 */
function wcms_session_set( $name, $value ) {
	unset( WC()->session->$name );
	WC()->session->$name = $value;
}

/**
 * Delete a session.
 *
 * @param string $name Session name.
 *
 * @return void
 */
function wcms_session_delete( $name ) {
	unset( WC()->session->$name );
}
</file>

<file path=".gitignore">
/nbproject/private/
node_modules
project.xml
project.properties
.DS_Store
Thumbs.db
.buildpath
.project
.settings*
.vscode
sftp-config.json
/deploy/
/wc-apidocs/
/languages/
screenshots/
/assets/css/*.css
/assets/js/*.min.js

# Ignore all log files except for .htaccess
/logs/*
!/logs/.htaccess

tests/e2e/config/local-*
.eslintcache

/vendor/

dist
.idea

woocommerce-shipping-multiple-addresses.zip
</file>

<file path=".npmrc">
enable-pre-post-scripts=true
</file>

<file path=".phpcs.security.xml">
<?xml version="1.0"?>
<ruleset name="Security sniffs from WordPress Coding Standards">
  <description>Security sniffs from WordPress Coding Standards</description>

  <arg value="sp"/>
  <arg name="colors"/>
  <arg name="extensions" value="php"/>
  <arg name="parallel" value="8"/>

  <config name="testVersion" value="7.2-"/>

  <!-- Do not fail PHPCS CI over warnings -->
  <config name="ignore_warnings_on_exit" value="1"/>

  <rule ref="WordPress.Security.EscapeOutput"/>
  <rule ref="WordPress.Security.ValidatedSanitizedInput.InputNotSanitized"/>
  <rule ref="WordPress.Security.EscapeOutput">
    <properties>
      <property name="customEscapingFunctions" type="array" value="wc_help_tip,wc_sanitize_tooltip,wc_selected,wc_kses_notice,wc_esc_json,wc_query_string_form_fields,wc_make_phone_clickable" />
    </properties>
  </rule>
  <rule ref="WordPress.Security.ValidatedSanitizedInput">
    <properties>
      <property name="customSanitizingFunctions" type="array" value="wc_clean,wc_sanitize_tooltip,wc_format_decimal,wc_stock_amount,wc_sanitize_permalink,wc_sanitize_textarea,sanitize_url" />
    </properties>
  </rule>
  <!-- Encourage use of wp_safe_redirect() to avoid open redirect vulnerabilities.
     https://github.com/WordPress/WordPress-Coding-Standards/pull/1264 -->
  <rule ref="WordPress.Security.SafeRedirect"/>

  <!-- Verify that a nonce check is done before using values in superglobals.
     https://github.com/WordPress/WordPress-Coding-Standards/issues/73 -->
  <rule ref="WordPress.Security.NonceVerification"/>

  <!-- https://github.com/WordPress/WordPress-Coding-Standards/issues/1157 -->
  <rule ref="WordPress.Security.PluginMenuSlug"/>

  <!-- Covers rule: The eval() construct is very dangerous, and is impossible to secure. ... these must not be used. -->
  <rule ref="Squiz.PHP.Eval.Discouraged">
    <type>error</type>
    <message>eval() is a security risk so not allowed.</message>
  </rule>

</ruleset>
</file>

<file path="babel.config.js">
module.exports = {
	ignore: [],
	presets: [ '@wordpress/babel-preset-default' ],
	plugins: [],
};
</file>

<file path="class-wc-ms-compatibility.php">
<?php
/**
 * WooCommerce Plugin Compatibility
 *
 * This source file is subject to the GNU General Public License v3.0
 * that is bundled with this package in the file license.txt.
 * It is also available through the world-wide-web at this URL:
 * http://www.gnu.org/licenses/gpl-3.0.html
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade the plugin to newer
 * versions in the future. If you wish to customize the plugin for your
 * needs please refer to http://www.skyverge.com
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\Internal\DataStores\Orders\CustomOrdersTableController;

if ( ! class_exists( 'WC_MS_Compatibility' ) ) :

	/**
	 * WooCommerce Compatibility Utility Class
	 *
	 * The unfortunate purpose of this class is to provide a single point of
	 * compatibility functions for dealing with supporting multiple versions
	 * of WooCommerce.
	 *
	 * The recommended procedure is to rename this file/class, replacing "my plugin"
	 * with the particular plugin name, so as to avoid clashes between plugins.
	 * Over time we expect to remove methods from this class, using the current
	 * ones directly, as support for older versions of WooCommerce is dropped.
	 *
	 * Current Compatibility: 2.1.x - 2.2
	 *
	 * @version 2.0
	 */
	class WC_MS_Compatibility {

		/**
		 * Gets and formats a list of cart item data + variations for display on the frontend.
		 *
		 * @param array $cart_item Cart item object.
		 * @param bool  $flat Should the data be returned flat or in a list.
		 * @return string
		 */
		public static function get_item_data( $cart_item, $flat = false ) {
			if ( self::is_wc_version_gte( '3.3' ) ) {
				return wc_get_formatted_cart_item_data( $cart_item, $flat );
			}

			return WC()->cart->get_item_data( $cart_item );
		}

		/**
		 * Transparently backport the `post_status` WP Query arg used by WC 2.2
		 * for order statuses to the `shop_order_status` taxonomy query arg used by
		 * WC 2.1
		 *
		 * @since 2.0.0
		 * @param array $args WP_Query args.
		 * @return array
		 */
		public static function backport_order_status_query_args( $args ) {

			if ( ! self::is_wc_version_gte_2_2() ) {

				// Convert post status arg to taxonomy query compatible with WC 2.1.
				if ( ! empty( $args['post_status'] ) ) {

					$order_statuses = array();

					foreach ( (array) $args['post_status'] as $order_status ) {

						$order_statuses[] = str_replace( 'wc-', '', $order_status );
					}

					$args['post_status'] = 'publish';

					$tax_query = array(
						'taxonomy' => 'shop_order_status',
						'field'    => 'slug',
						'terms'    => $order_statuses,
						'operator' => 'IN',
					);

					$args['tax_query'] = array_merge( isset( $args['tax_query'] ) ? $args['tax_query'] : array(), $tax_query ); // phpcs:ignore
				}
			}

			return $args;
		}


		/**
		 * Get the user ID for an order
		 *
		 * @since 2.0.0
		 * @param \WC_Order $order Order object.
		 * @return int
		 */
		public static function get_order_user_id( WC_Order $order ) {

			if ( self::is_wc_version_gte_2_2() ) {

				return $order->get_user_id();

			} else {

				return $order->customer_user ? $order->customer_user : 0;
			}
		}


		/**
		 * Get the user for an order
		 *
		 * @since 2.0.0
		 * @param \WC_Order $order Order object.
		 * @return bool|WP_User
		 */
		public static function get_order_user( WC_Order $order ) {

			if ( self::is_wc_version_gte_2_2() ) {

				return $order->get_user();

			} else {

				return self::get_order_user_id( $order ) ? get_user_by( 'id', self::get_order_user_id( $order ) ) : false;
			}
		}

		/**
		 * The the Order's status.
		 *
		 * @param WC_Order $order Order object.
		 * @return string
		 */
		public static function get_order_status( WC_Order $order ) {
			if ( self::is_wc_version_gte_2_2() ) {
				return $order->get_status();
			} else {
				return $order->status;
			}
		}

		/**
		 * Backports the get_formatted() method to WC 2.1
		 *
		 * @since 2.0.0
		 * @see WC_Order_Item_Meta::get_formatted()
		 * @param \WC_Order_Item_Meta $item_meta order item meta class instance.
		 * @param string              $hide_prefix exclude meta when key is prefixed with this, defaults to `_`.
		 * @return array
		 */
		public static function get_formatted_item_meta( WC_Order_Item_Meta $item_meta, $hide_prefix = '_' ) {

			if ( self::is_wc_version_gte_2_2() ) {

				return $item_meta->get_formatted( $hide_prefix );

			} else {

				if ( empty( $item_meta->meta ) ) {
					return array();
				}

				$formatted_meta = array();

				foreach ( (array) $item_meta->meta as $meta_key => $meta_values ) {

					if ( empty( $meta_values ) || ! is_array( $meta_values ) || ( ! empty( $hide_prefix ) && substr( $meta_key, 0, 1 ) === $hide_prefix ) ) {
						continue;
					}

					foreach ( $meta_values as $meta_value ) {

						// Skip serialised meta.
						if ( is_serialized( $meta_value ) ) {
							continue;
						}

						$attribute_key = urldecode( str_replace( 'attribute_', '', $meta_key ) );

						// If this is a term slug, get the term's nice name.
						if ( taxonomy_exists( $attribute_key ) ) {
							$term = get_term_by( 'slug', $meta_value, $attribute_key );

							if ( ! is_wp_error( $term ) && is_object( $term ) && $term->name ) {
								$meta_value = $term->name;
							}

							// If we have a product, and its not a term, try to find its non-sanitized name.
						} elseif ( $item_meta->product ) {
							$product_attributes = $item_meta->product->get_attributes();

							if ( isset( $product_attributes[ $attribute_key ] ) ) {
								$meta_key = wc_attribute_label( $product_attributes[ $attribute_key ]['name'] );
							}
						}

						$formatted_meta[ $meta_key ] = array(
							'label' => wc_attribute_label( $attribute_key ),
							/**
							 * Filter for manipulating display meta value.
							 *
							 * @param mixed $meta_value Meta value.
							 *
							 * @since 2.0.0
							 */
							'value' => apply_filters( 'woocommerce_order_item_display_meta_value', $meta_value ),
						);
					}
				}

				// phpcs:disable --- This is an example of the format.
				/**
				 * Return an array of formatted item meta in format:
				 *
				 * array(
				 *   $meta_key => array(
				 *     'label' => $label,
				 *     'value' => $value
				 *   )
				 * )
				 *
				 * e.g.
				 *
				 * array(
				 *   'pa_size' => array(
				 *     'label' => 'Size',
				 *     'value' => 'Medium',
				 *   )
				 * )
				 */
				// phpcs:enable
				return $formatted_meta;
			}
		}


		/**
		 * Get the full path to the log file for a given $handle
		 *
		 * @since 2.0.0
		 * @param string $handle Log handle.
		 *
		 * @return string
		 */
		public static function wc_get_log_file_path( $handle ) {

			if ( self::is_wc_version_gte_2_2() ) {

				return wc_get_log_file_path( $handle );

			} else {

				return sprintf( '%s/plugins/woocommerce/logs/%s-%s.txt', WP_CONTENT_DIR, $handle, sanitize_file_name( wp_hash( $handle ) ) );
			}
		}

		/**
		 * Get the tax rates
		 *
		 * @param string $tax_class Tax class.
		 *
		 * @return array
		 */
		public static function get_tax_rates( $tax_class = '' ) {
			if ( self::is_wc_version_gte_2_2() ) {
				return WC_Tax::get_rates( $tax_class );
			} else {
				return WC()->cart->tax->get_rates( $tax_class );
			}
		}

		/**
		 * Calculate the tax of the given $price
		 *
		 * @param float $price Price value.
		 * @param array $rate Rate in percentage.
		 * @param bool  $price_includes_tax If price include tax or not.
		 * @param bool  $suppress_rounding If need to suppress rounding.
		 * @return array
		 */
		public static function calc_tax( $price, $rate, $price_includes_tax = false, $suppress_rounding = false ) {
			if ( self::is_wc_version_gte_2_2() ) {
				return WC_Tax::calc_tax( $price, $rate, $price_includes_tax, $suppress_rounding );
			} else {
				return WC()->cart->tax->calc_tax( $price, $rate, $price_includes_tax, $suppress_rounding );
			}
		}

		/**
		 * Calculate the total tax amount
		 *
		 * @param array $taxes List of taxes.
		 *
		 * @return float
		 */
		public static function get_tax_total( $taxes ) {
			if ( self::is_wc_version_gte_2_2() ) {
				return WC_Tax::get_tax_total( $taxes );
			} else {
				return WC()->cart->tax->get_tax_total( $taxes );
			}
		}


		/**
		 * Helper method to get the version of the currently installed WooCommerce
		 *
		 * @since 1.0.0
		 * @return string woocommerce version number or null
		 */
		private static function get_wc_version() {

			return defined( 'WC_VERSION' ) && WC_VERSION ? WC_VERSION : null;
		}


		/**
		 * Returns true if the installed version of WooCommerce is 2.2 or greater
		 *
		 * @since 2.0.0
		 * @return boolean true if the installed version of WooCommerce is 2.2 or greater
		 */
		public static function is_wc_version_gte_2_2() {
			return self::get_wc_version() && version_compare( self::get_wc_version(), '2.2', '>=' );
		}


		/**
		 * Returns true if the installed version of WooCommerce is greater than $version
		 *
		 * @since 1.0.0
		 * @param string $version the version to compare.
		 *
		 * @return boolean true if the installed version of WooCommerce is > $version
		 */
		public static function is_wc_version_gt( $version ) {
			return self::get_wc_version() && version_compare( self::get_wc_version(), $version, '>' );
		}

		/**
		 * Returns true if the installed version of WooCommerce is greater than equal $version
		 *
		 * @since 1.0.0
		 * @param string $version the version to compare.
		 *
		 * @return boolean true if the installed version of WooCommerce is > $version
		 */
		public static function is_wc_version_gte( $version ) {
			return self::get_wc_version() && version_compare( self::get_wc_version(), $version, '>=' );
		}

		/**
		 * Get order property with compatibility check on order getter introduced
		 * in WC 3.0.
		 *
		 * @since 3.3.23
		 *
		 * @param WC_Order $order Order object.
		 * @param string   $prop  Property name.
		 *
		 * @return mixed Property value
		 */
		public static function get_order_prop( $order, $prop ) {
			$modifier = function ( $a ) {
				return $a;
			};

			switch ( $prop ) {
				case 'order_date':
					$getter   = array( $order, 'get_date_created' );
					$modifier = function ( $date ) {
						return $date ? $date->date( 'Y-m-d H:i:s' ) : '';
					};
					break;
				case 'customer_user':
					$getter = array( $order, 'get_customer_id' );
					break;
				default:
					$getter = array( $order, 'get_' . $prop );
					break;
			}

			return is_callable( $getter ) ? $modifier( call_user_func( $getter ) ) : $order->{ $prop };
		}

		/**
		 * Get screen type for order metabox.
		 *
		 * @return string
		 */
		public static function get_meta_box_screen() {
			return wc_get_container()->get( CustomOrdersTableController::class )->custom_orders_table_usage_is_enabled()
				? wc_get_page_screen_id( 'shop-order' )
				: 'shop_order';
		}
	}


endif; // Class exists check.
</file>

<file path="webpack.config.js">
/**
 * The Webpack configuration for WooCommerce Shipping Multiple Addresses.
 *
 * @package WC_Shipping_Multiple_Addresses
 */

const path                 = require( 'path' );
const defaultConfig        = require( '@wordpress/scripts/config/webpack.config' );
const MiniCssExtractPlugin = require( 'mini-css-extract-plugin' );

const config = {
	...defaultConfig,
	entry: {
		'wcms-block-multiple-addresses':
			path.resolve(
				process.cwd(),
				'client',
				'block-multiple-addresses',
				'index.js'
			),
		'wcms-duplicate-cart-button':
			path.resolve(
				process.cwd(),
				'client',
				'duplicate-cart-button',
				'index.js'
			),
	},
	output: {
		path: path.resolve( __dirname, 'dist' ),
		filename: '[name].js',
	},
	module: {
		rules: [
			{
				test: /\.(j|t)sx?$/,
				exclude: [ /node_modules/ ],
				loader: 'babel-loader',
			},
			{
				test: /\.css$/i,
				use: [
					'style-loader',
					'css-loader'
				],
			},
			{
				test: /\.scss$/i,
				use: [
					MiniCssExtractPlugin.loader,
					'css-loader',
					'sass-loader',
				],
			}
		],
	},
	plugins: [
		new MiniCssExtractPlugin( {
			filename: `[name].css`,
		} ),
	],
};

module.exports = ( env ) => {
	if ( env.mode == 'production' ) {
		config.mode    = 'production';
		config.devtool = false;
	} else {
		config.mode    = 'development';
		config.devtool = 'inline-source-map';
	}
	return config;
};
</file>

<file path="woocommerce-shipping-multiple-address.php">
<?php
/**
 * Backwards compat.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$active_plugins = get_option( 'active_plugins', array() );
foreach ( $active_plugins as $key => $active_plugin ) {
	if ( strstr( $active_plugin, '/woocommerce-shipping-multiple-address.php' ) ) {
		$active_plugins[ $key ] = str_replace( '/woocommerce-shipping-multiple-address.php', '/woocommerce-shipping-multiple-addresses.php', $active_plugin );
	}
}
update_option( 'active_plugins', $active_plugins );
</file>

<file path=".github/ISSUE_TEMPLATE/config.yml">
blank_issues_enabled: false
contact_links:
  - name: We have moved to Linear!
    url: http://linear.app/team/WOOMLAD/new
    about: Please open new issues on Linear.
</file>

<file path="client/block-multiple-addresses/index.js">
/**
 * Multiple Addresses Shipping Options Block
 *
 * This file is responsible for rendering selected shipping addresses and options from the Multiple Addresses Shipping plugin.
 *
 * @package WC_Shipping_Multiple_Addresses
 */

const { registerPlugin }                                         = window.wp.plugins;
const { ExperimentalOrderShippingPackages, extensionCartUpdate } = window.wc.blocksCheckout;
const { CART_STORE_KEY }                                         = window.wc.wcBlocksData;
const { RawHTML, useState, useEffect }                           = window.wp.element;
const { dispatch }                                               = window.wp.data;
const { Button, Textarea, CheckboxControl, TextInput }           = window.wc.blocksComponents;

import './style.scss';

const onClickSetWCMSButton = ( data_url ) => {
	localStorage.removeItem( 'ms_note' );
	localStorage.removeItem( 'ms_date' );
	localStorage.removeItem( 'ms_gift' );

	location.href = data_url;
}

const maybeGetWCMSButtonHTML = ( staticVar ) => {
	if ( staticVar.has_multi_address || ! staticVar.is_eligible_wcms ) {
		return '';
	}

	return (
		<div className="wcms-set-button-container">
			<p className="wcms-block-notification" href={ staticVar.modify_addr_link }>{ staticVar.lang_notification }</p>
			<div className="wcms-block-button">
				<Button
					className="wcms-block-button-set"
					onClick={ ( e ) => onClickSetWCMSButton( staticVar.modify_addr_link ) }
					label={ staticVar.lang_button }
				>{ staticVar.lang_button }</Button>
			</div>
		</div>
	);
};

const getProductLI = ( product, index ) => {
	return (
		<li key={index}>
			<div className="product-label"><RawHTML>{ product.title }</RawHTML></div>
			<div className="product-attribute"><RawHTML>{ product.attribute }</RawHTML></div>
		</li>
	);
};

const getShippingMethodHTML = ( chosenShippingMethod, noMethodText ) => {
	if ( chosenShippingMethod.length < 1 ) {
		return (
			<div className="shipping-error">{ noMethodText }</div>
		);
	}

	return (
		<div className="shipping-method">
			<span className="shipping-method-label"><RawHTML>{ chosenShippingMethod.option_label }</RawHTML></span>
		</div>
	);
};

var delayTimer = 0;

const maybeGetShippingFieldsHTML = ( data, index, staticVar, namespace ) => {
	const msNote = ( localStorage.getItem( 'ms_note' ) ) ? JSON.parse( localStorage.getItem( 'ms_note' ) ) : {};
	const msDate = ( localStorage.getItem( 'ms_date' ) ) ? JSON.parse( localStorage.getItem( 'ms_date' ) ) : {};
	const msGift = ( localStorage.getItem( 'ms_gift' ) ) ? JSON.parse( localStorage.getItem( 'ms_gift' ) ) : {};

	const [ shippingNote, setShippingNote ] = useState( data['note'] );
	const [ shippingDate, setShippingDate ] = useState( data['date'] );
	const [ shippingGift, setShippingGift ] = useState( data['gift'] );
	const [ errorDate, setErrorDate ]       = useState( '' );

	const updateCartData = () => {
		const savedNotes = ( localStorage.getItem( 'ms_note' ) ) ? JSON.parse( localStorage.getItem( 'ms_note' ) ) : {};
		const savedDates = ( localStorage.getItem( 'ms_date' ) ) ? JSON.parse( localStorage.getItem( 'ms_date' ) ) : {};
		const savedGifts = ( localStorage.getItem( 'ms_gift' ) ) ? JSON.parse( localStorage.getItem( 'ms_gift' ) ) : {};

		extensionCartUpdate(
			{
				namespace,
				data: {
					'notes': savedNotes,
					'dates': savedDates,
					'gifts': savedGifts
				}
			}

		);

		dispatch( CART_STORE_KEY ).updateCustomerData();
	};

	const onNoteChange = ( textNote ) => {
		if ( Number.isInteger( staticVar.note_limit ) && 0 !== staticVar.note_limit && textNote.length > staticVar.note_limit ) {
			return;
		}

		setShippingNote( textNote );
		msNote[ index ] = textNote;
		localStorage.setItem( 'ms_note', JSON.stringify( msNote ) );

		clearTimeout( delayTimer );

		delayTimer = setTimeout( updateCartData, 1000 );
	};

	const validateDateValue = ( textDate ) => {
		const validDates = staticVar.valid_dates.map( ( date ) => parseInt( date ) );
		const excludedDates = staticVar.excluded_dates;
		const dateObj = new Date( textDate );
		const day = dateObj.getDay();
		const date = dateObj.getDate();
		const month = ( '0' + ( dateObj.getMonth() + 1 ) ).slice( -2 );
		const year = dateObj.getFullYear();
		const newDateString = month + '-' + date + '-' + year;

		if ( validDates.length > 0 && ! validDates.includes( day ) ) {
    		return false;
		}

		if ( excludedDates.length > 0 && excludedDates.includes( newDateString ) ) {
			return false;
		}

		return true;
	};

	const onDateChange = ( textDate ) => {
		if ( ! validateDateValue( textDate ) ) {
			setErrorDate( staticVar.date_error_text + ' ' + textDate );
			return;
		}

		setErrorDate( '' );
		setShippingDate( textDate );
		msDate[ index ] = textDate;
		localStorage.setItem( 'ms_date', JSON.stringify( msDate ) );

		updateCartData();
	};

	const onGiftChange = ( checked ) => {
		const isGift = checked ? 'yes' : 'no';

		setShippingGift( isGift );
		msGift[ index ] = isGift;
		localStorage.setItem( 'ms_gift', JSON.stringify( msGift ) );

		updateCartData();
	};

	const maybeGetNoteFieldHTML = () => {

		if ( ! staticVar.show_notes ) {
			return '';
		}

		return (
			<div className="wcms-input-container wcms-input-note">
				<div className="wcms-shipping-note-label"><span>{ staticVar.note_label_text }</span></div>
				<Textarea
					className="wcms-shipping-note"
					name={ 'shipping_note[' + index + ']' }
					onTextChange={ ( value ) => onNoteChange( value ) }
					value={ shippingNote }
				/>
			</div>
		);
	}

	const maybeGetDateFieldHTML = () => {

		if ( ! staticVar.show_datepicker ) {
			return '';
		}

		const currentDate = new Date();
		const curDateString = currentDate.getFullYear() + '-' + ( '0' + ( currentDate.getMonth() + 1 ) ).slice( -2 ) + '-' + ( '0' + ( currentDate.getDate() ) ).slice( -2 );

		return (
			<div className="wcms-input-container wcms-input-date">
				<div className="wcms-shipping-date-label"><span>{ staticVar.date_label_text }</span></div>
				<TextInput
					type="date"
					className="wcms-shipping-date"
					min={ curDateString }
					onChange={ ( value ) => onDateChange( value ) }
					value={ shippingDate }
				/>
				<span className="error_date">{ errorDate }</span>
			</div>
		);
	}

	const maybeGetGiftFieldHTML = () => {

		if ( ! staticVar.show_gifts ) {
			return '';
		}

		const isChecked = ( 'yes' === shippingGift ) ? true : false;

		return (
			<div className="wcms-input-container wcms-input-gift">
				<CheckboxControl
					className="wcms-shipping-gift"
					label={ staticVar.gifts_text }
					onChange={ ( checked ) => onGiftChange( checked ) }
					checked={ isChecked }
				/>
			</div>
		);
	}

	const noteFieldHTML = maybeGetNoteFieldHTML();
	const dateFieldHTML = maybeGetDateFieldHTML();
	const giftFieldHTML = maybeGetGiftFieldHTML();

	if ( ! noteFieldHTML && ! dateFieldHTML && ! giftFieldHTML ) {
		return '';
	}

	return (
		<div className="wcms-shipping-note-container">
			{ noteFieldHTML }
			{ dateFieldHTML }
			{ giftFieldHTML }
		</div>
	);
}

const getAddressInfoHTML = ( { data, index, staticVar, namespace } ) => {
	let productListHTML = [];
	const shippingMethodHTML = getShippingMethodHTML( data['chosen_shipping_method'], staticVar.no_method_text );
	const shippingFieldsHTML = maybeGetShippingFieldsHTML( data, index, staticVar, namespace );

	productListHTML = productListHTML.concat( data.products.map( ( product, index ) => getProductLI( product, index ) ) );

	return (
		<div key={index} className="wcms-block-addresses">
			<div className="wcms-block-package-name">{ data.package_name }</div>
			<div className="wcms-block-address"><RawHTML>{ data.formatted_address }</RawHTML></div>
			<div className="wcms-block-products">
				<ul>
					{ productListHTML }
				</ul>
			</div>
			<div className="wcms-block-shipping-methods">
				{ shippingMethodHTML }
			</div>
			<div>
				{ shippingFieldsHTML }
			</div>
		</div>
	);
};

const onClickModifyButton = ( modify_addr_link ) => {
	location.href = modify_addr_link;
};

const getModifyResetButtonHTML = ( staticVar ) => {
	return (
		<div className="wcms-block-button">
			<Button
				className="wcms-block-button-modify"
				onClick={ ( e ) => onClickModifyButton( staticVar.modify_addr_link ) }
				label={ staticVar.modify_addr_text }
			>{ staticVar.modify_addr_text }</Button>
			<a className="wcms-block-button-reset" href={ staticVar.reset_url }>{ staticVar.reset_addr_text }</a>
		</div>
	);
};

/**
 * Multiple Addresses shipping component.
 *
 * @param extensions
 * @returns {JSX.Element}
 * @constructor
 */
const MultipleAddresses = ( { extensions } ) => {
	const namespace = 'wc_shipping_multiple_addresses';
	let has_errors  = false;

	// First check for the namespace and multi_shipping_info existence. Skip if we don't have any relevant data.
	if ( ! extensions[ namespace ] || ! extensions[ namespace ][ 'multi_shipping_info' ] || ! extensions[ namespace ][ 'multi_shipping_info' ]['data'] ) {
		return <div className="woocommerce-shipping-multiple-addresses-info"></div>;
	}

	const datas = extensions[ namespace ][ 'multi_shipping_info' ]['data'];
	const staticVar = extensions[ namespace ][ 'multi_shipping_info' ]['static_var'];

	if ( ! staticVar.is_eligible_wcms ) {
		return <div className="woocommerce-shipping-multiple-addresses-info"></div>;
	}

	const WCMSButtonHTML = maybeGetWCMSButtonHTML( staticVar );
	
	// If cart is eligible for WCMS and multiple shipping is not being set yet,
	// Then it will display WCMS button to set the multiple shipping.
	if ( WCMSButtonHTML ) {
		return ( 
			<div className="woocommerce-shipping-multiple-addresses-info">
				{ WCMSButtonHTML }
			</div>
		);
	}

	let multiShippingHTML = [];
	const modifyResetButtonHTML = getModifyResetButtonHTML( staticVar );

	multiShippingHTML = multiShippingHTML.concat(
		datas.map( ( data, index ) => {
				const addressInfo = {
					data,
					index,
					staticVar,
					namespace
				};

				return getAddressInfoHTML( addressInfo );
			}
		) 
	);

	// Use effect to trigger `updateCustomerData` dispatch in order to update the tax for the first time.
	useEffect( () => {
		setTimeout( function() {
			dispatch( CART_STORE_KEY ).updateCustomerData();
		}, 400 );
	}, [] );

	return ( 
		<div className="woocommerce-shipping-multiple-addresses-info">
			<div>{ multiShippingHTML }</div>
			<div>{ modifyResetButtonHTML }</div>
		</div>
	);
}

const render = () => {
	return (
		<ExperimentalOrderShippingPackages>
			<MultipleAddresses />
		</ExperimentalOrderShippingPackages>
	);
};

registerPlugin( 'wcms-block-multiple-addresses', {
	render,
	scope: 'woocommerce-checkout',
} );
</file>

<file path="client/duplicate-cart-button/index.js">
/**
 * Duplicate Cart Button Block
 *
 * This file is responsible for rendering duplicate cart button from the Multiple Addresses Shipping plugin.
 *
 * @package WC_Shipping_Multiple_Addresses
 */

const { registerPlugin }            = window.wp.plugins;
const { ExperimentalDiscountsMeta } = window.wc.blocksCheckout;
const { Button }                    = window.wc.blocksComponents;

import './style.scss';

/**
 * Duplicate cart button component.
 *
 * @param extensions
 * @returns {JSX.Element}
 * @constructor
 */
const DuplicateCartButton = ( { extensions } ) => {
	const namespace = 'wc_shipping_multiple_addresses';

	// First check for the namespace and duplicate_cart existence. And skip if there are no text and url for this type.
	if ( ! extensions[ namespace ] || ! extensions[ namespace ][ 'duplicate_cart' ] || ! extensions[ namespace ][ 'duplicate_cart' ]['text'] || ! extensions[ namespace ][ 'duplicate_cart' ]['url'] ) {
		return <></>;
	}

	const datas = extensions[ namespace ][ 'duplicate_cart' ];

	return ( 
		<div className="wcms-duplicate-cart">
			<Button
				className="wcms-duplicate-cart-button"
				onClick={ ( e ) => buttonOnClick( datas.url ) }
				label={ datas.text }
			>{ datas.text }</Button>
		</div>
	);
}

const buttonOnClick = ( data_url ) => {
	location.href = data_url;
};

const render = () => {
	return (
		<ExperimentalDiscountsMeta>
			<DuplicateCartButton />
		</ExperimentalDiscountsMeta>
	);
};

registerPlugin( 'wcms-duplicate-cart-button', {
	render,
	scope: 'woocommerce-checkout',
} );
</file>

<file path="includes/class-blocks-integration.php">
<?php
/**
 * Multiple Addresses Blocks Integration class.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

namespace WooCommerce\Multiple_Addresses;

defined( 'ABSPATH' ) || exit;

use Automattic\WooCommerce\Blocks\Integrations\IntegrationInterface;

/**
 * Multiple Addresses Blocks Integration class for Multiple Shipping Addresses.
 */
class Blocks_Integration implements IntegrationInterface {

	/**
	 * The name of the integration.
	 *
	 * @return string
	 */
	public function get_name(): string {
		return 'wcms-block-multiple-addresses';
	}

	/**
	 * When called invokes any initialization/setup for the integratidon.
	 */
	public function initialize() {
		$this->register_scripts();
	}

	/**
	 * Returns an array of script handles to enqueue in the frontend context.
	 *
	 * @return string[]
	 */
	public function get_script_handles(): array {
		return array(
			'wcms-block-multiple-addresses',
			'wcms-duplicate-cart-button',
		);
	}

	/**
	 * Returns an array of script handles to enqueue in the editor context.
	 *
	 * @return string[]
	 */
	public function get_editor_script_handles(): array {
		return array();
	}

	/**
	 * An array of key, value pairs of data made available to the block on the client side.
	 *
	 * @return array
	 */
	public function get_script_data(): array {
		return array();
	}

	/**
	 * Registers the scripts and styles for the integration.
	 */
	public function register_scripts() {

		foreach ( $this->get_script_handles() as $handle ) {
			$this->register_script( $handle );
		}
	}

	/**
	 * Register a script for the integration.
	 *
	 * @param string $handle Script handle.
	 */
	protected function register_script( string $handle ) {
		$script_path = $handle . '.js';
		$script_url  = WC_MS_DIST_URL . $script_path;

		$script_asset_path = WC_MS_DIST_DIR . $handle . '.asset.php';
		$script_asset      = file_exists( $script_asset_path )
			? require $script_asset_path // nosemgrep: audit.php.lang.security.file.inclusion-arg --- This is a safe file inclusion.
			: array(
				'dependencies' => array(),
				'version'      => $this->get_file_version( WC_MS_DIST_DIR . $script_path ),
			);

		$style_path = 'style-' . $handle . '.css';
		$style_url  = WC_MS_DIST_URL . $style_path;

		wp_register_script(
			$handle,
			$script_url,
			$script_asset['dependencies'],
			$script_asset['version'],
			true
		);

		wp_set_script_translations(
			$handle,
			'woocommerce-shipping-multiple-addresses',
			WC_MS_ABSPATH . 'languages'
		);

		wp_enqueue_style(
			$handle,
			$style_url,
			array(),
			$script_asset['version']
		);
	}

	/**
	 * Get the file modified time as a cache buster if we're in dev mode.
	 *
	 * @param string $file Local path to the file.
	 *
	 * @return string The cache buster value to use for the given file.
	 */
	protected function get_file_version( string $file ): string {
		if ( defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG && file_exists( $file ) ) {
			return filemtime( $file );
		}

		return WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION;
	}
}
</file>

<file path="includes/class-wc-ms-order-type-order-shipment.php">
<?php
/**
 * Class WC_MS_Order_Type_Order_Shipment file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Order_Type_Order_Shipment
 */
class WC_MS_Order_Type_Order_Shipment extends WC_Order {
	/**
	 * Get internal type (post type.)
	 *
	 * @return string
	 */
	public function get_type() {
		return 'order_shipment';
	}

	/**
	 * Set order key.
	 *
	 * @param string $value Max length 37 chars.
	 * @throws WC_Data_Exception Throws exception when invalid data is found.
	 */
	public function set_order_key( $value ) {
		$this->set_prop( 'order_key', substr( $value, 0, 37 ) );
	}
}
</file>

<file path="includes/class-wc-ms-packages.php">
<?php
/**
 * Class WC_MS_Packages file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Packages.
 */
class WC_MS_Packages {
	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_filter( 'woocommerce_cart_shipping_packages', array( $this, 'shipping_packages' ) );
	}

	/**
	 * Filter the shipping packages to break it up into multiple packages
	 *
	 * @param array $packages Cart packages.
	 *
	 * @return array
	 */
	public function shipping_packages( $packages ) {
		$wcms_packages = array();
		$settings      = $this->wcms->settings;
		$methods       = ( wcms_session_isset( 'shipping_methods' ) ) ? wcms_session_get( 'shipping_methods' ) : array();

		// If multiple shipping is already setup then split packages.
		$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
		if ( ! empty( $sess_cart_addresses ) ) {

			// Group items into ship-to addresses.
			$addresses = wcms_session_get( 'cart_item_addresses' );

			$products_array = array();
			$address_fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

			// Loop through all cart items.
			if ( wcms_count_real_cart_items() > 0 ) {
				foreach ( wcms_get_real_cart_items() as $cart_item_key => $values ) {
					$qty = $values['quantity'];

					// Split each cart item by quantity.
					for ( $i = 1; $i <= $qty; $i++ ) {
						if ( isset( $addresses[ 'shipping_first_name_' . $cart_item_key . '_' . $values['product_id'] . '_' . $i ] ) ) {
							$address = array();

							foreach ( $address_fields as $field_name => $field ) {
								$address_key             = str_replace( 'shipping_', '', $field_name );
								$addresses_key           = $field_name . '_' . $cart_item_key . '_' . $values['product_id'] . '_' . $i;
								$address[ $address_key ] = ( isset( $addresses[ $addresses_key ] ) ) ? $addresses[ $addresses_key ] : '';
							}
						} else {
							$address = array();

							foreach ( $address_fields as $field_name => $field ) {
								$address_key             = str_replace( 'shipping_', '', $field_name );
								$address[ $address_key ] = '';
							}
						}

						// Fixes for issue #206
						// 'address' array key is needed for FedEx compatibility.
						$address['address'] = ( isset( $address['address_1'] ) ) ? $address['address_1'] : '';

						$current_address = wcms_get_formatted_address( $address );
						$key             = md5( $current_address );

						// Update values for individual cart items.
						$_value  = $values;
						$price   = $_value['line_total'] / $qty;
						$tax     = $_value['line_tax'] / $qty;
						$sub     = $_value['line_subtotal'] / $qty;
						$sub_tax = $_value['line_subtotal_tax'] / $qty;

						$_value['cart_key']          = $cart_item_key;
						$_value['quantity']          = 1;
						$_value['line_total']        = $price;
						$_value['line_tax']          = $tax;
						$_value['line_subtotal']     = $sub;
						$_value['line_subtotal_tax'] = $sub_tax;
						$meta                        = md5( wc_get_formatted_cart_item_data( $_value ) );

						$method = $this->wcms->get_product_shipping_method( $values['product_id'] );
						if ( ! $method ) {
							$method = '';
						}

						$key .= $method;

						if ( isset( $products_array[ $key ] ) ) {

							// If the same product exists, add to the qty and cost.
							$found = false;
							foreach ( $products_array[ $key ]['products'] as $idx => $prod ) {
								if ( intval( $prod['id'] ) === intval( $_value['product_id'] ) ) {
									if ( isset( $prod['value']['variation_id'], $_value['variation_id'] ) && intval( $prod['value']['variation_id'] ) !== intval( $_value['variation_id'] ) ) {
										continue;
									}

									if ( $meta === $prod['meta'] ) {
										$found = true;
										$products_array[ $key ]['products'][ $idx ]['value']['quantity']          += 1;
										$products_array[ $key ]['products'][ $idx ]['value']['line_total']        += $_value['line_total'];
										$products_array[ $key ]['products'][ $idx ]['value']['line_tax']          += $_value['line_tax'];
										$products_array[ $key ]['products'][ $idx ]['value']['line_subtotal']     += $_value['line_subtotal'];
										$products_array[ $key ]['products'][ $idx ]['value']['line_subtotal_tax'] += $_value['line_subtotal_tax'];
										break;
									}
								}
							}

							// Add a new product.
							if ( ! $found ) {
								$products_array[ $key ]['products'][] = array(
									'id'    => $_value['product_id'],
									'meta'  => $meta,
									'value' => $_value,
								);
							}
						} else {

							$products_array[ $key ] = array(
								'products' => array(
									array(
										'id'    => $_value['product_id'],
										'meta'  => $meta,
										'value' => $_value,
									),
								),
								'country'  => $address['country'],
								'city'     => $address['city'],
								'state'    => $address['state'],
								'postcode' => $address['postcode'],
								'address'  => $address,
							);
						}

						if ( ! empty( $method ) ) {
							$products_array[ $key ]['method'] = $method;
						}
					}
				}

				// Create packages from split products array.
				if ( ! empty( $products_array ) ) {
					$wcms_packages = array();
					foreach ( $products_array as $idx => $group ) {
						$pkg = array(
							'contents'      => array(),
							'contents_cost' => 0,
							'cart_subtotal' => 0,
							'destination'   => $group['address'],
						);

						if ( isset( $group['method'] ) ) {
							$pkg['method'] = $group['method'];
						}

						if ( isset( $methods[ $idx ] ) ) {
							$pkg['selected_method'] = $methods[ $idx ];
						}

						foreach ( $group['products'] as $item ) {
							/**
							 * Filter to manipulate or add cart item data.
							 *
							 * @param array Cart item data.
							 * @param array Product ID in the cart item.
							 * @param array Product variation ID in the cart item.
							 *
							 * @since 3.3.25
							 */
							$data = (array) apply_filters( 'woocommerce_add_cart_item_data', array(), $item['value']['product_id'], $item['value']['variation_id'] );

							// list array keys to be removed in cart item data.
							$keys_to_remove = array( 'product_id', 'variation_id', 'variation', 'quantity', 'data', 'line_tax_data', 'line_subtotal', 'line_subtotal_tax', 'line_total', 'line_tax', 'cart_key', 'data_hash', 'key' );

							// Get cart item data from add ons and generate cart id.
							$data         = array_merge( $data, array_diff_key( $item['value'], array_flip( $keys_to_remove ) ) );
							$cart_item_id = WC()->cart->generate_cart_id( $item['value']['product_id'], $item['value']['variation_id'], $item['value']['variation'], $data );

							$item['value']['package_idx'] = $idx;

							$pkg['contents'][ $cart_item_id ] = $item['value'];
							if ( $item['value']['data']->needs_shipping() ) {
								$pkg['contents_cost'] += $item['value']['line_total'];

								if ( WC()->cart->display_prices_including_tax() ) {
									$pkg['cart_subtotal'] += $item['value']['line_subtotal'] + $item['value']['line_subtotal_tax'];
								} else {
									$pkg['cart_subtotal'] += $item['value']['line_subtotal'];
								}
							}
						}

						$wcms_packages[] = $pkg;
					}

					// Return shipping packages which we created.
					if ( ! empty( $wcms_packages ) ) {
						wc_enqueue_js( '_multi_shipping = true;' );
						$packages = $this->normalize_packages_address( $wcms_packages );
					}
				}
			}
		}

		wcms_session_set( 'wcms_packages', $packages );
		return $packages;
	}

	/**
	 * This method copies the destination and full address from $base_package if it exists over to the current package index
	 *
	 * @param array $packages Cart packages.
	 *
	 * @return array modified $packages
	 */
	public function normalize_packages_address( $packages ) {
		$default = $this->get_default_shipping_address();

		if ( ! empty( $default ) ) {
			foreach ( $packages as $idx => $package ) {
				if ( ( ! isset( $package['destination'] ) || $this->wcms->is_address_empty( $package['destination'] ) ) && ! $this->wcms->is_address_empty( $default ) ) {
					$packages[ $idx ]['destination'] = $default;
				}
			}
		}

		return $packages;
	}

	/**
	 * Return a default address based on the customer information
	 *
	 * @return array default address
	 */
	public function get_default_shipping_address() {
		$first_name = WC()->customer->get_shipping_first_name();
		$last_name  = WC()->customer->get_shipping_last_name();
		$company    = WC()->customer->get_shipping_company();

		return array(
			'first_name' => $first_name,
			'last_name'  => $last_name,
			'company'    => $company,
			'address_1'  => WC()->customer->get_shipping_address(),
			'address_2'  => WC()->customer->get_shipping_address_2(),
			'city'       => WC()->customer->get_shipping_city(),
			'state'      => WC()->customer->get_shipping_state(),
			'postcode'   => WC()->customer->get_shipping_postcode(),
			'country'    => WC()->customer->get_shipping_country(),
		);
	}
}
</file>

<file path="includes/class-wc-ms-post-types.php">
<?php
/**
 * Class WC_MS_Post_Types file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class WC_MS_Post_Types.
 */
class WC_MS_Post_Types {

	/**
	 * Initiating.
	 */
	public static function init() {
		add_action( 'init', array( __CLASS__, 'register_post_types' ), 10 );
	}

	/**
	 * Register the post type for order shipment.
	 */
	public static function register_post_types() {
		if ( post_type_exists( 'order_shipment' ) ) {
			return;
		}

		wc_register_order_type(
			'order_shipment',
			/**
			 * Filter to manipulate the order shipment post type args.
			 *
			 * @param array Post type args.
			 *
			 * @since 3.3
			 */
			apply_filters(
				'wc_ms_register_post_type_order_shipment',
				array(
					'label'                            => __( 'Order Shipments', 'woocommerce-shipping-multiple-addresses' ),
					'description'                      => __( 'This is where store order shipments are stored.', 'woocommerce-shipping-multiple-addresses' ),
					'public'                           => false,
					'show_ui'                          => true,
					'capability_type'                  => 'shop_order',
					'map_meta_cap'                     => true,
					'publicly_queryable'               => false,
					'exclude_from_search'              => true,
					'show_in_menu'                     => false,
					'hierarchical'                     => false,
					'show_in_nav_menus'                => false,
					'rewrite'                          => false,
					'query_var'                        => false,
					'supports'                         => array( 'title', 'comments', 'custom-fields' ),
					'has_archive'                      => false,
					'exclude_from_orders_screen'       => true,
					'add_order_meta_boxes'             => false,
					'exclude_from_order_count'         => true,
					'exclude_from_order_views'         => true,
					'exclude_from_order_reports'       => true,
					'exclude_from_order_sales_reports' => true,
					'class_name'                       => 'WC_MS_Order_Type_Order_Shipment',
				)
			)
		);
	}
}

WC_MS_Post_Types::init();
</file>

<file path="includes/class-wc-ms-privacy.php">
<?php
/**
 * Class WC_MS_Privacy file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! class_exists( 'WC_Abstract_Privacy' ) ) {
	return;
}

/**
 * Class WC_MS_Privacy.
 */
class WC_MS_Privacy extends WC_Abstract_Privacy {
	/**
	 * Constructor
	 */
	public function __construct() {
		parent::__construct( __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' ) );

		$this->add_exporter( 'woocommerce-shipping-multiple-addresses-order-data', __( 'WooCommerce Multple Shipping Order Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'order_data_exporter' ) );
		$this->add_eraser( 'woocommerce-shipping-multiple-addresses-order-data', __( 'WooCommerce Multiple Shipping Order Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'order_data_eraser' ) );

		$this->add_exporter( 'woocommerce-shipping-multiple-addresses-customer-data', __( 'WooCommerce Multiple Shipping Customer Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'customer_data_exporter' ) );
		$this->add_eraser( 'woocommerce-shipping-multiple-addresses-customer-data', __( 'WooCommerce Multiple Shipping Customer Data', 'woocommerce-shipping-multiple-addresses' ), array( $this, 'customer_data_eraser' ) );
	}

	/**
	 * Returns a list of orders that are using multiple shipping.
	 *
	 * @param string $email_address Email address.
	 * @param int    $page Page number in pagination.
	 *
	 * @return array WP_Post
	 */
	protected function get_s2ma_orders( $email_address, $page ) {
		$user = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.

		$order_query = array(
			'meta_key'   => '_multiple_shipping',// phpcs:ignore --- The meta is needed.
			'meta_value' => 'yes',// phpcs:ignore --- The meta is needed.
			'limit'      => 10,
			'page'       => $page,
		);

		if ( $user instanceof WP_User ) {
			$order_query['customer_id'] = (int) $user->ID;
		} else {
			$order_query['billing_email'] = $email_address;
		}

		return wc_get_orders( $order_query );
	}

	/**
	 * Gets the message of the privacy to display.
	 */
	public function get_privacy_message() {
		// translators: %s is a privacy shipping URL.
		return wpautop( sprintf( __( 'By using this extension, you may be storing personal data or sharing data with an external service. <a href="%s" target="_blank">Learn more about how this works, including what you may want to include in your privacy policy.</a>', 'woocommerce-shipping-multiple-addresses' ), 'https://docs.woocommerce.com/document/privacy-shipping/#woocommerce-shipping-multiple-addresses' ) );
	}

	/**
	 * Handle exporting data for Orders.
	 *
	 * @param string $email_address E-mail address to export.
	 * @param int    $page          Pagination of data.
	 *
	 * @return array
	 */
	public function order_data_exporter( $email_address, $page = 1 ) {
		$done           = false;
		$data_to_export = array();

		$orders = $this->get_s2ma_orders( $email_address, (int) $page );

		$done = true;

		if ( 0 < count( $orders ) ) {
			foreach ( $orders as $order ) {
				$packages = $order->get_meta( '_wcms_packages' );

				foreach ( $packages as $idx => $package ) {
					$products = $package['contents'];
					$address  = ( ! empty( $package['destination'] ) ) ? WC()->countries->get_formatted_address( $package['destination'], ', ' ) : '';

					// translators: %s is package destination address.
					$data  = sprintf( __( 'Products listing for shipping address "%s": ', 'woocommerce-shipping-multiple-addresses' ), $address );
					$data .= implode(
						', ',
						array_map(
							function ( $product ) {
								return get_the_title( $product['data']->id );
							},
							$products
						)
					);

					$order_note = $order->get_meta( '_note_' . $idx );

					if ( ! empty( $order_note ) ) {
						// translators: %s is order note.
						$data .= sprintf( __( '. Note: %s.', 'woocommerce-shipping-multiple-addresses' ), $order_note );
					}

					$data_to_export[] = array(
						'group_id'    => 'woocommerce_orders',
						'group_label' => __( 'Orders', 'woocommerce-shipping-multiple-addresses' ),
						'item_id'     => 'order-' . $order->get_id(),
						'data'        => array(
							array(
								// translators: %s is package index.
								'name'  => sprintf( __( 'Multiple Shipping package "%s"', 'woocommerce-shipping-multiple-addresses' ), $idx ),
								'value' => $data,
							),
						),
					);
				}

				$shipment_data = WC_MS_Order_Shipment::get_shipment_objects_by_order( $order->get_id() );

				foreach ( $shipment_data as $shipment ) {
					$data_to_export[] = array(
						'group_id'    => 'woocommerce_orders',
						'group_label' => __( 'Orders', 'woocommerce-shipping-multiple-addresses' ),
						'item_id'     => 'order-' . $order->get_id(),
						'data'        => array(
							array(
								// translators: %s is for order shipment ID.
								'name'  => sprintf( __( 'Multiple Shipping Order Shipment "%s"', 'woocommerce-shipping-multiple-addresses' ), $shipment->get_id() ),
								'value' => $shipment->get_customer_note(),
							),
						),
					);
				}
			}

			$done = 10 > count( $orders );
		}

		return array(
			'data' => $data_to_export,
			'done' => $done,
		);
	}

	/**
	 * Finds and exports customer data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function customer_data_exporter( $email_address, $page ) {
		$user           = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.
		$data_to_export = array();

		if ( $user instanceof WP_User ) {
			$data_to_export[] = array(
				'group_id'    => 'woocommerce_customer',
				'group_label' => __( 'Customer Data', 'woocommerce-shipping-multiple-addresses' ),
				'item_id'     => 'user',
				'data'        => array(
					array(
						'name'  => __( 'Multiple Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
						'value' => wp_json_encode( get_user_meta( $user->ID, 'wc_other_addresses', true ) ),
					),
				),
			);
		}

		return array(
			'data' => $data_to_export,
			'done' => true,
		);
	}

	/**
	 * Finds and erases customer data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function customer_data_eraser( $email_address, $page ) {
		$page = (int) $page;
		$user = get_user_by( 'email', $email_address ); // Check if user has an ID in the DB to load stored personal data.

		$other_addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

		$items_removed = false;
		$messages      = array();

		if ( ! empty( $other_addresses ) ) {
			$items_removed = true;
			delete_user_meta( $user->ID, 'wc_other_addresses' );
			$messages[] = __( 'Multiple Shipping User Data Erased.', 'woocommerce-shipping-multiple-addresses' );
		}

		return array(
			'items_removed'  => $items_removed,
			'items_retained' => false,
			'messages'       => $messages,
			'done'           => true,
		);
	}

	/**
	 * Finds and erases order data by email address.
	 *
	 * @since 3.4.0
	 * @param string $email_address The user email address.
	 * @param int    $page  Page.
	 * @return array An array of personal data in name value pairs
	 */
	public function order_data_eraser( $email_address, $page ) {
		$orders = $this->get_s2ma_orders( $email_address, (int) $page );

		$items_removed  = false;
		$items_retained = false;
		$messages       = array();

		foreach ( (array) $orders as $order ) {
			$order = wc_get_order( $order->get_id() );

			list( $removed, $retained, $msgs ) = $this->maybe_handle_order( $order );
			$items_removed                    |= $removed;
			$items_retained                   |= $retained;
			$messages                          = array_merge( $messages, $msgs );
		}

		// Tell core if we have more orders to work on still.
		$done = count( $orders ) < 10;

		return array(
			'items_removed'  => $items_removed,
			'items_retained' => $items_retained,
			'messages'       => $messages,
			'done'           => $done,
		);
	}

	/**
	 * Handle eraser of data tied to Orders
	 *
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	protected function maybe_handle_order( $order ) {
		$order_id = $order->get_id();

		$packages          = $order->get_meta( '_shipping_packages' );
		$sess_item_address = $order->get_meta( '_shipping_addresses' );
		$sess_packages     = $order->get_meta( '_wcms_packages' );
		$ms_methods        = $order->get_meta( '_shipping_methods' );
		$sess_rates        = $order->get_meta( '_shipping_rates' );

		if ( empty( $packages ) && empty( $sess_item_address ) && empty( $sess_packages ) && empty( $ms_methods ) && empty( $sess_rates ) ) {
			return array( false, false, array() );
		}

		$shipment_data = WC_MS_Order_Shipment::get_shipment_objects_by_order( $order->get_id() );

		foreach ( $shipment_data as $shipment ) {
			$shipment->delete( true );
		}

		foreach ( $packages as $idx => $package ) {
			$order->delete_meta_data( '_note_' . $idx );
			$order->delete_meta_data( '_date_' . $idx );
		}

		$order->delete_meta_data( '_shipping_packages' );
		$order->delete_meta_data( '_shipping_addresses' );
		$order->delete_meta_data( '_wcms_packages' );
		$order->delete_meta_data( '_shipping_methods' );
		$order->delete_meta_data( '_shipping_rates' );
		$order->save();

		return array( true, false, array( __( 'Multiple Shipping Order Data Erased.', 'woocommerce-shipping-multiple-addresses' ) ) );
	}
}

new WC_MS_Privacy();
</file>

<file path="includes/class-wc-ms-shipping-easy.php">
<?php
/**
 * Class WC_MS_Shipworks file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Shipping_Easy.
 */
class WC_MS_Shipping_Easy {

	/**
	 * Class constructor.
	 */
	public function __construct() {
		add_action( 'woocommerce_order_action_se_send_to_shippingeasy', array( $this, 'process_manual_export' ), 1 );
		add_action( 'woocommerce_order_action_shippingeasy_export', array( $this, 'process_manual_export' ), 1 );
		add_action( 'woocommerce_thankyou', array( $this, 'send_shipments' ), 1 );
		add_action( 'woocommerce_payment_complete', array( $this, 'send_shipments' ), 1 );

		add_filter( 'se_order_values', array( $this, 'mark_as_gift' ), 5 );
		add_filter( 'se_order_values', array( $this, 'order_id_replacement' ), 10 );
		add_action( 'se_shipment_response', array( $this, 'log_shipment_response' ) );
	}

	/**
	 * Process the export.
	 *
	 * @param WC_Order $order Order object.
	 */
	public function process_manual_export( $order ) {
		$this->send_shipments( $order->get_id(), true );
		$order->update_meta_data( 'se_order_created', true );
		$order->save();
	}

	/**
	 * Send shipments.
	 *
	 * @param int     $parent_order_id ID of parent order.
	 * @param boolean $backend_order If order from backend.
	 */
	public function send_shipments( $parent_order_id, $backend_order = false ) {
		$parent_order = wc_get_order( $parent_order_id );

		if ( ! $parent_order ) {
			return;
		}

		if ( 'yes' === $parent_order->get_meta( '_multiple_shipping' ) ) {
			$shipment_ids = WC_MS_Order_Shipment::get_by_order( $parent_order_id );

			if ( class_exists( 'WC_ShippingEasy_Integration' ) ) {
				$se = new WC_ShippingEasy_Integration();

				foreach ( $shipment_ids as $shipment_id ) {
					$se->shipping_place_order( $shipment_id, $backend_order );
				}
			} else {
				foreach ( $shipment_ids as $shipment_id ) {
					shipping_place_order( $shipment_id, $backend_order );
				}
			}

			$parent_order->update_meta_data( 'se_order_created', true );
			$parent_order->save();
		}
	}

	/**
	 * Replace the Order ID value.
	 *
	 * @param array $values ShippingEasy value.
	 */
	public function order_id_replacement( $values ) {
		$shipment = wc_get_order( $values['external_order_identifier'] );

		if ( false !== $shipment && 'order_shipment' === $shipment->get_type() ) {
			$values['external_order_identifier'] = $shipment->get_parent_id() . '-' . $shipment->get_id();
		}

		return $values;
	}

	/**
	 * Mark the value as a gift. If there is a meta gift.
	 *
	 * @param array $values ShippingEasy value.
	 */
	public function mark_as_gift( $values ) {
		$shipment = wc_get_order( $values['external_order_identifier'] );

		if ( false !== $shipment && 'order_shipment' === $shipment->get_type() ) {
			if ( 1 === intval( $shipment->get_meta( '_gift' ) ) ) {
				$values['notes'] = ! empty( $values['notes'] ) ? $values['notes'] : '';
				$values['notes'] = __( 'This is a gift!', 'woocommerce-shipping-multiple-addresses' ) . "\n\n" . $values['notes'];
			}
		}

		return $values;
	}

	/**
	 * Log response from ShippingEasy.
	 *
	 * @param array $response Response from ShippingEasy.
	 */
	public function log_shipment_response( $response ) {

		$order_id = $response['shipment']['orders'][0]['ext_order_reference_id'];

		if ( strpos( $order_id, '_' ) !== false ) {
			$parts    = explode( '_', $order_id );
			$order_id = $parts[0];
		}

		$order = wc_get_order( $order_id );

		if ( ! ( $order instanceof WC_Order ) || 'order_shipment' !== $order->get_type() ) {
			return;
		}

		// Store the values of shipped order which we are getting from ShippingEasy.
		$tracking_number     = $response['shipment']['tracking_number'];
		$carrier_key         = $response['shipment']['carrier_key'];
		$carrier_service_key = $response['shipment']['carrier_service_key'];
		$shipment_cost_cents = $response['shipment']['shipment_cost'];
		$shipment_cost       = ( $shipment_cost_cents / 100 );

		/* translators: 1: tracking number 2: carrier key 3: carrier service key 4: cost */
		$comment_update = sprintf( __( 'Shipping Tracking Number: %1$s<br/>Carrier Key: %2$s<br/>Carrier Service Key: %3$s<br/>Cost: %4$s', 'woocommerce-shipping-multiple-addresses' ), $tracking_number, $carrier_key, $carrier_service_key, $shipment_cost );

		$order->add_order_note( $comment_update );
	}
}

new WC_MS_Shipping_Easy();
</file>

<file path="includes/class-wc-ms-shipworks.php">
<?php
/**
 * Class WC_MS_Shipworks file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Shipworks.
 */
class WC_MS_Shipworks {

	/**
	 * Class constructor
	 */
	public function __construct() {
		add_filter( 'se_woocommerce_order_rows', array( $this, 'send_split_orders' ), 10, 2 );
		add_filter( 'sc_orders_rows', array( $this, 'send_split_orders' ), 10, 1 );
		add_filter( 'woocommerce_new_customer_note', array( $this, 'customer_note_added' ), 1 );
	}

	/**
	 * Send order in split based on the multiple address shipment.
	 *
	 * @param array $rows Shipworks rows.
	 */
	public function send_split_orders( $rows ) {
		$new_rows = array();

		foreach ( $rows as $i => $row ) {
			$shipments = WC_MS_Order_Shipment::get_shipment_objects_by_order( $row['ID'] );

			if ( count( $shipments ) > 0 ) {
				foreach ( $shipments as $shipment ) {
					$order_note_count = count( wc_get_order_notes( array( 'order_id' => $shipment->get_id() ) ) );

					$new_rows[] = array(
						'ID'                    => $shipment->get_id(),
						// WC always sets this to 1 when using the CPT data store for orders.
						'post_author'           => 1,
						'post_date'             => gmdate( 'Y-m-d H:i:s', $shipment->get_date_created( 'edit' )->getOffsetTimestamp() ),
						'post_date_gmt'         => gmdate( 'Y-m-d H:i:s', $shipment->get_date_created( 'edit' )->getTimestamp() ),
						'post_content'          => '',
						// translators: %s is a date.
						'post_title'            => sprintf( __( 'Shipment &ndash; %s', 'woocommerce-shipping-multiple-addresses' ), gmdate( _x( 'M d, Y @ H:i A', 'Order date parsed by date function', 'woocommerce-shipping-multiple-addresses' ) ) ),
						'post_excerpt'          => $shipment->get_customer_note(),
						'post_status'           => $shipment->get_status(),
						'comment_status'        => 'open',
						'ping_status'           => 'closed',
						'post_password'         => $shipment->get_order_key(),
						// translators: %s Shipment ID.
						'post_name'             => sprintf( __( 'shipment-%s', 'woocommerce-shipping-multiple-addresses' ), $shipment->get_id() ),
						'to_ping'               => '',
						'pinged'                => '',
						'post_modified'         => gmdate( 'Y-m-d H:i:s', $shipment->get_date_modified( 'edit' )->getOffsetTimestamp() ),
						'post_modified_gmt'     => gmdate( 'Y-m-d H:i:s', $shipment->get_date_modified( 'edit' )->getTimestamp() ),
						'post_content_filtered' => '',
						'post_parent'           => $shipment->get_parent_id(),
						'guid'                  => $shipment->get_view_order_url(),
						'menu_order'            => 0,
						'post_type'             => $shipment->get_type(),
						'post_mime_type'        => '',
						'comment_count'         => $order_note_count,
					);
				}

				unset( $rows[ $i ] );

			}
		}

		$rows = array_merge( $rows, $new_rows );

		return $rows;
	}

	/**
	 * Add customer note into the order object.
	 *
	 * @param array $data Array of Order ID and customer note.
	 */
	public function customer_note_added( $data ) {
		$order = wc_get_order( $data['order_id'] );

		if ( false === $order || 'order_shipment' !== $order->get_type() ) {
			return;
		}

		$parent_order = wc_get_order( $order->get_parent_id() );

		if ( false === $parent_order ) {
			return;
		}

		$parent_order->add_order_note( $data['customer_note'], 1 );
	}
}

new WC_MS_Shipworks();
</file>

<file path=".nvmrc">
22.14.0
</file>

<file path="README.md">
[![CI](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/cron_qit.yml)

woocommerce-shipping-multiple-addresses
====================

Ship to multiple addresses from WooCommerce.

| Product Page | Documentation | Ideas board |
| ------------ | ------------- | ----------- |
| https://woocommerce.com/products/shipping-multiple-addresses/ | https://docs.woocommerce.com/document/multiple-ship-to-addresses/ | https://ideas.woocommerce.com/forums/133476-woocommerce/category/75738-category-shipping-methods |

## NPM Scripts

WooCommerce Shipping Multiple Addresses utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These include building JavaScript, SASS, CSS minification, and language files.

`pnpm run build:dev` - Runs the tasks necessary when developing.

`pnpm run watchsass` - Will continuously monitor changes in the `scss` files and will minify them automatically.
</file>

<file path="includes/integrations/class-wc-ms-customer-order-csv-export.php">
<?php
/**
 * Compatibility for Customer/Order CSV export plugin.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Plugin compatibility with `woocommerce-customer-order-csv-export`.
 */
class WC_MS_Customer_Order_Csv_Export {

	/**
	 * Plugin reference.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_filter( 'wc_customer_order_csv_export_order_headers', array( $this, 'modify_column_headers' ), 10, 1 );

		add_filter( 'wc_customer_order_export_csv_order_line_item', array( $this, 'sv_wc_csv_export_add_cart_id_to_order_line_item' ), 10, 2 );
		add_filter( 'wc_customer_order_export_csv_order_row_one_row_per_item', array( $this, 'sv_wc_csv_export_add_package_multiple_address' ), 10, 2 );
	}

	/**
	 * Method for adding an additional header for S2MA.
	 *
	 * @param  array $column_headers Existing column headers.
	 *
	 * @return array
	 *
	 * @since 3.6.0
	 */
	public function modify_column_headers( $column_headers ) {
		$column_headers['wcms'] = __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' );
		return $column_headers;
	}

	/**
	 * Adds the line item's cart key to the line item data for use by the one row per item
	 * filter.
	 *
	 * @param array                 $line_item {
	 *       line item data in key => value format
	 *       the keys are for convenience and not necessarily used for exporting. Make
	 *       sure to prefix the values with the desired line item entry name
	 * }.
	 * @param WC_Order_Item_Product $item WC order item data.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	public function sv_wc_csv_export_add_cart_id_to_order_line_item( $line_item, $item ) {
		$line_item['cart_key'] = wc_get_order_item_meta( $item->get_id(), '_wcms_cart_key', true );

		return $line_item;
	}

	/**
	 * Adds the corresponding package address to each line item.
	 *
	 * @param array $order_data array of order data.
	 * @param array $item array of item data.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	public function sv_wc_csv_export_add_package_multiple_address( $order_data, $item ) {
		$order_id = $order_data['order_id'];
		$order    = wc_get_order( $order_id );

		if ( ! $order ) {
			return $order_data;
		}

		$shipping_addresses = $order->get_meta( '_shipping_addresses' );

		// Quit if it's not multiple addresses.
		if ( empty( $shipping_addresses ) ) {
			return $order_data;
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $this->find_package( $packages, $item['cart_key'] );

		// Quit if destination does not exist in the package.
		if ( empty( $package['destination'] ) ) {
			return $order_data;
		}

		$address  = wcms_get_address( $package['destination'] );

		foreach ( $address as $addr_key => $addr_val ) {
			// Only add shipping address to csv.
			if ( isset( $order_data[ $addr_key ] ) && false !== strpos( $addr_key, 'shipping_' ) ) {
				$order_data[ $addr_key ] = $addr_val;
			}
		}

		$address = implode(
			'|',
			array_map(
				function ( $key, $value ) {
					return sprintf( '%s:%s', $key, $value );
				},
				array_keys( $address ),
				$address
			)
		);

		$order_data['wcms'] = $address;

		return $order_data;
	}

	/**
	 * Helper function to check the export format.
	 *
	 * @param \WC_Customer_Order_CSV_Export_Generator $csv_generator the generator instance.
	 *
	 * @return bool - true if this is a one row per item format
	 *
	 * @since 3.6.0
	 */
	public function is_one_row( $csv_generator ) {
		$one_row_per_item = false;
		if ( version_compare( wc_customer_order_csv_export()->get_version(), '4.0.0', '<' ) ) {
			// pre 4.0 compatibility.
			$one_row_per_item = ( 'default_one_row_per_item' === $csv_generator->order_format || 'legacy_one_row_per_item' === $csv_generator->order_format );
		} elseif ( isset( $csv_generator->format_definition ) ) {
			// post 4.0 (requires 4.0.3+).
			$one_row_per_item = 'item' === $csv_generator->format_definition['row_type'];
		}
		return $one_row_per_item;
	}

	/**
	 * Finds the package with the corresponding cart key.
	 *
	 * @param array  $packages Cart package.
	 * @param string $cart_key Cart key.
	 *
	 * @return array
	 *
	 * @since 3.6.11
	 */
	private function find_package( $packages, $cart_key ) {
		foreach ( $packages as $package ) {
			if ( array_key_exists( $cart_key, $package['contents'] ) ) {
				return $package;
			}
		}

		return null;
	}
}
</file>

<file path="includes/class-store-api-extension.php">
<?php
/**
 * Store_API_Extension class.
 *
 * A class to extend the store public API with Multiple Addresses shipping functionality.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

namespace WooCommerce\Multiple_Addresses;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\StoreApi\Schemas\V1\CartSchema;
use WC_Ship_Multiple;

/**
 * Store API Extension.
 */
class Store_API_Extension {
	/**
	 * Global WC_Ship_Multiple variable.
	 *
	 * @var WC_Ship_Multiple
	 */
	private static WC_Ship_Multiple $wcms;

	/**
	 * Plugin Identifier, unique to each plugin.
	 *
	 * @var string
	 */
	const IDENTIFIER = 'wc_shipping_multiple_addresses';

	/**
	 * Bootstraps the class and hooks required data.
	 *
	 * @since 1.0.0
	 */
	public static function init() {
		self::$wcms = $GLOBALS['wcms'];
		self::extend_store();
	}

	/**
	 * Registers the data into each endpoint.
	 */
	public static function extend_store() {
		$logger              = wc_get_logger();
		$update_callback_reg = woocommerce_store_api_register_update_callback(
			array(
				'namespace' => self::IDENTIFIER,
				'callback'  => function( $data ) {
					self::update_shipping_notes( $data );
				},
			)
		);

		if ( is_wp_error( $update_callback_reg ) ) {
			$logger->error( $update_callback_reg->get_error_message() );
			return;
		}

		$endpoint_data_reg = woocommerce_store_api_register_endpoint_data(
			array(
				'endpoint'        => CartSchema::IDENTIFIER,
				'namespace'       => self::IDENTIFIER,
				'data_callback'   => array( static::class, 'data_callback' ),
				'schema_callback' => array( static::class, 'schema_callback' ),
				'schema_type'     => ARRAY_A,
			)
		);

		if ( is_wp_error( $endpoint_data_reg ) ) {
			$logger->error( $endpoint_data_reg->get_error_message() );
			return;
		}
	}

	/**
	 * Update multiple shipping notes an delivery dates.
	 *
	 * @param array $post_data Shipping notes data from POST.
	 */
	public static function update_shipping_notes( $post_data ) {
		if ( ! empty( $post_data['notes'] ) && is_array( $post_data['notes'] ) ) {
			wcms_session_set( 'wcms_package_notes', $post_data['notes'] );
		}

		if ( ! empty( $post_data['dates'] ) && is_array( $post_data['dates'] ) ) {
			wcms_session_set( 'wcms_delivery_dates', $post_data['dates'] );
		}

		if ( ! empty( $post_data['gifts'] ) && is_array( $post_data['gifts'] ) ) {
			wcms_session_set( 'wcms_package_gifts', $post_data['gifts'] );
		}
	}

	/**
	 * Store API extension data callback.
	 *
	 * @return array
	 */
	public static function data_callback() {
		$packages          = WC()->cart->get_shipping_packages();
		$shipping_packages = WC()->shipping->get_packages();

		foreach ( $shipping_packages as $index => $package ) {
			if ( ! isset( $packages[ $index ] ) ) {
				continue;
			}

			$packages[ $index ]['rates'] = $package['rates'];
		}

		$data = ( self::$wcms->cart->cart_is_eligible_for_multi_shipping() ) ? self::prepare_multi_shipping_data( $packages ) : array();

		return array(
			'duplicate_cart'      => self::prepare_duplicate_cart_button(),
			'multi_shipping_info' => array(
				'data'       => $data,
				'static_var' => self::prepare_settings_static_variable(),
			),
		);
	}

	/**
	 * Store API extension schema callback.
	 *
	 * @return array Registered schema.
	 */
	public static function schema_callback() {
		return array(
			'duplicate_cart' => array(
				'description' => __( 'MS duplicate cart variable', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => array( 'array' ),
				'context'     => array( 'view' ),
				'readonly'    => true,
			),
			'multi_shipping_info' => array(
				'description' => __( 'MS info', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => array( 'array' ),
				'context'     => array( 'view' ),
				'readonly'    => true,
			),
		);
	}

	/**
	 * Prepare the duplicate cart data.
	 */
	public static function prepare_duplicate_cart_button() {
		$ms_settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		if ( ! isset( $ms_settings['cart_duplication'] ) || 'no' === $ms_settings['cart_duplication'] ) {
			return array();
		}

		$dupe_url = add_query_arg(
			array(
				'duplicate-form' => '1',
				'_wcmsnonce'     => wp_create_nonce( 'wcms-duplicate-cart' ),
			),
			get_permalink( wc_get_page_id( 'multiple_addresses' ) )
		);

		return array(
			'text' => esc_html__( 'Duplicate Cart', 'woocommerce-shipping-multiple-addresses' ),
			'url'  => $dupe_url,
		);
	}

	/**
	 * Prepare multiple shipping data.
	 *
	 * @param array $packages Cart packages.
	 */
	public static function prepare_multi_shipping_data( $packages ) {
		$page_id                 = wc_get_page_id( 'multiple_addresses' );
		$ms_permalink            = get_permalink( $page_id );
		$chosen_shipping_methods = wcms_session_get( 'shipping_methods' ) ?? array();
		$notes                   = wcms_session_get( 'wcms_package_notes' ) ?? array();
		$dates                   = wcms_session_get( 'wcms_delivery_dates' ) ?? array();
		$gifts                   = wcms_session_get( 'wcms_package_gifts' ) ?? array();
		$ms_packages             = array();

		foreach ( $packages as $x => $package ) {
			$products = $package['contents'];

			if ( self::$wcms->is_address_empty( $package['destination'] ) ) {
				$ms_packages[ $x ]['error_message'] = esc_html__( 'The following items do not have a shipping address assigned.', 'woocommerce-shipping-multiple-addresses' );
			} elseif ( ! isset( $package['rates'] ) || empty( $package['rates'] ) ) {
				$ms_packages[ $x ]['error_message'] = esc_html__( 'There are no shipping options available for the following items.', 'woocommerce-shipping-multiple-addresses' );
			}

			// translators: %d is package index.
			$ms_packages[ $x ]['package_name']      = sprintf( __( 'Shipment %d Address', 'woocommerce-shipping-multiple-addresses' ), intval( $x ) + 1 );
			$ms_packages[ $x ]['ms_permalink']      = $ms_permalink;
			$ms_packages[ $x ]['formatted_address'] = wcms_get_formatted_address( $package['destination'] );
			$ms_packages[ $x ]['products']          = array();

			foreach ( $products as $p => $product ) {
				$attributes    = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
				$product_title = wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
				$product_attr  = wp_kses_post( str_replace( "\n", '<br/>', $attributes ) );

				$ms_packages[ $x ]['products'][] = array(
					'attribute' => $product_attr,
					'title'     => $product_title,
				);
			}

			$ship_rates             = self::get_multi_addr_shipping_rates( $package, $packages );
			$chosen_shipping_method = array();
			$session_chosen_methods = WC()->session->get( 'chosen_shipping_methods' );

			foreach ( $ship_rates as $method_id => $method ) {
				if ( 'multiple_shipping' === $method_id ) {
					continue;
				}

				if ( isset( $chosen_shipping_methods[ $x ]['id'] ) && $chosen_shipping_methods[ $x ]['id'] === $method['option_value'] ) {
					$chosen_shipping_method = $method;
				} elseif ( isset( $session_chosen_methods[ $x ] ) && $session_chosen_methods[ $x ] === $method['option_value'] ) {
					$chosen_shipping_method = $method;
				}
			}

			$ms_packages[ $x ]['note']                   = isset( $notes[ $x ] ) ? $notes[ $x ] : '';
			$ms_packages[ $x ]['date']                   = isset( $dates[ $x ] ) ? $dates[ $x ] : '';
			$ms_packages[ $x ]['gift']                   = isset( $gifts[ $x ] ) ? $gifts[ $x ] : 'no';
			$ms_packages[ $x ]['chosen_shipping_method'] = $chosen_shipping_method;
			$ms_packages[ $x ]['session_cart_addresses'] = wcms_session_get( 'cart_item_addresses' );
		}

		return $ms_packages;
	}

	/**
	 * Formatting the shipping method label.
	 *
	 * @param object $method Shipping Method object.
	 */
	public static function get_formatted_shipping_method_label( $method ) {
		$label = esc_html( $method->label );

		if ( $method->cost <= 0 ) {
			return $label;
		}

		$shipping_tax = $method->get_shipping_tax();
		$label       .= ' &mdash; ';

		// Append price to label using the correct tax settings.
		if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {
			$label .= wc_price( $method->cost );
			if ( $shipping_tax > 0 && WC()->cart->prices_include_tax ) {
				$label .= ' ' . WC()->countries->ex_tax_or_vat();
			}

			return $label;
		}

		$label .= wc_price( $method->cost + $shipping_tax );
		if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
			$label .= ' ' . WC()->countries->inc_tax_or_vat();
		}

		return $label;
	}

	/**
	 * Get shipping rates for the multiple address.
	 *
	 * @param array $package Current package.
	 * @param array $packages the whole cart packages.
	 *
	 * @return array
	 */
	public static function get_multi_addr_shipping_rates( $package, $packages ) {
		$shipping_rates = array();

		$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
		if ( isset( $sess_cart_addresses ) && ! empty( $sess_cart_addresses ) ) {
			// Always allow users to select shipping.
			foreach ( $package['rates'] as $rate ) {
				$rate->label                 = self::get_formatted_shipping_method_label( $rate );
				$shipping_rates[ $rate->id ] = $rate;
				$shipping_rates[ $rate->id ] = array(
					'option_label' => wp_strip_all_tags( $rate->label ),
					'option_value' => esc_attr( $rate->id ),
				);
			}
		} elseif ( self::$wcms->packages_have_different_origins( $packages ) || self::$wcms->packages_have_different_methods( $packages ) || self::$wcms->packages_contain_methods( $packages ) ) {

			$type = ( self::$wcms->packages_have_different_origins( $packages ) || self::$wcms->packages_have_different_methods( $packages ) ) ? 1 : 2;

			// Show shipping methods available to each package.
			foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

				if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
					continue;
				}

				if ( ! $shipping_method->is_available( $package ) ) {
					continue;
				}

				// Reset Rates.
				$shipping_method->rates = array();

				// Calculate Shipping for package.
				$shipping_method->calculate_shipping( $package );

				// Place rates in package array.
				if ( empty( $shipping_method->rates ) || ! is_array( $shipping_method->rates ) ) {
					continue;
				}

				foreach ( $shipping_method->rates as $rate ) {
					$rate->label  = self::get_formatted_shipping_method_label( $rate );
					$option_label = ( 1 === $type ) ? wp_kses_post( wc_cart_totals_shipping_method_label( $rate ) ) : esc_html( $rate->label );
					$option_value = ( 1 === $type ) ? esc_attr( $rate->id ) . '||' . wp_strip_all_tags( $rate->label ) : esc_attr( $rate->id );

					$shipping_rates[ $rate->id ] = array(
						'option_label' => $option_label,
						'option_value' => $option_value,
					);
				}
			}
		}

		return $shipping_rates;
	}

	/**
	 * Prepare variables from the settings and static text or urls.
	 */
	public static function prepare_settings_static_variable() {
		$id                = wc_get_page_id( 'multiple_addresses' );
		$reset_url         = add_query_arg(
			array(
				'wcms_reset_address' => true,
				'nonce'              => wp_create_nonce( 'wcms_reset_address_security' ),
			),
			wc_get_checkout_url()
		);
		$modify_addr_link  = get_permalink( $id );
		$add_addr_link     = add_query_arg( 'cart', 1, get_permalink( $id ) );
		$has_multi_address = ( self::$wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() );
		$note_limit        = ! empty( self::$wcms->gateway_settings['checkout_notes_limit'] ) ? absint( self::$wcms->gateway_settings['checkout_notes_limit'] ) : '';
		$show_notes        = ( ! empty( self::$wcms->gateway_settings['checkout_notes'] ) && 'yes' === self::$wcms->gateway_settings['checkout_notes'] ) ? true : false;
		$show_datepicker   = ( ! empty( self::$wcms->gateway_settings['checkout_datepicker'] ) && 'yes' === self::$wcms->gateway_settings['checkout_datepicker'] ) ? true : false;
		$valid_dates       = ( ! empty( self::$wcms->gateway_settings['checkout_valid_days'] ) ) ? self::$wcms->gateway_settings['checkout_valid_days'] : array();
		$excluded_dates    = ( ! empty( self::$wcms->gateway_settings['checkout_exclude_dates'] ) ) ? self::$wcms->gateway_settings['checkout_exclude_dates'] : array();
		$show_gifts        = \WC_MS_Gifts::is_enabled();
		$lang_notification = \WC_Ship_Multiple::$lang['notification'];
		$lang_button       = \WC_Ship_Multiple::$lang['btn_items'];

		return array(
			'is_eligible_wcms'  => self::$wcms->cart->cart_is_eligible_for_multi_shipping(),
			'has_multi_address' => $has_multi_address,
			'reset_url'         => $reset_url,
			'modify_addr_link'  => $modify_addr_link,
			'add_addr_link'     => $add_addr_link,
			'modify_addr_text'  => esc_html__( 'Modify/Add Address', 'woocommerce-shipping-multiple-addresses' ),
			'reset_addr_text'   => esc_html__( 'Reset Address', 'woocommerce-shipping-multiple-addresses' ),
			'lang_notification' => esc_html( $lang_notification ),
			'lang_button'       => esc_attr( $lang_button ),
			'show_notes'        => $show_notes,
			'note_label_text'   => esc_html( 'Note:', 'woocommerce-shipping-multiple-addresses' ),
			'show_gifts'        => $show_gifts,
			'gifts_text'        => esc_html__( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ),
			'show_datepicker'   => $show_datepicker,
			'date_label_text'   => esc_html( 'Shipping date:', 'woocommerce-shipping-multiple-addresses' ),
			'valid_dates'       => $valid_dates,
			'excluded_dates'    => $excluded_dates,
			'date_error_text'   => esc_html__( 'The item cannot be send on', 'woocommerce-shipping-multiple-addresses' ),
			'note_limit'        => $note_limit,
			'no_method_text'    => esc_html__( 'No shipping method', 'woocommerce-shipping-multiple-addresses' ),
		);
	}
}
</file>

<file path="includes/class-wc-ms-admin-user-addresses-list-table.php">
<?php
/**
 * Class WC_MS_Admin_User_Addresses_List_Table file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! class_exists( 'WP_List_Table' ) ) {
	require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}

/**
 * Class for managing address book.
 */
class WC_MS_Admin_User_Addresses_List_Table extends WP_List_Table {
	/**
	 * User object.
	 *
	 * @var WP_User
	 */
	public $user;

	/**
	 * Create and instance of this list table.
	 *
	 * @param WP_User $user User object.
	 */
	public function __construct( $user ) {
		$this->user = $user;
		parent::__construct(
			array(
				'singular' => 'address',
				'plural'   => 'addresses',
				'ajax'     => false,
			)
		);
	}

	/**
	 * List of columns
	 *
	 * @return array
	 */
	public function get_columns() {
		return array(
			'address' => esc_html__( 'Address', 'woocommerce-shipping-multiple-addresses' ),
		);
	}

	/**
	 * List of sortable columns
	 *
	 * @return array
	 */
	public function get_sortable_columns() {
		return array();
	}

	/**
	 * Prepare address items.
	 */
	public function prepare_items() {
		global $wcms;

		$wcms->cart->load_cart_files();

		$columns = $this->get_columns();
		$hidden  = array();

		$sortable              = $this->get_sortable_columns();
		$this->_column_headers = array( $columns, $hidden, $sortable );
		$addresses             = $wcms->address_book->get_user_addresses( $this->user, false );
		$items                 = array();

		foreach ( $addresses as $index => $address ) {
			$items[] = array(
				'index'   => $index,
				'address' => $address,
			);
		}

		$this->items = $items;
	}

	/**
	 * Display address column.
	 *
	 * @param array $item Row item.
	 */
	public function column_address( $item ) {
		// No need to escape. It's already escaped on `wcms_get_formatted_address()`.
		$out = '<div class="address">' . wcms_get_formatted_address( $item['address'] ) . '</div>';// phpcs:ignore

		// Get actions.
		$delete_url = admin_url( 'admin-post.php?action=wcms_delete_address&index=' . $item['index'] . '&user_id=' . intval( $this->user->ID ) . '&_wpnonce=' . wp_create_nonce( 'delete_shipping_address' ) );
		$actions    = array(
			'edit'  => '<a class="edit-address" data-index="' . esc_attr( $item['index'] ) . '" title="' . esc_attr__( 'Edit', 'woocommerce-shipping-multiple-addresses' ) . '" href="#">' . esc_html__( 'Edit', 'woocommerce-shipping-multiple-addresses' ) . '</a>',
			'trash' => '<a class="submitdelete" title="' . esc_attr__( 'Delete', 'woocommerce-shipping-multiple-addresses' ) . '" href="' . esc_url( $delete_url ) . '">' . esc_html__( 'Delete', 'woocommerce-shipping-multiple-addresses' ) . '</a>',
		);

		$row_actions = array();

		foreach ( $actions as $action => $link ) {
			$row_actions[] = '<span class="' . esc_attr( $action ) . '">' . $link . '</span>';
		}

		$out .= '<div class="row-actions">' . implode( ' | ', $row_actions ) . '</div>';

		return $out;
	}

	/**
	 * Create a row in table.
	 *
	 * @param array $item Row item.
	 */
	public function single_row( $item ) {
		$address              = $item['address'];
		$default_address_keys = array_keys( WC()->countries->get_default_address_fields() );
		$fields               = WC()->countries->get_address_fields( $address['shipping_country'], 'wcms_shipping_' );
		?>
		<tr id="address-<?php echo esc_attr( $item['index'] ); ?>">
			<?php $this->single_row_columns( $item ); ?>
		</tr>
		<tr></tr>
		<tr style="display: none;" class="address-form" id="address-form-<?php echo esc_attr( $item['index'] ); ?>" data-index="<?php echo esc_attr( $item['index'] ); ?>">
			<td>
				<div class="address-column">
				<?php
				foreach ( $fields as $key => $field ) {
					$val         = ( isset( $address[ substr( $key, 5 ) ] ) ) ? $address[ substr( $key, 5 ) ] : '';
					$default_key = str_replace( array( 'wcms_shipping_', 'shipping_' ), '', $key );

					// Get the value for non default address fields.
					if ( empty( $val ) && isset( $address[ $key ] ) && ! in_array( $default_key, $default_address_keys, true ) ) {
						$val = $address[ $key ];
					}

					$val = ( empty( $val ) && ! empty( $_GET[ $key ] ) ) ? sanitize_text_field( wp_unslash( $_GET[ $key ] ) ) : $val; // phpcs:ignore WordPress.Security.NonceVerification.Recommended

					// No need to escape. `woocommerce_form_field()` has been escaped.
					echo woocommerce_form_field( $key, $field, $val ); //phpcs:ignore
				}
				?>
				</div>

				<p class="submit">
					<input type="button" class="button btn-cancel" value="<?php esc_html_e( 'Cancel', 'woocommerce-shipping-multiple-addresses' ); ?>" />
					<input type="button" class="button-primary btn-save" value="<?php esc_html_e( 'Save Address', 'woocommerce-shipping-multiple-addresses' ); ?>" />
				</p>
			</td>
		</tr>
		<?php
	}

	/**
	 * Removing table nav.
	 *
	 * @param string $which which side of the table.
	 */
	public function display_tablenav( $which ) {}
}
</file>

<file path="includes/class-wc-ms-admin.php">
<?php
/**
 * Class WC_MS_Admin file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class WC_MS_Admin.
 */
class WC_MS_Admin {
	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// Settings styles and scripts.
		add_action( 'admin_enqueue_scripts', array( $this, 'settings_scripts' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'edit_user_scripts' ) );

		add_filter( 'woocommerce_shipping_settings', array( $this, 'shipping_settings' ) );
		add_filter( 'woocommerce_account_settings', array( $this, 'account_settings' ) );

		add_action( 'admin_notices', array( $this, 'show_shipping_address_notices' ) );
		add_action( 'edit_user_profile', array( $this, 'add_customer_shipping_addresses_link' ), 21 );
		add_action( 'show_user_profile', array( $this, 'add_customer_shipping_addresses_link' ), 21 );

		// Delete address request.
		add_action( 'admin_post_wcms_delete_address', array( $this, 'delete_user_shipping_address' ) );
		add_action( 'wp_ajax_wcms_edit_user_address', array( $this, 'edit_user_shipping_address' ) );
	}

	/**
	 * Add scripts on WC settings page.
	 */
	public static function settings_scripts() {
		$screen = get_current_screen();

		if ( 'woocommerce_page_wc-settings' !== $screen->id ) {
			return;
		}

		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		wp_enqueue_script( 'wcms-product-search', plugins_url( 'assets/js/product-search' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_enqueue_script( 'wcms-admin', plugins_url( 'assets/js/admin' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_localize_script(
			'wcms-product-search',
			'wcms_product_search',
			array(
				'security'       => wp_create_nonce( 'search-products' ),
				'isLessThanWC27' => version_compare( WC_VERSION, '3.0', '<' ),
			)
		);
	}

	/**
	 * Add scripts on edit user page.
	 */
	public static function edit_user_scripts() {
		$screen = get_current_screen();

		if ( 'user-edit' !== $screen->id && 'profile' !== $screen->id ) {
			return;
		}

		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		wp_enqueue_script( 'wcms-country-select', plugins_url( 'assets/js/country-select' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_localize_script(
			'wcms-country-select',
			'wcms_country_select_params',
			/**
			 * Filter to manipulate the countries parameters on JS.
			 *
			 * @param array JSON encoded list of countries and select text.
			 *
			 * @since 3.3.19
			 */
			apply_filters(
				'wc_country_select_params',
				array(
					'countries'              => wp_json_encode( array_merge( WC()->countries->get_allowed_country_states(), WC()->countries->get_shipping_country_states() ) ),
					'i18n_select_state_text' => esc_attr__( 'Select an option&hellip;', 'woocommerce-shipping-multiple-addresses' ),
				)
			)
		);

		wp_enqueue_script( 'wcms-edit-user', plugins_url( 'assets/js/user-edit' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_localize_script(
			'wcms-edit-user',
			'wcms_edit_user_params',
			array(
				'save_nonce' => wp_create_nonce( 'wcms_edit_user_shipping_address_save' ),
			)
		);
	}

	/**
	 * Add shipping page settings.
	 *
	 * @param array $settings List of settings.
	 *
	 * @return array
	 */
	public function shipping_settings( $settings ) {
		$section_end    = array_pop( $settings );
		$shipping_table = array_pop( $settings );
		$settings[]     = array(
			'name'     => esc_html__( 'Multiple Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
			'desc'     => esc_html__( 'Page contents: [woocommerce_select_multiple_addresses] Parent: "Checkout"', 'woocommerce-shipping-multiple-addresses' ),
			'id'       => 'woocommerce_multiple_addresses_page_id',
			'type'     => 'single_select_page',
			'std'      => true,
			'class'    => 'chosen_select wc-enhanced-select',
			'css'      => 'min-width:300px;',
			'desc_tip' => false,
		);
		$settings[]     = $shipping_table;
		$settings[]     = $section_end;

		return $settings;
	}

	/**
	 * Add account settings.
	 *
	 * @param array $settings List of settings.
	 *
	 * @return array
	 */
	public function account_settings( $settings ) {
		foreach ( $settings as $idx => $setting ) {
			if ( 'sectionend' === $setting['type'] && 'account_page_options' === $setting['id'] ) {
				$front   = array_slice( $settings, 0, $idx );
				$front[] = array(
					'name'     => esc_html__( 'Account Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
					'desc'     => esc_html__( 'Page contents: [woocommerce_account_addresses] Parent: "My Account"', 'woocommerce-shipping-multiple-addresses' ),
					'id'       => 'woocommerce_account_addresses_page_id',
					'type'     => 'single_select_page',
					'std'      => true,
					'class'    => 'chosen_select wc-enhanced-select',
					'css'      => 'min-width:300px;',
					'desc_tip' => false,
				);
				array_splice( $settings, 0, $idx, $front );
				break;
			}
		}

		return $settings;
	}

	/**
	 * Show shipping address notice.
	 */
	public function show_shipping_address_notices() {
		if ( isset( $_GET['wcms_address_deleted'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
			echo '<div class="updated"><p>' . esc_html__( 'Shipping address deleted', 'woocommerce-shipping-multiple-addresses' ) . '</p></div>';
		}
	}

	/**
	 * Add all shipping addresses link on user page.
	 *
	 * @param WP_User $user User object.
	 */
	public function add_customer_shipping_addresses_link( $user ) {
		if ( ! current_user_can( 'manage_woocommerce' ) ) { // phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's a native WooCommerce capability
			return;
		}
		?>
		<h3><?php esc_html_e( 'Other Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></h3>

		<p>
			<a class="button view-addresses-table" href="#"><?php esc_html_e( 'View Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></a>
		</p>

		<div id="other_addresses_div" style="display: none;">
			<?php $this->render_user_addresses_table( $user ); ?>
		</div>
		<?php
	}

	/**
	 * Render a table for user addresses.
	 *
	 * @param WP_User $user User object.
	 */
	public function render_user_addresses_table( $user ) {
		require 'class-wc-ms-admin-user-addresses-list-table.php';

		$table = new WC_MS_Admin_User_Addresses_List_Table( $user );
		$table->prepare_items();
		$table->display();
	}

	/**
	 * Delete user shipping address.
	 */
	public function delete_user_shipping_address() {
		check_admin_referer( 'delete_shipping_address' );

		$user_id = isset( $_REQUEST['user_id'] ) ? intval( $_REQUEST['user_id'] ) : 0;
		$index   = isset( $_REQUEST['index'] ) ? intval( $_REQUEST['index'] ) : 0;

		$user      = new WP_User( $user_id );
		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		if ( isset( $addresses[ $index ] ) ) {
			unset( $addresses[ $index ] );
		}

		$this->wcms->address_book->save_user_addresses( $user_id, $addresses );

		// Redirect back to the profile page.
		wp_safe_redirect( admin_url( 'user-edit.php?user_id=' . $user_id . '&wcms_address_deleted=1' ) );
		exit;
	}

	/**
	 * Edit user shipping address.
	 */
	public function edit_user_shipping_address() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, 'wcms_edit_user_shipping_address_save' ) ) {
			die( esc_html__( 'Permission denied: Security check failed', 'woocommerce-shipping-multiple-addresses' ) );
		}

		// phpcs:ignore WordPress.WP.Capabilities.Unknown --- It's a native WooCommerce capability
		if ( ! current_user_can( 'manage_woocommerce' ) ) {
			die( esc_html__( 'Permission denied: Not enough capability', 'woocommerce-shipping-multiple-addresses' ) );
		}

		// No need to sanitize in this line. It will be sanitized after being parsed on line 220.
		$data    = isset( $_POST['data'] ) ? wp_unslash( $_POST['data'] ) : '';// phpcs:ignore
		$address = array();

		if ( empty( $data ) ) {
			die( esc_html__( 'No address data', 'woocommerce-shipping-multiple-addresses' ) );
		}

		parse_str( $data, $address );
		$address = wc_clean( $address );

		$index   = isset( $_POST['index'] ) ? sanitize_text_field( wp_unslash( $_POST['index'] ) ) : '';
		$user_id = isset( $_POST['user'] ) ? sanitize_text_field( wp_unslash( $_POST['user'] ) ) : '';
		$user    = get_user_by( 'ID', $user_id );

		if ( false === $user ) {
			die( esc_html__( 'User does not exists!', 'woocommerce-shipping-multiple-addresses' ) );
		}

		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		// store the same values without the shipping_ prefix.
		foreach ( $address as $key => $value ) {
			$key             = str_replace( 'wcms_', '', $key );
			$address[ $key ] = $value;
		}

		$addresses[ $index ] = $address;

		$this->wcms->address_book->save_user_addresses( $user_id, $addresses );

		// No need to escape. It's already been escaped by `wcms_get_formatted_address()`.
		die( wcms_get_formatted_address( $address ) ); //phpcs:ignore
	}
}
</file>

<file path="includes/class-wc-ms-cart.php">
<?php
/**
 * Class WC_MS_Cart file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Class WC_MS_Cart.
 */
class WC_MS_Cart {
	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// Duplicate cart POST.
		add_action( 'template_redirect', array( $this, 'duplicate_cart_post' ) );

		add_action( 'woocommerce_cart_totals_after_shipping', array( &$this, 'remove_shipping_calculator' ) );

		// WCMS Cart.
		add_action( 'woocommerce_cart_actions', array( $this, 'show_duplicate_cart_button' ) );

		// Cleanup.
		add_action( 'woocommerce_cart_emptied', array( $this->wcms, 'clear_session' ) );
		add_action( 'woocommerce_cart_updated', array( $this, 'cart_updated' ) );
	}

	/**
	 * Duplicating cart after the button is hit.
	 */
	public function duplicate_cart_post() {

		if ( isset( $_GET['duplicate-form'] ) && isset( $_GET['_wcmsnonce'] ) && wp_verify_nonce( sanitize_text_field( wp_unslash( $_GET['_wcmsnonce'] ) ), 'wcms-duplicate-cart' ) ) {
			$fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

			$user_addresses = $this->wcms->address_book->get_user_addresses( wp_get_current_user() );
			$address_ids    = array_keys( $user_addresses );

			$data = ( wcms_session_isset( 'cart_item_addresses' ) ) ? wcms_session_get( 'cart_item_addresses' ) : array();
			$rel  = ( wcms_session_isset( 'wcms_item_addresses' ) ) ? wcms_session_get( 'wcms_item_addresses' ) : array();

			$added      = $this->duplicate_cart();
			$address_id = array_shift( $address_ids );
			$address    = $user_addresses[ $address_id ];

			foreach ( $added as $item ) {
				$qtys       = $item['qty'];
				$product_id = $item['id'];
				$sig        = $item['key'] . '_' . $product_id . '_';

				$i = 1;
				for ( $y = 0; $y < $qtys; $y++ ) {
					$rel[ $address_id ][] = $item['key'];

					while ( isset( $data[ 'shipping_first_name_' . $sig . $i ] ) ) {
						++$i;
					}

					$_sig = $sig . $i;
					if ( $fields ) {
						foreach ( $fields as $key => $field ) {
							$data[ $key . '_' . $_sig ] = $address[ $key ];
						}
					}
				}

				$cart_address_ids_session = wcms_session_get( 'cart_address_ids' );

				if ( ! wcms_session_isset( 'cart_address_ids' ) || ! in_array( $sig, $cart_address_ids_session, true ) ) {
					$cart_address_sigs_session          = wcms_session_get( 'cart_address_sigs' );
					$cart_address_sigs_session[ $_sig ] = $address_id;
					wcms_session_set( 'cart_address_sigs', $cart_address_sigs_session );
				}
			}

			wcms_session_set( 'cart_item_addresses', $data );
			wcms_session_set( 'address_relationships', $rel );
			wcms_session_set( 'wcms_item_addresses', $rel );

			wp_safe_redirect( get_permalink( wc_get_page_id( 'multiple_addresses' ) ) );
			exit;
		}
	}

	/**
	 * Removes the shipping calculator on the cart page when we have multiple shipping addresses.
	 */
	public function remove_shipping_calculator() {

		if ( isset( WC()->session ) && isset( WC()->session->cart_item_addresses ) ) {
			$script = '
				jQuery( function( $ ) {
					$( ".woocommerce-shipping-calculator" ).remove();
					$( document.body ).on( "updated_cart_totals", function() {
						$( ".woocommerce-shipping-calculator" ).remove();
					} );
				} );
			';
			wc_enqueue_js( $script );
		}
	}

	/**
	 * Show duplicate cart button if it's enabled.
	 */
	public function show_duplicate_cart_button() {
		$ms_settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		if ( isset( $ms_settings['cart_duplication'] ) && 'no' !== $ms_settings['cart_duplication'] ) {
			$dupe_url = add_query_arg(
				array(
					'duplicate-form' => '1',
					'_wcmsnonce'     => wp_create_nonce( 'wcms-duplicate-cart' ),
				),
				get_permalink( wc_get_page_id( 'multiple_addresses' ) )
			);

			echo '<a class="button expand" href="' . esc_url( $dupe_url ) . '" >' . esc_html__( 'Duplicate Cart', 'woocommerce-shipping-multiple-addresses' ) . '</a>';
		}
	}

	/**
	 * Clean WCMS session when cart is updated.
	 */
	public function cart_updated() {
		if ( is_admin() ) {
			return;
		}

		$cart = WC()->cart->get_cart();

		if ( empty( $cart ) || ! $this->cart_is_eligible_for_multi_shipping() ) {
			wcms_session_delete( 'cart_item_addresses' );
			wcms_session_delete( 'cart_address_sigs' );
			wcms_session_delete( 'address_relationships' );
			wcms_session_delete( 'shipping_methods' );
			wcms_session_delete( 'wcms_original_cart' );
			wcms_session_delete( 'wcms_package_notes' );
			wcms_session_delete( 'wcms_delivery_dates' );
			wcms_session_delete( 'wcms_package_gifts' );
		}
	}

	/**
	 * Duplicating cart items.
	 *
	 * @param int $multiplier Duplication multiplier.
	 *
	 * @return array
	 */
	public function duplicate_cart( $multiplier = 1 ) {

		$this->load_cart_files();

		$cart         = WC()->cart;
		$current_cart = $cart->get_cart();
		$orig_cart    = array();

		if ( wcms_session_isset( 'wcms_original_cart' ) ) {
			$orig_cart = wcms_session_get( 'wcms_original_cart' );
		}

		if ( ! empty( $orig_cart ) ) {
			$contents = wcms_session_get( 'wcms_original_cart' );
		} else {
			$contents = $cart->get_cart();
			wcms_session_set( 'wcms_original_cart', $contents );
		}

		$added = array();
		foreach ( $contents as $cart_key => $content ) {
			$add_qty     = $content['quantity'] * $multiplier;
			$current_qty = ( isset( $current_cart[ $cart_key ] ) ) ? $current_cart[ $cart_key ]['quantity'] : 0;

			$cart->set_quantity( $cart_key, $current_qty + $add_qty );

			$added[] = array(
				'id'      => $content['product_id'],
				'qty'     => $add_qty,
				'key'     => $cart_key,
				'content' => $content,
			);
		}

		return $added;
	}

	/**
	 * Load WooCommerce Cart files.
	 */
	public function load_cart_files() {

		if ( file_exists( WC()->plugin_path() . '/classes/class-wc-cart.php' ) ) {
			require_once WC()->plugin_path() . '/classes/abstracts/abstract-wc-session.php';
			require_once WC()->plugin_path() . '/classes/class-wc-session-handler.php';
			require_once WC()->plugin_path() . '/classes/class-wc-cart.php';
			require_once WC()->plugin_path() . '/classes/class-wc-checkout.php';
			require_once WC()->plugin_path() . '/classes/class-wc-customer.php';
		} else {
			require_once WC()->plugin_path() . '/includes/abstracts/abstract-wc-session.php';
			require_once WC()->plugin_path() . '/includes/class-wc-session-handler.php';
			require_once WC()->plugin_path() . '/includes/class-wc-cart.php';
			require_once WC()->plugin_path() . '/includes/class-wc-checkout.php';
			require_once WC()->plugin_path() . '/includes/class-wc-customer.php';
		}

		if ( ! WC()->session ) {
			WC()->session = new WC_Session_Handler();
		}

		if ( ! WC()->customer ) {
			WC()->customer = new WC_Customer();
		}
	}

	/**
	 * Check if the contents of the current cart are valid for multiple shipping
	 *
	 * To pass, there must be 1 or more items in the cart that passes the @see WC_Cart::needs_shipping() test.
	 * If there is only 1 item in the cart, it must have a quantity of 2 or more. And child items
	 * from Bundles and Composite Products are excluded from the count.
	 *
	 * This method will automatically return false if the only available shipping method is Local Pickup
	 *
	 * @return bool
	 */
	public function cart_is_eligible_for_multi_shipping() {
		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		$has_item_address  = ( ! wcms_session_isset( 'cart_item_addresses' ) || empty( $sess_item_address ) ) ? false : true;
		$item_allowed      = false;
		$contents          = wcms_get_real_cart_items();

		if ( empty( $contents ) ) {
			/**
			 * Filter to manipulate the value whether cart is eligible for multi shipping or not.
			 *
			 * @param boolean Allow to have multi shipping or not.
			 *
			 * @since 3.3.16
			 */
			return apply_filters( 'wc_ms_cart_is_eligible', false );
		} elseif ( count( $contents ) > 1 ) {
			$item_allowed = true;
		} else {
			$content = current( $contents );
			if ( $content && $content['quantity'] > 1 ) {
				$item_allowed = true;
			}
		}

		// Do not allow to set multiple addresses if only local pickup is available.
		$available_methods = $this->wcms->get_available_shipping_methods();

		// phpcs:ignore WordPress.Security.NonceVerification.Missing
		$post_shipping_method = isset( $_POST['shipping_method'] ) ? sanitize_text_field( wp_unslash( $_POST['shipping_method'] ) ) : '';

		if ( 1 === count( $available_methods ) && ( isset( $available_methods['local_pickup'] ) || isset( $available_methods['local_pickup_plus'] ) ) ) {
			$item_allowed = false;
		} elseif ( ! empty( $post_shipping_method ) && ( 'local_pickup' === $post_shipping_method || 'local_pickup_plus' === $post_shipping_method ) ) {
			$item_allowed = false;
		}

		// Do not allow if any of the cart items is in the excludes list.
		$settings        = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$excl_products   = ( isset( $settings['excluded_products'] ) ) ? array_map( 'intval', $settings['excluded_products'] ) : array();
		$excl_categories = ( isset( $settings['excluded_categories'] ) ) ? array_map( 'intval', $settings['excluded_categories'] ) : array();

		if ( $excl_products || $excl_categories ) {

			foreach ( $contents as $cart_item ) {
				$product_id = intval( $cart_item['product_id'] );
				if ( in_array( $product_id, $excl_products, true ) ) {
					$item_allowed = false;
					break;
				}

				// Item categories.
				$cat_ids = wp_get_object_terms( $cart_item['product_id'], 'product_cat', array( 'fields' => 'ids' ) );

				foreach ( $cat_ids as $cat_id ) {
					$cat_id = intval( $cat_id );
					if ( in_array( $cat_id, $excl_categories, true ) ) {
						$item_allowed = false;
						break 2;
					}
				}
			}
		}

		/**
		 * Filter to manipulate the value whether cart is eligible for multi shipping or not.
		 *
		 * @param boolean Allow to have multi shipping or not.
		 *
		 * @since 3.3.16
		 */
		return apply_filters( 'wc_ms_cart_is_eligible', $item_allowed );
	}

	/**
	 * Check if multiple addresses have been set
	 *
	 * @return bool
	 */
	public function cart_has_multi_shipping() {
		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		/**
		 * Filter to manipulate the value whether cart has multi shipping or not.
		 *
		 * @param boolean Cart has multi shipping or not.
		 *
		 * @since 3.3.23
		 */
		return apply_filters( 'wc_ms_cart_has_multi_shipping', empty( $sess_item_address ) ? false : true );
	}
}
</file>

<file path="includes/class-wc-multiple-shipping-settings.php">
<?php
/**
 * Class WC_Multiple_Shipping_Settings file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_Multiple_Shipping_Settings class.
 *
 * @extends WC_Shipping_Method
 */
class WC_Multiple_Shipping_Settings extends WC_Shipping_Method {

	/**
	 * Is cart duplication enabled?
	 *
	 * @var string.
	 */
	public $cart_duplication;

	/**
	 * Checkout notification value.
	 *
	 * @var string.
	 */
	public $lang_notification;

	/**
	 * Text for multiple address button.
	 *
	 * @var string.
	 */
	public $lang_btn_items;

	/**
	 * Class constructor.
	 */
	public function __construct() {
		$this->id                 = 'multiple_shipping';
		$this->method_title       = __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' );
		$this->method_description = __( 'Multiple Shipping is used automatically by the WooCommerce Ship to Multiple Addresses.', 'woocommerce-shipping-multiple-addresses' );
		$this->init();
	}

	/**
	 * Initiate the action and filter.
	 */
	public function init() {
		// Load the form fields.
		$this->init_form_fields();

		// Load the settings.
		$this->init_settings();

		// Define user set variables.
		$this->enabled           = 'yes';
		$this->title             = $this->settings['title'];
		$this->cart_duplication  = $this->settings['cart_duplication'];
		$this->lang_notification = $this->settings['lang_notification'];
		$this->lang_btn_items    = $this->settings['lang_btn_items'];

		add_action( 'woocommerce_update_options_shipping_multiple_shipping', array( $this, 'process_admin_options' ) );
		add_filter( 'woocommerce_settings_api_sanitized_fields_' . $this->id, array( $this, 'save_settings' ) );
	}

	/**
	 * Do nothing when calculate shipping.
	 *
	 * @param array $package Current cart package.
	 */
	public function calculate_shipping( $package = array() ) {
		// Do nothing here.
	}

	/**
	 * Listing the form fields.
	 */
	public function init_form_fields() {
		$this->form_fields = array(
			'title'                    => array(
				'title'       => __( 'Title', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'text',
				'description' => __( 'This controls the title which the user sees during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'default'     => __( 'Multiple Shipping', 'woocommerce-shipping-multiple-addresses' ),
			),
			'cart_duplication_section' => array(
				'type'        => 'title',
				'title'       => __( 'Cart Duplication', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'This functionality will allow your customers to duplicate the contents of their cart in order to be able to ship the same cart to multiple addresses in addition to individual products.', 'woocommerce-shipping-multiple-addresses' ),
			),
			'cart_duplication'         => array(
				'title' => __( 'Enable Cart Duplication', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'checkbox',
				'label' => 'Enable',
			),
			'checkout_section'         => array(
				'type'  => 'title',
				'title' => __( 'Checkout Fields', 'woocommerce-shipping-multiple-addresses' ),
			),
			'checkout_notes'           => array(
				'type'        => 'checkbox',
				'title'       => __( 'Delivery Notes', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'default'     => 'yes',
				'description' => __( 'Allow customers to write delivery notes to every shipping address selected.', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'checkout_notes_limit'     => array(
				'type'        => 'text',
				'title'       => __( 'Limit Character Input', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Characters', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Limit the character input for delivery notes. Leave the box empty to disable the limit.', 'woocommerce-shipping-multiple-addresses' ),
				'css'         => 'width: 100px',
				'desc_tip'    => true,
			),
			'checkout_datepicker'      => array(
				'type'        => 'checkbox',
				'title'       => __( 'Date Picker', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Allow customers to pick delivery dates for every shipping address selected.', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'checkout_valid_days'      => array(
				'type'        => 'multiselect',
				'title'       => __( 'Valid Shipping Days', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Days', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Days that customers can select for the "Shipping Date" during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'options'     => array(
					0 => __( 'Sunday', 'woocommerce-shipping-multiple-addresses' ),
					1 => __( 'Monday', 'woocommerce-shipping-multiple-addresses' ),
					2 => __( 'Tuesday', 'woocommerce-shipping-multiple-addresses' ),
					3 => __( 'Wednesday', 'woocommerce-shipping-multiple-addresses' ),
					4 => __( 'Thursday', 'woocommerce-shipping-multiple-addresses' ),
					5 => __( 'Friday', 'woocommerce-shipping-multiple-addresses' ),
					6 => __( 'Saturday', 'woocommerce-shipping-multiple-addresses' ),
				),
				'default'     => array( 0, 1, 2, 3, 4, 5, 6 ),
				'class'       => 'show-if-checkout-datepicker wc-enhanced-select',
				'desc_tip'    => true,
			),
			'checkout_exclude_dates'   => array(
				'type'        => 'ms_multi_datepicker',
				'title'       => __( 'Excluded Delivery Dates', 'woocommerce-shipping-multiple-addresses' ),
				'label'       => __( 'Excluded Dates', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Specific dates that customers cannnot select for the "Shipping Date" during checkout.', 'woocommerce-shipping-multiple-addresses' ),
				'class'       => 'show-if-checkout-datepicker',
				'desc_tip'    => true,
			),
			'gift_section'             => array(
				'type'        => 'title',
				'title'       => __( 'Gift Packages', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Allow customers to mark certain shipping packages as gifts', 'woocommerce-shipping-multiple-addresses' ),
			),
			'gift_packages'            => array(
				'title' => __( 'Enable Gift Packages', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'checkbox',
				'label' => 'Enable',
			),
			'exclusions'               => array(
				'type'        => 'title',
				'title'       => __( 'Excluded Products &amp; Categories', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Do not allow multiple shipping addresses when any of the products and categories below are in the cart', 'woocommerce-shipping-multiple-addresses' ),
			),
			'excluded_products'        => array(
				'title' => __( 'Products', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'ms_product_select',
			),
			'excluded_categories'      => array(
				'title' => __( 'Categories', 'woocommerce-shipping-multiple-addresses' ),
				'type'  => 'ms_category_select',
			),
			'language_section'         => array(
				'type'  => 'title',
				'title' => __( 'Text your shoppers see when Multiple Shipping is enabled at checkout', 'woocommerce-shipping-multiple-addresses' ),
			),
			'lang_notification'        => array(
				'type'    => 'text',
				'title'   => __( 'Checkout Notification', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'You may use multiple shipping addresses on this cart', 'woocommerce-shipping-multiple-addresses' ),
			),
			'lang_btn_items'           => array(
				'type'    => 'text',
				'title'   => __( 'Button: Item Addresses', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'Set Multiple Addresses', 'woocommerce-shipping-multiple-addresses' ),
			),
			'partial_orders'           => array(
				'title'       => __( 'Partially Complete Orders', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'checkbox',
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Partially complete order by shipping address', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'email_section'            => array(
				'type'  => 'title',
				'title' => __( 'Partial Order Completed Email', 'woocommerce-shipping-multiple-addresses' ),
			),
			'partial_orders_email'     => array(
				'title'       => __( 'Send Email', 'woocommerce-shipping-multiple-addresses' ),
				'type'        => 'checkbox',
				'label'       => __( 'Enable', 'woocommerce-shipping-multiple-addresses' ),
				'description' => __( 'Send an email when an order has been marked as partially complete', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
			),
			'email_subject'            => array(
				'type'    => 'text',
				'title'   => __( 'Subject', 'woocommerce-shipping-multiple-addresses' ),
				'css'     => 'width: 350px;',
				'default' => __( 'Part of your order has been shipped', 'woocommerce-shipping-multiple-addresses' ),
			),
			'email_message'            => array(
				'type'        => 'ms_wp_editor',
				'description' => __( 'Leave empty to use the default email message', 'woocommerce-shipping-multiple-addresses' ),
				'desc_tip'    => true,
				'title'       => __( 'Message', 'woocommerce-shipping-multiple-addresses' ),
				'css'         => 'width: 350px;',
				'default'     => sprintf(
					// translators: %1$s is a blog name.
					__( '<p>Hi there. Part of your recent order on %1$s has been completed. Your order details are shown below for your reference:</p><h2>Order: {order_id}</h2><br />{products_table}<br />{addresses_table}', 'woocommerce-shipping-multiple-addresses' ),
					get_option( 'blogname' )
				),
			),
		);
	}

	/**
	 * Check if current package is available for multiple address.
	 *
	 * @param mixed $package Cart package.
	 * @return boolean
	 */
	public function is_available( $package ) {
		$packages = WC()->cart->get_shipping_packages();

		if ( ! empty( $packages ) && count( $packages ) > 1 ) {
			return true;
		}

		return false;
	}

	/**
	 * Generate WP editor field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_wp_editor_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$html     = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();
		$editor            = '';
		$content           = ( isset( $settings['email_message'] ) ) ? $settings['email_message'] : $data['default'];

		ob_start();
		wp_editor( $content, esc_attr( $this->plugin_id . $this->id . '_' . $key ) );
		$editor = ob_get_clean();

		$html     .= '<tr valign="top">' . "\n";
			$html .= '<th scope="row" class="titledesc">';
			$html .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

			$html .= '<br/><br/><div class="vars-box">
			<strong>Available Variables</strong><br/>
			<em>{order_id}</em><br/>
			<em>{order_date}</em><br/>
			<em>{order_time}</em><br/>
			<em>{products_table}</em><br/>
			<em>{addresses_table}</em>
			</div>';

			$html     .= '</th>' . "\n";
			$html     .= '<td class="forminp">' . "\n";
				$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";
				$html .= $editor;

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

			$html .= '</fieldset>';
			$html .= '</td>' . "\n";
		$html     .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate product field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_product_select_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$html = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();

		if ( ! isset( $settings['excluded_products'] ) ) {
			$settings['excluded_products'] = array();
		}

		$product_ids = array_filter( array_map( 'absint', $settings['excluded_products'] ) );

		$html .= '<tr valign="top">' . "\n";
		$html .= '<th scope="row" class="titledesc">';
		$html .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

		$html .= '</th>' . "\n";
		$html .= '<td class="forminp">' . "\n";
		$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";

		$html .= '<select
						multiple="multiple"
						id="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '"
						name="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '[]"
						class="wcms-product-search"
						data-placeholder="' . esc_attr__( 'Search for a product&hellip;', 'woocommerce-shipping-multiple-addresses' ) . '"
						style="width: 400px"
					>' . "\n";

		foreach ( $product_ids as $product_id ) {
			$product      = wc_get_product( $product_id );
			$product_name = $product ? htmlspecialchars( wp_kses_post( $product->get_formatted_name() ), ENT_COMPAT ) : '';

			$html .= '<option value="' . esc_attr( $product_id ) . '" selected="selected">' . esc_html( $product_name ) . '</option>' . "\n";
		}

		$html .= '</select>' . "\n";

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

		$html .= '</fieldset>';
		$html .= '</td>' . "\n";
		$html .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate category field HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_category_select_html( $key, $data ) {

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$html = '';

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		// Custom attribute handling.
		$custom_attributes = array();
		$categories        = get_terms(
			array(
				'taxonomy' => 'product_cat',
				'order_by' => 'name',
				'order'    => 'ASC',
			)
		);
		$html             .= '<tr valign="top">' . "\n";
			$html         .= '<th scope="row" class="titledesc">';
			$html         .= '<label for="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '">' . wp_kses_post( $data['title'] ) . '</label>';

		if ( $tip ) {
			$html .= '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . WC()->plugin_url() . '/assets/images/help.png" height="16" width="16" />';
		}

			$html     .= '</th>' . "\n";
			$html     .= '<td class="forminp">' . "\n";
				$html .= '<fieldset><legend class="screen-reader-text"><span>' . wp_kses_post( $data['title'] ) . '</span></legend>' . "\n";
				$html .= '<select multiple="multiple" id="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '" name="' . esc_attr( $this->plugin_id . $this->id . '_' . $key ) . '[]" class="chosen_select " multiple data-placeholder="' . __( 'Select categories&hellip;', 'woocommerce-shipping-multiple-addresses' ) . '" style="width: 400px">';

		foreach ( $categories as $category ) {
			$selected = ( isset( $settings['excluded_categories'] ) && is_array( $settings['excluded_categories'] ) && in_array( $category->term_id, array_map( 'intval', $settings['excluded_categories'] ), true ) ) ? 'selected' : '';
			$html    .= '<option value="' . $category->term_id . '" ' . $selected . '>' . esc_html( $category->name ) . '</option>';
		}
				$html .= '</select>';

		if ( $description ) {
			$html .= ' <p class="description">' . wp_kses_post( $description ) . '</p>' . "\n";
		}

			$html .= '</fieldset>';
			$html .= '</td>' . "\n";
		$html     .= '</tr>' . "\n";

		return $html;
	}

	/**
	 * Generate multi datepicker HTML.
	 *
	 * @param string $key Field key.
	 * @param array  $data Field data.
	 *
	 * @return string.
	 */
	public function generate_ms_multi_datepicker_html( $key, $data ) {
		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

		$data['title']       = isset( $data['title'] ) ? $data['title'] : '';
		$data['desc_tip']    = isset( $data['desc_tip'] ) ? $data['desc_tip'] : false;
		$data['description'] = isset( $data['description'] ) ? $data['description'] : '';

		// Description handling.
		if ( true === $data['desc_tip'] ) {
			$description = '';
			$tip         = $data['description'];
		} elseif ( ! empty( $data['desc_tip'] ) ) {
			$description = $data['description'];
			$tip         = $data['desc_tip'];
		} elseif ( ! empty( $data['description'] ) ) {
			$description = $data['description'];
			$tip         = '';
		} else {
			$description = '';
			$tip         = '';
		}

		ob_start();
		?>
		<tr valign="top">
			<th scope="row" class="titledesc">
				<label for="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>"><?php echo wp_kses_post( $data['title'] ); ?></label>

				<?php
				if ( $tip ) {
					echo '<img class="help_tip" data-tip="' . wc_sanitize_tooltip( $tip ) . '" src="' . esc_url( WC()->plugin_url() . '/assets/images/help.png' ) . '" height="16" width="16" />';
				}
				?>
			</th>
			<td class="forminp">
				<fieldset><legend class="screen-reader-text"><span><?php echo wp_kses_post( $data['title'] ); ?></span></legend>
					<?php
					$excludes = isset( $settings['checkout_exclude_dates'] ) ? $settings['checkout_exclude_dates'] : array();

					if ( ! $excludes ) {
						$excludes = array();
					}
					?>

					<div class="datepicker-div" style="float: left; width: 350px;"></div>

					<div style="float: left; width: 350px;">
						<div style="display: inline-block; width: 300px;">
							<select class="wc-enhanced-select excluded-list show-if-checkout-datepicker" id="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>" name="<?php echo esc_attr( $this->plugin_id . $this->id . '_' . $key ); ?>[]" multiple>
								<?php foreach ( $excludes as $date ) : ?>
									<option selected value="<?php echo esc_attr( $date ); ?>"><?php echo esc_html( $date ); ?></option>
								<?php endforeach; ?>
							</select>
						</div>
						<button class="button" type="button" id="show_excluded_dates_calendar"><span class="dashicons dashicons-calendar-alt" style="line-height: inherit;"></span></button>
						<button class="button" type="button" id="hide_excluded_dates_calendar" style="display: none;"><span class="dashicons dashicons-yes" style="line-height: inherit;"></span></button>
					</div>
				</fieldset>
			</td>
		</tr>
		<?php
		return ob_get_clean();
	}

	/**
	 * Save settings.
	 *
	 * @param array $settings Setting values.
	 *
	 * @return array.
	 */
	public function save_settings( $settings ) {
		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Nonce verification is handled in WC
		$settings['email_subject']       = isset( $_POST['woocommerce_multiple_shipping_email_subject'] ) ? sanitize_text_field( wp_unslash( $_POST['woocommerce_multiple_shipping_email_subject'] ) ) : '';
		$settings['email_message']       = isset( $_POST['woocommerce_multiple_shipping_email_message'] ) ? sanitize_textarea_field( wp_unslash( $_POST['woocommerce_multiple_shipping_email_message'] ) ) : '';
		$settings['excluded_categories'] = isset( $_POST['woocommerce_multiple_shipping_excluded_categories'] ) ? array_map( 'absint', $_POST['woocommerce_multiple_shipping_excluded_categories'] ) : array();

		$products = array();
		$key      = 'woocommerce_multiple_shipping_excluded_products';
		if ( ! empty( $_POST[ $key ] ) ) {
			$values = wc_clean( wp_unslash( $_POST[ $key ] ) );
			if ( ! empty( $values[0] ) && strpos( $values[0], ',' ) !== false ) {
				$products = explode( ',', $values[0] );
			} else {
				$products = array_map( 'absint', $values );
			}
		}
		$settings['excluded_products'] = $products;

		return $settings;
		// phpcs:enable WordPress.Security.NonceVerification.Missing
	}

	/**
	 * Validating the product field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_product_select_field( $key ) {
		$text = $this->get_option( $key );
		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$text = $new;
		}
		// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}

	/**
	 * Validating the category field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_category_select_field( $key ) {
		$text = $this->get_option( $key );

		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$text = $new;
		}
			// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}

	/**
	 * Validating the multi datepicker field.
	 *
	 * @param string $key Field key.
	 *
	 * @return string.
	 */
	public function validate_ms_multi_datepicker_field( $key ) {
		$text = $this->get_option( $key );

		// phpcs:disable WordPress.Security.NonceVerification.Missing --- Security handled in WC settings API
		if ( isset( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) && is_array( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) ) {
			$val = wc_clean( wp_unslash( $_POST[ $this->plugin_id . $this->id . '_' . $key ] ) );
			$new = array();

			foreach ( $val as $value ) {
				$new[] = wp_kses_post( trim( stripslashes( $value ) ) );
			}

			$new = array_unique( $new );

			$text = $new;
		} else {
			$text = array();
		}
		// phpcs:enable WordPress.Security.NonceVerification.Missing
		return $text;
	}
}
</file>

<file path="templates/my-account-addresses.php">
<?php
/**
 * Shipping addresses template file for my account page.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<div class="addresses">
	<header class="title">
		<h3><?php esc_html_e( 'Other Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
		<a href="<?php echo esc_url( add_query_arg( 'action', 'add', $form_url ) ); ?>" class="edit"><?php esc_html_e( 'Add Address', 'woocommerce-shipping-multiple-addresses' ); ?></a>
	</header>

	<?php
	if ( empty( $addresses ) ) {
		echo '<i>' . esc_html__( 'No shipping addresses set up yet.', 'woocommerce-shipping-multiple-addresses' ) . '</i> ';
		echo '<a href="' . esc_url( add_query_arg( 'action', 'add', $form_url ) ) . '">' . esc_html__( 'Set up shipping addresses', 'woocommerce-shipping-multiple-addresses' ) . '</a>';
	} else {
		foreach ( $addresses as $idx => $address ) {
			if ( 0 === $idx ) {
				// Skip the default address.
				continue;
			}

			wc_get_template(
				'address-block.php',
				array(
					'address' => $address,
					'idx'     => $idx,
				),
				'multi-shipping',
				dirname( WC_Ship_Multiple::FILE ) . '/templates/'
			);
		}
		echo '<div class="clear: both;"></div>';
	}
	?>
</div>
</file>

<file path=".github/workflows/build.yml">
name: Build
on:
  workflow_call:
    inputs:
      plugin_name:
        required: true
        type: string
        description: 'The name of the plugin (e.g. woocommerce-shipping-multiple-addresses)'
    outputs:
      plugin_name:
        description: "The name of the plugin"
        value: ${{ jobs.build.outputs.plugin_name }}

jobs:
  build:
    name: Build plugin artifact (zip file)
    runs-on: ubuntu-latest
    outputs:
      plugin_name: ${{ inputs.plugin_name }}
    env:
      PLUGIN_NAME: ${{ inputs.plugin_name }}
    steps:
      - name: Set Git to use HTTPS instead of SSH
        run: git config --global url.https://github.com/.insteadOf git://github.com/

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10

      - uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'

      - name: Install PNPM dependencies
        run: pnpm install --frozen-lockfile

      - name: Build plugin zip
        run: pnpm build

      - name: Upload plugin zip
        uses: actions/upload-artifact@v4
        with:
          name: ${{ env.PLUGIN_NAME }}
          path: ${{ env.PLUGIN_NAME }}.zip
</file>

<file path=".github/workflows/update-requires-headers.yml">
name: Update Requires Headers

on:
  schedule:
    - cron: '0 0 * * *' # Run every day at midnight UTC
  workflow_dispatch: # Allow manual trigger

env:
  PLUGIN_FILE_NAME: "woocommerce-shipping-multiple-addresses.php"
  MAIN_BRANCH: "trunk"

jobs:
  update-headers:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout Repository
        uses: actions/checkout@v3

      # Step 2: Fetch the latest WooCommerce release
      - name: Fetch Latest WooCommerce Release
        id: fetch_wc_release
        run: |
          # Download the latest WooCommerce release
          echo "Fetching the latest stable release..."
          LATEST_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases/latest | jq -r '.tag_name')
          echo "Stable release: $LATEST_RELEASE"

          echo "Fetching the latest RC release..."
          RC_RELEASE=$(curl -s https://api.github.com/repos/woocommerce/woocommerce/releases | jq -r '[.[] | select(.tag_name | ascii_downcase | contains("rc"))] | sort_by(.published_at) | reverse | .[0].tag_name')
          echo "RC release: $RC_RELEASE"

          # Default to stable release
          DOWNLOAD_TAG="$LATEST_RELEASE"

          if [ -n "$RC_RELEASE" ]; then
          # Remove any leading "v" to compare version numbers correctly
          STABLE_VERSION=$(echo "$LATEST_RELEASE" | sed 's/^v//')
          RC_VERSION=$(echo "$RC_RELEASE" | sed 's/^v//')

          # Compare versions using sort -V. The highest version will be last.
          HIGHEST=$(echo -e "$RC_VERSION\n$STABLE_VERSION" | sort -V | tail -n 1)
          if [ "$HIGHEST" = "$RC_VERSION" ] && [ "$RC_VERSION" != "$STABLE_VERSION" ]; then
          echo "RC release is newer than stable release."
          DOWNLOAD_TAG="$RC_RELEASE"
          else
          echo "Stable release is newer or equal to RC release."
          fi
          else
          echo "No RC release found."
          fi

          echo "Downloading release: $DOWNLOAD_TAG"
          curl -L -o woocommerce.zip "https://github.com/woocommerce/woocommerce/releases/download/$DOWNLOAD_TAG/woocommerce.zip"

          # Unzip WooCommerce release
          unzip -q woocommerce.zip

          # Extract minor version for WC requires at least
          LATEST_RELEASE=$(echo "$DOWNLOAD_TAG" | sed -E 's/^v?([0-9]+\.[0-9]+\.[0-9]+).*/\1/')
          MAJOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+')
          MINOR_VERSION=$(echo "$LATEST_RELEASE" | grep -oE '^[0-9]+\.[0-9]+' | cut -d. -f2)
          ADJUSTED_MINOR_VERSION=$((MINOR_VERSION - 2))

          if [ "$ADJUSTED_MINOR_VERSION" -lt 0 ]; then
            ADJUSTED_MAJOR_VERSION=$((MAJOR_VERSION - 1))
            ADJUSTED_MINOR_VERSION=$((ADJUSTED_MINOR_VERSION + 10))
          else
            ADJUSTED_MAJOR_VERSION=$MAJOR_VERSION
          fi

          LATEST_MINOR_RELEASE="$ADJUSTED_MAJOR_VERSION.$ADJUSTED_MINOR_VERSION"

          echo "Latest WooCommerce Release: $LATEST_RELEASE"
          echo "LATEST_RELEASE=$LATEST_RELEASE" >> $GITHUB_ENV
          echo "LATEST_MINOR_RELEASE=$LATEST_MINOR_RELEASE" >> $GITHUB_ENV

      # Step 3: Set Branch Name
      - name: Set Branch Name
        run: |
          BRANCH_NAME="tweak/update-requires-headers-${{ env.LATEST_RELEASE }}"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV

      # Step 4: Compare and Update Headers
      - name: Compare and Update Headers
        id: compare_and_update
        run: |
          WC_CORE_FILE="woocommerce/woocommerce.php"
          EXTENSION_FILE=${{ env.PLUGIN_FILE_NAME }}
          CHANGES_MADE=false

          # Get required versions from WooCommerce core
          REQUIRES_AT_LEAST=$(grep -oP 'Requires at least: \K[\d.]+' "$WC_CORE_FILE")
          REQUIRES_PHP=$(grep -oP 'Requires PHP: \K[\d.]+' "$WC_CORE_FILE")
          LATEST_MINOR_RELEASE=${{ env.LATEST_MINOR_RELEASE }}
          echo "REQUIRES_AT_LEAST=$REQUIRES_AT_LEAST" >> $GITHUB_ENV
          echo "REQUIRES_PHP=$REQUIRES_PHP" >> $GITHUB_ENV

          # -------------------------------
          # Compare and update 'Requires at least'
          # -------------------------------
          if grep -qE "^\s*\* Requires at least:" "$EXTENSION_FILE"; then
            current_req=$(grep -oP '^\s*\* Requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires at least' header: $current_req"
            if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
              echo "Updating 'Requires at least' header from $current_req to $REQUIRES_AT_LEAST"
              sed -i "s#^\s*\* Requires at least:.*# * Requires at least: $REQUIRES_AT_LEAST#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
            fi
          else
            echo "Adding missing 'Requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires at least: $REQUIRES_AT_LEAST" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'Requires PHP'
          # -------------------------------
          if grep -qE "^\s*\* Requires PHP:" "$EXTENSION_FILE"; then
            current_php=$(grep -oP '^\s*\* Requires PHP:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'Requires PHP' header: $current_php"
            if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
              echo "Updating 'Requires PHP' header from $current_php to $REQUIRES_PHP"
              sed -i "s#^\s*\* Requires PHP:.*# * Requires PHP: $REQUIRES_PHP#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
            fi
          else
            echo "Adding missing 'Requires PHP' header."
            sed -i "/^\s*\* Version:/a \\ \\* Requires PHP: $REQUIRES_PHP" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          # -------------------------------
          # Compare and update 'WC requires at least'
          # -------------------------------
          if grep -qE "^\s*\* WC requires at least:" "$EXTENSION_FILE"; then
            current_wc=$(grep -oP '^\s*\* WC requires at least:\s*\K[\d.]+' "$EXTENSION_FILE")
            echo "Current 'WC requires at least' header: $current_wc"
            if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
              echo "Updating 'WC requires at least' header from $current_wc to $LATEST_MINOR_RELEASE"
              sed -i "s#^\s*\* WC requires at least:.*# * WC requires at least: $LATEST_MINOR_RELEASE#" "$EXTENSION_FILE"
              CHANGES_MADE=true
            else
              echo "Current 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
            fi
          else
            echo "Adding missing 'WC requires at least' header."
            sed -i "/^\s*\* Version:/a \\ \\* WC requires at least: $LATEST_MINOR_RELEASE" "$EXTENSION_FILE"
            CHANGES_MADE=true
          fi

          echo "EDITED_FILES=${{ env.PLUGIN_FILE_NAME }}" >> $GITHUB_ENV
          echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV

      - name: Compare and Update Headers in README File
        run: |
          README_FILE="readme.txt"
          CHANGES_MADE=${{ env.CHANGES_MADE }}

          if [ -f "$README_FILE" ]; then
            echo "Found $README_FILE. Updating headers..."

            # -------------------------------
            # Compare and update 'Requires at least'
            # -------------------------------
            if grep -qE "^Requires at least:" "$README_FILE"; then
              current_req=$(grep -oP '^Requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires at least' in README: $current_req"
              if [ "$(printf "%s\n%s" "$current_req" "$REQUIRES_AT_LEAST" | sort -V | head -n1)" = "$current_req" ] && [ "$current_req" != "$REQUIRES_AT_LEAST" ]; then
                echo "Updating 'Requires at least' header to $REQUIRES_AT_LEAST"
                sed -i "s#^Requires at least:.*#Requires at least: $REQUIRES_AT_LEAST#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires at least' ($current_req) is greater than or equal to required ($REQUIRES_AT_LEAST); not updating."
              fi
            else
              echo "Adding missing 'Requires at least' header."
              sed -i "/^Stable tag:/i Requires at least: $REQUIRES_AT_LEAST" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'Requires PHP'
            # -------------------------------
            if grep -qE "^Requires PHP:" "$README_FILE"; then
              current_php=$(grep -oP '^Requires PHP:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'Requires PHP' in README: $current_php"
              if [ "$(printf "%s\n%s" "$current_php" "$REQUIRES_PHP" | sort -V | head -n1)" = "$current_php" ] && [ "$current_php" != "$REQUIRES_PHP" ]; then
                echo "Updating 'Requires PHP' header to $REQUIRES_PHP"
                sed -i "s#^Requires PHP:.*#Requires PHP: $REQUIRES_PHP#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'Requires PHP' ($current_php) is greater than or equal to required ($REQUIRES_PHP); not updating."
              fi
            else
              echo "Adding missing 'Requires PHP' header."
              sed -i "/^Stable tag:/i Requires PHP: $REQUIRES_PHP" "$README_FILE"
              CHANGES_MADE=true
            fi

            # -------------------------------
            # Compare and update 'WC requires at least'
            # -------------------------------
            if grep -qE "^WC requires at least:" "$README_FILE"; then
              current_wc=$(grep -oP '^WC requires at least:\s*\K[\d.]+' "$README_FILE")
              echo "Current 'WC requires at least' in README: $current_wc"
              if [ "$(printf "%s\n%s" "$current_wc" "$LATEST_MINOR_RELEASE" | sort -V | head -n1)" = "$current_wc" ] && [ "$current_wc" != "$LATEST_MINOR_RELEASE" ]; then
                echo "Updating 'WC requires at least' header to $LATEST_MINOR_RELEASE"
                sed -i "s#^WC requires at least:.*#WC requires at least: $LATEST_MINOR_RELEASE#" "$README_FILE"
                CHANGES_MADE=true
              else
                echo "Existing 'WC requires at least' ($current_wc) is greater than or equal to required ($LATEST_MINOR_RELEASE); not updating."
              fi
            else
              echo "Adding missing 'WC requires at least' header."
              sed -i "/^Stable tag:/i WC requires at least: $LATEST_MINOR_RELEASE" "$README_FILE"
              CHANGES_MADE=true
            fi

            echo "CHANGES_MADE=$CHANGES_MADE" >> $GITHUB_ENV
            echo "FILES_TO_CHECK=${{ env.PLUGIN_FILE_NAME }},readme.txt" >> $GITHUB_ENV
          else
            echo "$README_FILE not found."
          fi

      # Step 5: Ignore Unwanted Files
      - name: Ignore Unwanted Files
        run: |
          echo "woocommerce/" >> .gitignore
          echo "woocommerce.zip" >> .gitignore
          git clean -fdx

      # Step 5: Create and Push Branch
      - name: Create and Push Branch
        if: env.CHANGES_MADE == 'true'
        run: |
          # Check if branch already exists remotely
          if git ls-remote --exit-code --heads origin ${{ env.BRANCH_NAME }}; then
            echo "Branch ${{ env.BRANCH_NAME }} already exists remotely. Skipping creation."
            exit 0
          fi

          # Configure Git user
          git config user.name github-actions
          git config user.email <EMAIL>

          # Create and checkout the branch
          git checkout -b "${{ env.BRANCH_NAME }}"

          # Commit only the modified files
          git add ${{ env.PLUGIN_FILE_NAME }}
          if [ -f "readme.txt" ]; then
            git add readme.txt
          fi
          git commit -m "Update Requires headers"

          # Push the branch
          git push --set-upstream origin ${{ env.BRANCH_NAME }}

      # Step 6: Create Pull Request
      - name: Create Pull Request
        if: env.CHANGES_MADE == 'true'
        run: |
          # Check if there are commits between base and head branch
          COMMIT_COUNT=$(git rev-list --count ${{ env.MAIN_BRANCH }}..${{ env.BRANCH_NAME }} 2>/dev/null || echo "0")

          if [ "$COMMIT_COUNT" -eq 0 ]; then
            echo "No commits found between ${{ env.MAIN_BRANCH }} and ${{ env.BRANCH_NAME }}. Skipping PR creation."
            exit 0
          fi

          # Check if PR already exists for this branch
          if gh pr list --head ${{ env.BRANCH_NAME }} --json number --jq length | grep -q "^0$"; then
            echo "Creating new PR for branch ${{ env.BRANCH_NAME }}"
            gh pr create \
              --title "Update Requires headers for WooCommerce compatibility" \
              --body "This PR updates plugin headers to ensure compatibility with the latest WooCommerce release.

            Please test the plugin and merge." \
              --base ${{ env.MAIN_BRANCH }} \
              --head ${{ env.BRANCH_NAME }} \
              --reviewer dustinparker,iyut,bartech,abdalsalaam
          else
            echo "PR already exists for branch ${{ env.BRANCH_NAME }}. Skipping PR creation."
          fi
        env:
          GH_TOKEN: ${{ github.token }}
</file>

<file path="includes/class-wc-ms-gifts.php">
<?php
/**
 * Class WC_MS_Gifts file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class WC_MS_Gifts.
 */
class WC_MS_Gifts {

	/**
	 * Main class instance.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Main class instance.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {

		$this->wcms = $wcms;

		add_action( 'wc_ms_shipping_package_block', array( __CLASS__, 'render_gift_form' ), 10, 1 );

		add_action( 'woocommerce_checkout_update_order_meta', array( __CLASS__, 'store_order_gift_data' ), 20, 2 );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( __CLASS__, 'store_order_gift_data' ), 20 );

		add_action( 'woocommerce_before_checkout_shipping_form', array( __CLASS__, 'shipping_address_gift_form' ) );
		add_action( 'woocommerce_checkout_update_order_meta', array( __CLASS__, 'store_shipping_address_gift_data' ) );
		add_action( 'woocommerce_admin_order_data_after_shipping_address', array( __CLASS__, 'render_order_shipping_gift_data' ) );

		// Modify the packages, shipping methods and addresses in the session.
		add_filter( 'wc_ms_checkout_session_packages', array( __CLASS__, 'apply_gift_data_to_packages' ), 30 );

		add_action( 'wc_ms_order_package_block_before_address', array( __CLASS__, 'render_gift_data' ), 10, 3 );
	}

	/**
	 * Returns TRUE if the Gift Packages setting is enabled
	 *
	 * @return bool
	 */
	public static function is_enabled() {
		global $wcms;

		if ( ! isset( $wcms->gateway_settings['gift_packages'] ) || 'yes' !== $wcms->gateway_settings['gift_packages'] ) {
			return false;
		}

		return true;
	}

	/**
	 * Show the gift checkbox on the shipping packages blocks.
	 *
	 * @param string $loop Array key.
	 *
	 * @return void
	 */
	public static function render_gift_form( $loop ) {
		if ( ! self::is_enabled() ) {
			return;
		}

		?>
		<div class="gift-form">
			<p>
				<label>
					<input type="checkbox" class="chk-gift" name="shipping_gift[<?php echo esc_attr( $loop ); ?>]" value="yes" data-index="<?php echo esc_attr( $loop ); ?>" />
					<?php esc_html_e( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
			</p>
		</div>

		<?php
	}

	/**
	 * Modify the 'wcms_packages' session data to attach gift data from POST
	 * and at the same time, populate the WC_Gift_Checkout::gifts array
	 *
	 * @param array $packages Shipping packages to modify.
	 *
	 * @return array
	 */
	public static function apply_gift_data_to_packages( $packages ) {
		// No need to use nonce verification. it has been verified on `WC_Checkout::process_checkout()`.
		$shipping_gift = isset( $_POST['shipping_gift'] ) ? wc_clean( $_POST['shipping_gift'] ) : array(); // phpcs:ignore
		if ( empty( $shipping_gift ) ) {
			return $packages;
		}

		foreach ( $shipping_gift as $idx => $value ) {

			if ( 'yes' !== $value ) {
				continue;
			}

			if ( ! isset( $packages[ $idx ] ) ) {
				continue;
			}

			$packages[ $idx ]['gift'] = true;

		}

		return $packages;
	}

	/**
	 * Store gift data.
	 *
	 * @param int|WC_Order $new_order Either order ID or order object.
	 *
	 * @return void
	 */
	public static function store_order_gift_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		// No need for nonce verification. It has been verified on `WC_Checkout::process_checkout()`.
		$shipping_gift = isset( $_POST['shipping_gift'] ) ? wc_clean( $_POST['shipping_gift'] ) : array(); //phpcs:ignore
		$packages      = $order->get_meta( '_wcms_packages' );

		if ( ! empty( $shipping_gift ) && is_array( $shipping_gift ) ) {
			foreach ( $shipping_gift as $idx => $value ) {

				if ( 'yes' !== $value || ! array_key_exists( $idx, $packages ) ) {
					continue;
				}

				$order->update_meta_data( '_gift_' . $idx, true );
			}
		} elseif ( empty( $shipping_gift ) && is_array( $packages ) ) {
			foreach ( $packages as $idx => $package ) {
				if ( isset( $package['gift'] ) && ( 'yes' === $package['gift'] || true === $package['gift'] ) ) {
					$order->update_meta_data( '_gift_' . $idx, true );
				}
			}
		}

		$order->save();
	}

	/**
	 * Render the 'This is a Gift' option in the shipping address form
	 */
	public static function shipping_address_gift_form() {
		if ( ! self::is_enabled() ) {
			return;
		}
		?>
		<div class="gift-form">
			<p>
				<label>
					<input type="checkbox" class="chk-gift" name="checkout_shipping_gift" value="yes" />
					<?php esc_html_e( 'This is a gift', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
			</p>
		</div>
		<?php
	}

	/**
	 * Mark an order as a gift.
	 *
	 * @param int|WC_Order $new_order Either order ID or order object.
	 *
	 * @return void.
	 */
	public static function store_shipping_address_gift_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		// No need for nonce verification. It has been verified on `WC_Checkout::process_checkout()`.
        if ( ! empty( $_POST['checkout_shipping_gift'] ) ) { //phpcs:ignore
			$order->update_meta_data( '_gift', true );
			$order->save();
		}
	}

	/**
	 * Render gift data for shipping address.
	 *
	 * @param \WC_Order $order Order object.
	 *
	 * @return void
	 */
	public static function render_order_shipping_gift_data( $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$is_gift = $order->get_meta( '_gift' );

		if ( $is_gift ) {
			echo '<p><span class="dashicons dashicons-megaphone"></span> <strong>This is a gift</strong></p>';
		}
	}

	/**
	 * Render gift data.
	 *
	 * @param \WC_Order $order Order object.
	 * @param array     $package Package to check.
	 * @param string    $package_index Package index.
	 *
	 * @return void.
	 */
	public static function render_gift_data( $order, $package, $package_index ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages      = $order->get_meta( '_wcms_packages' );
		$order_is_gift = wc_string_to_bool( $order->get_meta( '_gift_' . $package_index ) );

		if ( $order_is_gift && is_array( $packages ) && 1 === count( $packages ) ) {
			/**
			 * Inject the gift data into the only package
			 * because multishipping doesn't process gift
			 * data when there's only one package
			 */
			$package['gift'] = true;
		}

		if ( isset( $package['gift'] ) && true === $package['gift'] ) {
			?>
			<div class="gift-package">
				<h5><div class="dashicons dashicons-yes"></div><?php esc_html_e( 'This is a Gift', 'woocommerce-shipping-multiple-addresses' ); ?></h5>
			</div>
			<?php

		}
	}
}
</file>

<file path="includes/class-wc-ms-notes.php">
<?php
/**
 * Class WC_MS_Notes file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_MS_Notes class.
 */
class WC_MS_Notes {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_filter( 'wc_ms_checkout_locale', array( __CLASS__, 'add_datepicker_variables' ) );
		add_action( 'wp_footer', array( __CLASS__, 'checkout_scripts' ) );
		add_action( 'wc_ms_shipping_package_block', array( __CLASS__, 'render_note_form' ), 10, 2 );

		add_action( 'woocommerce_checkout_update_order_meta', array( __CLASS__, 'store_order_notes' ), 20 );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( __CLASS__, 'store_order_notes' ) );

		// Modify the packages, shipping methods and addresses in the session.
		add_filter( 'wc_ms_checkout_session_packages', array( __CLASS__, 'apply_notes_to_packages' ), 30 );

		add_action( 'wc_ms_order_package_block_before_address', array( __CLASS__, 'render_notes' ), 11, 2 );
		add_action( 'wc_ms_order_package_block_before_address', array( __CLASS__, 'render_dates' ), 11, 2 );
	}

	/**
	 * Add datepicker settings to the WCMS JS array
	 *
	 * @param array $wcms_js WCMS JS variable.
	 *
	 * @return array
	 */
	public static function add_datepicker_variables( $wcms_js ) {
		global $wcms;
		$settings = $wcms->gateway_settings;

		$show_datepicker = ( ! empty( $settings['checkout_datepicker'] ) && 'yes' === $settings['checkout_datepicker'] ) ? true : false;

		// Set to enable all days by default.
		$wcms_js['datepicker_valid_days'] = array( 0, 1, 2, 3, 4, 5, 6 );

		if ( $show_datepicker && ! empty( $settings['checkout_valid_days'] ) ) {
			$wcms_js['datepicker_valid_days'] = array_map( 'absint', $settings['checkout_valid_days'] );
		}

		// Set excluded dates.
		$wcms_js['datepicker_excluded_dates'] = array();
		if ( $show_datepicker && ! empty( $settings['checkout_exclude_dates'] ) ) {
			$wcms_js['datepicker_excluded_dates'] = $settings['checkout_exclude_dates'];
		}

		return $wcms_js;
	}

	/**
	 * Store the notes in the checkout form immediately after they are entered
	 */
	public static function checkout_scripts() {

		if ( ! is_checkout() ) {
			return;
		}

		?>
		<script>
			jQuery(document).ready(function($) {

				$("form.checkout").on("keypress", "textarea.ms_shipping_note", function(e) {
					var val     = $(this).val(),
						length  = val.length,
						limit   = $(this).data("limit"),
						remain = parseInt(limit - length);

					if ( limit > 0 ) {
						if (remain <= 0 && e.which !== 0 && e.charCode !== 0) {
							$(this).val((val).substring(0, length - 1))
						}
					}

				});

				if ( supports_html5_storage() ) {

					var apply_notes = function() {
						$("textarea.ms_shipping_note").each(function() {
							var hash = $( this ).data( 'hash' );
							var note_hash = localStorage["ms_note_" + hash ];
							$(this).val(note_hash);
						});
					};

					$("div.woocommerce").on("change", "textarea.ms_shipping_note", function() {
						var note  = $(this).val();

						var hash = $( this ).data( 'hash' );
						localStorage["ms_note_"+ hash] = note;
					});

					$("body").on("updated_checkout", function() {
						apply_notes();
					})

					$("form.checkout").on("submit", function() {
						$("textarea.ms_shipping_note").each(function() {
							var hash = $( this ).data( 'hash' );
							localStorage.removeItem("ms_note_" + hash);
						});
					});
				}
			});
		</script>
		<?php
	}

	/**
	 * Show the note checkbox on the shipping packages blocks.
	 *
	 * @param int   $loop Loop index.
	 * @param array $package Cart package.
	 */
	public static function render_note_form( $loop, $package ) {
		global $wcms;

		$package_hash = md5( wp_json_encode( $package ) );

		if ( ! isset( $wcms->gateway_settings['checkout_notes'] ) ) {
			$wcms->gateway_settings['checkout_notes'] = 'yes';
		}

		$show_notes      = ( ! empty( $wcms->gateway_settings['checkout_notes'] ) && 'yes' === $wcms->gateway_settings['checkout_notes'] ) ? true : false;
		$show_datepicker = ( ! empty( $wcms->gateway_settings['checkout_datepicker'] ) && 'yes' === $wcms->gateway_settings['checkout_datepicker'] ) ? true : false;

		if ( $show_datepicker ) :
			$value    = '';
			$postdata = array();
			// No need to verify nonce. It's already verified.
            if ( !empty( $_POST['post_data'] ) ) { // phpcs:ignore
                parse_str( $_POST['post_data'], $postdata ); //phpcs:ignore
				$postdata = wc_clean( $postdata );
			}

			if ( isset( $postdata['shipping_date'] ) && isset( $postdata['shipping_date'][ $loop ] ) ) {
				$value = $postdata['shipping_date'][ $loop ];
			}
			?>
		<div class="datepicker-form">
			<p>
				<label>
					<?php esc_html_e( 'Shipping Date', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
				<input type="text" class="datepicker ms_shipping_date" name="shipping_date[<?php echo esc_attr( $loop ); ?>]" data-index="<?php echo esc_attr( $loop ); ?>" value="<?php echo esc_attr( $value ); ?>" />
			</p>
		</div>
			<?php
		endif;

		if ( $show_notes ) :
			$limit = ! empty( $wcms->gateway_settings['checkout_notes_limit'] ) ? absint( $wcms->gateway_settings['checkout_notes_limit'] ) : 0;
			?>
		<div class="note-form">
			<p>
				<label>
					<?php esc_html_e( 'Note', 'woocommerce-shipping-multiple-addresses' ); ?>
				</label>
				<textarea name="shipping_note[<?php echo esc_attr( $loop ); ?>]" rows="2" cols="30" class="ms_shipping_note" data-index="<?php echo esc_attr( $loop ); ?>" data-hash="<?php echo esc_attr( $package_hash ); ?>" data-limit="<?php echo esc_attr( $limit ); ?>"></textarea>
				<?php if ( ! empty( $limit ) ) : ?>
					<small><em>
						<?php
						// translators: %d is character limit.
						printf( esc_html__( 'Character Limit: %d', 'woocommerce-shipping-multiple-addresses' ), esc_html( $limit ) );
						?>
					</em></small>
				<?php endif; ?>
			</p>
		</div>
			<?php
		endif;
	}

	/**
	 * Modify the 'wcms_packages' session data to attach notes from POST.
	 *
	 * @param array $packages Cart packages.
	 *
	 * @return array
	 */
	public static function apply_notes_to_packages( $packages ) {
		// No need to verify. It's already been verified.
		$post_note = isset( $_POST['shipping_note'] ) ? wc_clean( $_POST['shipping_note'] ) : array(); //phpcs:ignore
		$post_date = isset( $_POST['shipping_date'] ) ? wc_clean( $_POST['shipping_date'] ) : array(); //phpcs:ignore

		if ( ! empty( $post_note ) && is_array( $post_note ) ) {
			foreach ( $post_note as $idx => $value ) {

				if ( ! isset( $packages[ $idx ] ) ) {
					continue;
				}

				$packages[ $idx ]['note'] = esc_html( $value );

			}
		}

		if ( ! empty( $post_date ) && is_array( $post_date ) ) {
			foreach ( $post_date as $idx => $value ) {

				if ( ! isset( $packages[ $idx ] ) ) {
					continue;
				}

				$ts = strtotime( $value );

				if ( $ts ) {
					$packages[ $idx ]['date'] = gmdate( get_option( 'date_format' ), $ts );
				} else {
					$packages[ $idx ]['date'] = esc_html( $value );
				}
			}
		}

		return $packages;
	}

	/**
	 * Store multiple shipping notes on order.
	 *
	 * @param int|WC_Order $new_order Either Order ID or Order object.
	 */
	public static function store_order_notes( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		// No need to verify nonce. It's already verified.
		$packages  = $order->get_meta( '_wcms_packages' );
		$post_note = isset( $_POST['shipping_note'] ) ? wc_clean( $_POST['shipping_note'] ) : array(); //phpcs:ignore
		$post_date = isset( $_POST['shipping_date'] ) ? wc_clean( $_POST['shipping_date'] ) : array(); //phpcs:ignore

		if ( ! empty( $post_note ) && is_array( $post_note ) && is_array( $packages ) ) {
			foreach ( $post_note as $idx => $value ) {
				if ( ! array_key_exists( $idx, $packages ) ) {
					continue;
				}

				$order->update_meta_data( '_note_' . $idx, $value );
			}
		} elseif ( empty( $post_note ) && is_array( $packages ) ) {
			foreach ( $packages as $idx => $package ) {
				if ( ! empty( $package['note'] ) ) {
					$order->update_meta_data( '_note_' . $idx, $package['note'] );
				}
			}
		}

		if ( ! empty( $post_date ) && is_array( $post_date ) && is_array( $packages ) ) {
			foreach ( $post_date as $idx => $value ) {
				if ( ! array_key_exists( $idx, $packages ) ) {
					continue;
				}

				$order->update_meta_data( '_date_' . $idx, $value );
			}
		} elseif ( empty( $post_date ) && is_array( $packages ) ) {
			foreach ( $packages as $idx => $package ) {
				if ( ! empty( $package['date'] ) ) {
					$order->update_meta_data( '_date_' . $idx, $package['date'] );
				}
			}
		}

		$order->save();
	}

	/**
	 * Render notes on multiple shipping address.
	 *
	 * @param WC_Order $order Order object.
	 * @param array    $package Cart package.
	 */
	public static function render_notes( $order, $package ) {
		$allowed_html = array(
			'a'      => array(
				'href'  => array(),
				'title' => array(),
			),
			'br'     => array(),
			'em'     => array(),
			'strong' => array(),
		);

		if ( isset( $package['note'] ) && ! empty( $package['note'] ) ) {
			?>
			<ul class="order_notes">
				<li class="note">
					<div class="note_content">
						<?php echo wp_kses( nl2br( $package['note'] ), $allowed_html ); ?>
					</div>
				</li>
			</ul>
			<?php
		}
	}

	/**
	 * Render dates on multiple shipping address.
	 *
	 * @param WC_Order $order Order object.
	 * @param array    $package Cart package.
	 */
	public static function render_dates( $order, $package ) {

		if ( isset( $package['date'] ) && ! empty( $package['date'] ) ) {
			?>
			<ul class="order_notes">
				<li class="note">
					<div class="note_content">
						<?php
						// translators: %s is a date.
						printf( esc_html__( 'Shipping Date: %s', 'woocommerce-shipping-multiple-addresses' ), esc_html( $package['date'] ) );
						?>
					</div>
				</li>
			</ul>
			<?php
		}
	}
}
</file>

<file path="includes/class-wc-ms-order-shipment.php">
<?php
/**
 * Class WC_MS_Order_Shipment file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\Utilities\OrderUtil;

/**
 * WC_MS_Order_Shipment class.
 */
class WC_MS_Order_Shipment {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_action( 'woocommerce_order_status_changed', array( $this, 'inherit_order_status' ), 1, 3 );
		add_filter( 'woocommerce_can_reduce_order_stock', array( $this, 'prevent_order_to_update_stock' ), 10, 2 );
		add_filter( 'woocommerce_can_restore_order_stock', array( $this, 'prevent_order_to_update_stock' ), 10, 2 );
		foreach ( array(
			'woocommerce_email_enabled_new_order',
			'woocommerce_email_enabled_failed_order',
			'woocommerce_email_enabled_cancelled_order',
			'woocommerce_email_enabled_customer_completed_order',
			'woocommerce_email_enabled_customer_invoice',
			'woocommerce_email_enabled_customer_note',
			'woocommerce_email_enabled_customer_on_hold_order',
			'woocommerce_email_enabled_customer_processing_order',
			'woocommerce_email_enabled_customer_refunded_order',
		) as $hook_name ) {
			add_filter( $hook_name, array( $this, 'order_shipment_not_send_email' ), 10, 2 );
		}
	}

	/**
	 * Child order should not reducing the product stock.
	 * This method is to prevent the order to reducing the stock.
	 *
	 * @param boolean  $can_reduce Flag whether can reduce the stock or not.
	 * @param WC_Order $order Order object.
	 *
	 * @return boolean
	 */
	public function prevent_order_to_update_stock( $can_reduce, $order ) {
		if ( 'Multi-Shipping' === $order->get_created_via() && 'order_shipment' === $order->get_type() && $order->get_parent_id() > 0 ) {
			return false;
		}

		return $can_reduce;
	}

	/**
	 * Creates a single shipment.
	 *
	 * @param array $package WCMS package.
	 * @param int   $package_index Package index.
	 * @param int   $order_id Order ID.
	 *
	 * @return int|mixed|WP_Error
	 * @throws Exception When unable to create a product in the shipment.
	 */
	public function create_from_package( $package, $package_index, $order_id ) {
		global $wpdb;

		/**
		 * Give plugins the opportunity to create the shipment themselves.
		 *
		 * @param int                  Shipment ID / Child order ID.
		 * @param WC_MS_Order_Shipment WCMS Order shipment object.
		 *
		 * @since 3.3
		 */
		$shipment_id = apply_filters( 'wc_ms_create_shipment', null, $this );
		if ( ! empty( $shipment_id ) ) {
			return $shipment_id;
		}

		try {
			$order = wc_get_order( $order_id );

			// Start transaction if available.
			$wpdb->query( 'START TRANSACTION' );

			$customer_notes        = array();
			$package_note          = $order->get_meta( '_note_' . $package_index );
			$package_delivery_date = $order->get_meta( '_date_' . $package_index );

			if ( ! empty( $order->get_customer_note() ) ) {
				$customer_notes[] = $order->get_customer_note();
			}

			if ( ! empty( $package_note ) ) {
				$customer_notes[] = __( 'Note', 'woocommerce-shipping-multiple-addresses' ) . ': ' . $package_note;
			}

			if ( ! empty( $package_delivery_date ) ) {
				$customer_notes[] = __( 'Shipping Date', 'woocommerce-shipping-multiple-addresses' ) . ': ' . $package_delivery_date;
			}

			$shipment_data = array(
				'parent'        => $order_id,
				'customer_id'   => $order->get_user_id(),
				'order_index'   => $package_index,
				'customer_note' => implode( '<br/>', $customer_notes ),
				'created_via'   => 'Multi-Shipping',
			);

			$shipment_id = $this->create_shipment( $shipment_data );
			$shipment    = wc_get_order( $shipment_id );

			if ( is_wp_error( $shipment_id ) ) {
				return $shipment_id;
			} else {
				/**
				 * Allow other plugins to add action on new shipment process.
				 *
				 * @param int $shipment_id Child order ID.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_ms_new_shipment', $shipment_id );
			}

			// Store the line items.
			foreach ( $package['contents'] as $item_key => $values ) {
				$item_id = $shipment->add_product(
					$values['data'],
					$values['quantity'],
					array(
						'variation' => $values['variation'],
						'totals'    => array(
							'subtotal'     => $values['line_subtotal'],
							'subtotal_tax' => $values['line_subtotal_tax'],
							'total'        => $values['line_total'],
							'tax'          => $values['line_tax'],
							'tax_data'     => $values['line_tax_data'], // Since 2.2.
						),
					)
				);

				if ( ! $item_id ) {
					// translators: %d is an error code.
					throw new Exception( sprintf( __( 'Error %d: Unable to create shipment. Please try again.', 'woocommerce-shipping-multiple-addresses' ), 402 ) );
				}

				/**
				 * Allow other plugins to add order item meta.
				 *
				 * @param string $item_id Order item ID.
				 * @param string $values Package content values.
				 * @param string $item_key Order item key.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_ms_add_shipment_item_meta', $item_id, $values, $item_key );

				/**
				 * Allow other plugins to add order item meta to shipping.
				 *
				 * @param string $item_id Order item ID.
				 * @param string $values Package content values.
				 * @param string $item_key Order item key.
				 *
				 * @since 3.3
				 */
				do_action( 'woocommerce_add_order_item_meta', $item_id, $values, $item_key );
			}

			// Store shipping for all packages.
			$rates              = $order->get_meta( '_shipping_rates' );
			$shipping_methods   = $order->get_meta( '_shipping_methods' );
			$shipping_total     = 0;
			$shipping_tax_total = 0;

			if ( isset( $rates[ $package_index ][ $shipping_methods[ $package_index ]['id'] ] ) ) {
				$rate_id       = $shipping_methods[ $package_index ]['id'];
				$rate          = $rates[ $package_index ];
				$shipping_rate = $rate[ $rate_id ];

				$shipping_item = new WC_Order_Item_Shipping();

				$shipping_item->set_props(
					array(
						'method_title' => $shipping_rate->label,
						'method_id'    => $shipping_rate->id,
						'total'        => wc_format_decimal( $shipping_rate->cost ),
						'taxes'        => $shipping_rate->taxes,
						'order_id'     => $shipment->get_id(),
					)
				);

				foreach ( $shipping_rate->get_meta_data() as $key => $value ) {
					$shipping_item->add_meta_data( $key, $value, true );
				}

				$item_id = $shipping_item->save();

				$shipment->add_item( $shipping_item );

				$shipping_total     = $shipping_rate->cost;
				$shipping_tax_total = array_sum( $shipping_rate->taxes );

				if ( ! $item_id ) {
					// translators: %d is error code.
					throw new Exception( sprintf( __( 'Error %d: Unable to create shipment. Please try again.', 'woocommerce-shipping-multiple-addresses' ), 404 ) );
				}

				/**
				 * Allow other plugins to add order item meta to shipping.
				 *
				 * @param int    $shipment_id Child order ID.
				 * @param string $item_id Order item ID.
				 * @param int    $package_index.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_ms_add_shipping_shipment_item', $shipment_id, $item_id, $package_index );
			}

			// Store tax rows.
			$taxes     = array();
			$tax_total = 0;
			foreach ( $package['contents'] as $line_item ) {
				if ( ! empty( $line_item['line_tax_data']['total'] ) ) {
					foreach ( $line_item['line_tax_data']['total'] as $tax_rate_id => $tax_amount ) {
						if ( ! isset( $taxes[ $tax_rate_id ] ) ) {
							$taxes[ $tax_rate_id ] = 0;
						}

						$taxes[ $tax_rate_id ] += $tax_amount;
						$tax_total             += $tax_amount;
					}
				}
			}

			foreach ( $taxes as $tax_rate_id => $amount ) {
				$item = new WC_Order_Item_Tax();
				$item->set_props(
					array(
						'rate_id'            => $tax_rate_id,
						'tax_total'          => $amount,
						'shipping_tax_total' => 0,
					)
				);
				$item->set_rate( $tax_rate_id );
				$item->set_order_id( $shipment->get_id() );
				$shipment_tax_id = $item->save();

				if ( $shipment_tax_id ) {
					$shipment->add_item( $item );
				}

				/**
				 * Allow other plugins to modify zero rate ID.
				 *
				 * @param string Rate ID.
				 *
				 * @since 3.3
				 */
				if ( $tax_rate_id && ! $shipment_tax_id && apply_filters( 'woocommerce_cart_remove_taxes_zero_rate_id', 'zero-rated' ) !== $tax_rate_id ) {
					// translators: %d is error code.
					throw new Exception( sprintf( __( 'Error %d: Unable to create shipment. Please try again.', 'woocommerce-shipping-multiple-addresses' ), 405 ) );
				}
			}

			/**
			 * Calculate total.
			 * Allow plugins to modify calculated total.
			 *
			 * @param float Calculated total.
			 * @param WC_Order $shipment Child order object.
			 * @param array    $package Cart package.
			 *
			 * @since 3.3
			 */
			$shipment_total = max( 0, apply_filters( 'wc_ms_shipment_calculated_total', round( $package['contents_cost'] + $tax_total + $shipping_tax_total + $shipping_total, 2 ), $shipment, $package ) );

			// Billing address.
			$billing_address = $order->get_address( 'billing' );
			$shipment->set_address( $billing_address, 'billing' );

			// Shipping address.
			$shipping_address = array(
				'first_name' => '',
				'last_name'  => '',
				'company'    => '',
				'address_1'  => '',
				'address_2'  => '',
				'city'       => '',
				'state'      => '',
				'country'    => '',
				'postcode'   => '',
			);

			foreach ( $package['destination'] as $field => $value ) {
				$shipping_address[ $field ] = $value;
			}

			$shipment->set_shipping_first_name( $shipping_address['first_name'] );
			$shipment->set_shipping_last_name( $shipping_address['last_name'] );
			$shipment->set_shipping_company( $shipping_address['company'] );
			$shipment->set_shipping_address_1( $shipping_address['address_1'] );
			$shipment->set_shipping_address_2( $shipping_address['address_2'] );
			$shipment->set_shipping_city( $shipping_address['city'] );
			$shipment->set_shipping_state( $shipping_address['state'] );
			$shipment->set_shipping_country( $shipping_address['country'] );
			$shipment->set_shipping_postcode( $shipping_address['postcode'] );

			$shipment->set_payment_method( $order->get_payment_method() );

			$shipment->set_shipping_total( $shipping_total );
			$shipment->set_cart_tax( $tax_total );
			$shipment->set_shipping_tax( $shipping_tax_total );

			$shipment->set_total( $shipment_total );

			/**
			 * Allow plugins to add a shipment meta.
			 *
			 * @param WC_Order $shipment Child order object.
			 * @param WC_Order $order Parent order object.
			 * @param array    $package Cart package.
			 * @param int      $package_index Package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_checkout_update_shipment_meta', $shipment, $order, $package, $package_index );

			if ( WC_MS_Gifts::is_enabled() ) {
				$order_meta_gift = intval( $order->get_meta( '_gift_' . $package_index ) );
				if ( 1 === $order_meta_gift ) {
					$shipment->update_meta_data( '_gift', true );
				}
			}

			$shipment->save();

			// If we got here, the order was created without problems!
			$wpdb->query( 'COMMIT' );

		} catch ( Exception $e ) {
			// There was an error adding order data!
			$wpdb->query( 'ROLLBACK' );
			return new WP_Error( 'shipment-error', $e->getMessage() );
		}

		return $shipment_id;
	}

	/**
	 * Create a new Order Shipment
	 *
	 * @param array $args Arguments to create an order shipment.
	 *
	 * @return int|WP_Error
	 */
	public function create_shipment( $args ) {
		$default_args = array(
			/**
			 * Allow plugins to modify default status for shipment creation.
			 *
			 * @param string Post status.
			 *
			 * @since 3.3
			 */
			'status'        => apply_filters( 'wc_ms_default_shipment_status', 'pending' ),
			'customer_id'   => null,
			'customer_note' => null,
			'order_index'   => 0,
			'created_via'   => '',
			'parent'        => 0,
		);

		$args = wp_parse_args( $args, $default_args );

		if ( empty( $args['parent'] ) ) {
			return new WP_Error( 'create_shipment', __( 'Cannot create a shipment without an Order ID', 'woocommerce-shipping-multiple-addresses' ) );
		}

		// Since WooCommerce implementing the HPOS, they want the order key to be unique for each order.
		// And because we're using the same order_key from parent order, we might need to add the order index so
		// it remains unique for each child order.
		$parent_order       = wc_get_order( $args['parent'] );
		$shipment_order_key = str_replace( 'wc_order_', 'wcms_order_shipment_' . $args['order_index'] . '_', $parent_order->get_order_key() );

		$shipment_data = array(
			'post_status'   => $args['status'],
			'post_password' => $shipment_order_key,
			'post_parent'   => $args['parent'],
		);

		if ( ! in_array( 'wc-' . $args['status'], array_keys( wc_get_order_statuses() ), true ) ) {
			return new WP_Error( 'woocommerce_invalid_order_status', __( 'Invalid shipment status', 'woocommerce-shipping-multiple-addresses' ) );
		}

		if ( ! is_null( $args['customer_note'] ) ) {
			$shipment_data['post_excerpt'] = $args['customer_note'];
		}

		/**
		 * Allow plugins to modify/add shipment data.
		 *
		 * @param array Order shipment data.
		 *
		 * @since 3.3
		 */
		$shipment_data = apply_filters( 'wc_ms_new_shipment_data', $shipment_data );

		try {
			$shipment = new WC_MS_Order_Type_Order_Shipment();
			$shipment->set_parent_id( $shipment_data['post_parent'] );
			$shipment->set_status( $shipment_data['post_status'] );
			$shipment->set_created_via( sanitize_text_field( $args['created_via'] ) );
			$shipment->set_order_key( $shipment_data['post_password'] );

			/**
			 * Allow plugins to modify shipment key.
			 *
			 * @param string shipment key unique ID.
			 *
			 * @since 3.3
			 */
			$shipment->update_meta_data( '_shipment_key', 'wc_' . apply_filters( 'wc_ms_generate_shipment_key', uniqid( 'shipment_' ) ) );
			$shipment->set_customer_note( $shipment_data['post_excerpt'] );
			$shipment->set_customer_id( $args['customer_id'] );

			/**
			 * Allow plugins to add action before saving the shipment.
			 *
			 * @param WC_MS_Order_Type_Order_Shipment Order shipment object.
			 *
			 * @since 4.0.0
			 */
			do_action( 'wc_ms_new_shipment_before_save', $shipment );

			$shipment_id = $shipment->save();

			$shipment->add_order_note( 'Shipment for Order ' . $parent_order->get_order_number() );

			return $shipment_id;

		} catch ( Exception $e ) {
			return new WP_Error( 'error', $e->getMessage() );
		}
	}

	/**
	 * Manipulate sending new order email.
	 *
	 * @param bool     $is_enabled Flag for email being enabled or not.
	 * @param WC_Order $order Order object.
	 */
	public function order_shipment_not_send_email( $is_enabled, $order ) {
		if ( $order instanceof WC_MS_Order_Type_Order_Shipment ) {
			return false;
		}

		return $is_enabled;
	}

	/**
	 * Update shipment order post to inherit order status.
	 *
	 * @param int    $order_id ID of the order post.
	 * @param string $old_status Old status of the order.
	 * @param string $new_status New status of the order.
	 */
	public function inherit_order_status( $order_id, $old_status, $new_status ) {
		if ( 'shop_order' !== OrderUtil::get_order_type( $order_id ) ) {
			return;
		}

		$shipment_ids = self::get_by_order( $order_id );

		foreach ( $shipment_ids as $shipment_id ) {
			$shipment = wc_get_order( $shipment_id );

			if ( false === $shipment ) {
				continue;
			}
			$shipment->update_status( $new_status );
		}
	}

	/**
	 * Get order shipment IDs by order ID.
	 *
	 * @param int $order_id ID of the order object.
	 *
	 * @return int[]
	 */
	public static function get_by_order( $order_id ) {
		return wc_get_orders(
			array(
				'type'   => 'order_shipment',
				'parent' => $order_id,
				'limit'  => -1,
				'return' => 'ids',
			)
		);
	}

	/**
	 * Get order shipments by order ID.
	 *
	 * @param int $order_id ID of the order object.
	 *
	 * @return WC_MS_Order_Type_Order_Shipment[]
	 */
	public static function get_shipment_objects_by_order( $order_id ) {
		return wc_get_orders(
			array(
				'type'   => 'order_shipment',
				'parent' => $order_id,
				'limit'  => -1,
			)
		);
	}
}
</file>

<file path="includes/class-wc-ms-order.php">
<?php
/**
 * Class WC_MS_Order file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * WC_MS_Order class.
 */
class WC_MS_Order {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// Update package status.
		add_action( 'wp_ajax_wcms_update_package_status', array( $this, 'update_package_status' ) );
		add_action( 'woocommerce_order_status_completed', array( $this, 'update_package_on_completed_order' ) );

		// Order preview parameter override.
		add_filter( 'woocommerce_admin_order_preview_get_order_details', array( $this, 'update_preview_order_details' ), 20, 2 );

		// Order page shipping address override.
		add_action( 'woocommerce_admin_order_data_after_shipping_address', array( $this, 'override_order_shipping_address' ) );

		// Compatibility action for displaying order shipping packages.
		add_action( 'wcms_order_shipping_packages_table', array( $this, 'display_order_shipping_addresses' ), 10, 3 );

		add_action( 'manage_shop_order_posts_custom_column', array( $this, 'show_multiple_addresses_line' ), 1, 2 );
		add_action( 'manage_woocommerce_page_wc-orders_custom_column', array( $this, 'show_multiple_addresses_line' ), 1, 2 );

		// meta box.
		add_action( 'add_meta_boxes', array( $this, 'order_meta_box' ), 10, 2 );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_css' ) );
		add_action( 'woocommerce_process_shop_order_meta', array( $this, 'update_order_addresses' ), 10, 1 );
		add_action( 'woocommerce_saved_order_items', array( $this, 'update_order_taxes' ), 1, 2 );

		add_filter( 'woocommerce_order_get_items', array( $this, 'order_item_taxes' ), 30, 2 );

		// Hide metadata in order line items.
		add_filter( 'woocommerce_hidden_order_itemmeta', array( $this, 'hidden_order_item_meta' ) );

		// WC PIP.
		add_filter( 'woocommerce_pip_template_body', array( $this, 'pip_template_body' ), 10, 2 );

		add_filter( 'woocommerce_order_needs_shipping_address', array( $this, 'manipulate_needs_shipping' ), 10, 3 );
	}

	/**
	 * Update package status.
	 *
	 * @return void
	 */
	public function update_package_status() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, 'wcms_update_package_status_save' ) ) {
			die( esc_html__( 'Permission denied: Security check failed', 'woocommerce-shipping-multiple-addresses' ) );
		}

		if ( ! current_user_can( 'manage_woocommerce' ) ) { // phpcs:ignore -- WooCommerce capability.
			exit;
		}

		$pkg_idx  = isset( $_POST['package'] ) ? sanitize_text_field( wp_unslash( $_POST['package'] ) ) : '';
		$order_id = isset( $_POST['order'] ) ? intval( $_POST['order'] ) : 0;
		$order    = wc_get_order( $order_id );
		$status   = '';
		if ( $order instanceof WC_Order ) {
			$post_status = isset( $_POST['status'] ) ? sanitize_text_field( wp_unslash( $_POST['status'] ) ) : '';
			$packages    = $order->get_meta( '_wcms_packages' );
			$email       = isset( $_POST['email'] ) && ( 'true' === $_POST['email'] || true === $_POST['email'] );

			foreach ( $packages as $x => $package ) {
				if ( '' !== $pkg_idx && intval( $pkg_idx ) === $x ) {
					$packages[ $x ]['status'] = $post_status;

					if ( 'Completed' === $post_status && $email ) {
						self::send_package_email( $order, $pkg_idx );
					}

					break;
				}
			}

			$order->update_meta_data( '_wcms_packages', $packages );
			$order->save();

			$status = $post_status;
		}

		die( esc_html( $status ) );
	}

	/**
	 * Update package status on completed order.
	 *
	 * @param int $order_id Order ID.
	 * @return void
	 */
	public function update_package_on_completed_order( $order_id ) {
		$order = wc_get_order( $order_id );

		if ( ! $order ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( $packages ) {
				foreach ( $packages as $x => $package ) {
					$packages[ $x ]['status'] = 'Completed';
				}

				$order->update_meta_data( '_wcms_packages', $packages );
				$order->save();
			}
		}
	}

	/**
	 * Manipulate the order details value if it has multiple addresses.
	 *
	 * @param array    $order_details Order details.
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function update_preview_order_details( $order_details, $order ) {
		if ( is_callable( array( $order, 'get_meta' ) ) ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( is_array( $packages ) && 1 < count( $packages ) ) {
				$order_details['formatted_shipping_address'] = $this->generate_formatted_multiple_addresses( $order );
			}
		}

		return $order_details;
	}

	/**
	 * Generate address map url for package destination.
	 *
	 * @param array    $package Package.
	 * @param WC_Order $order   Order object.
	 *
	 * @return string
	 */
	public static function generate_address_map_url( $package, $order ) {

		$address_map_url = '';

		if ( isset( $package['destination'] ) && is_array( $package['destination'] ) ) {
			$destination        = $package['destination'];
			$destination_pieces = implode(
				', ',
				array_filter(
					array(
						$destination['address_1'],
						$destination['address_2'],
						$destination['city'],
						$destination['state'],
						$destination['postcode'],
						$destination['country'],
					)
				)
			);

			/**
			 * Allow modifying shipping address map URL.
			 *
			 * @param string $url Map URL.
			 * @param WC_Order $order Order object.
			 * @return string Map URL.
			 *
			 * @since 3.6.29
			 */
			$address_map_url = apply_filters( 'woocommerce_shipping_address_map_url', 'https://maps.google.com/maps?&q=' . rawurlencode( $destination_pieces ) . '&z=16', $order );
		}

		return $address_map_url;
	}

	/**
	 * Generate formatted shipping address for multiple addresses.
	 *
	 * @param WC_Order $order   Order object.
	 *
	 * @return string
	 */
	public function generate_formatted_multiple_addresses( $order ) {

		if ( ! $order || ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return;
		}

		$item_addresses_html = '';

		ob_start();
		?>

		<div class="item-addresses-holder">
			<?php foreach ( $packages as $x => $package ) { ?>

				<?php $address_map_url = self::generate_address_map_url( $package, $order ); ?>

				<div class="item-address-box package-<?php echo esc_attr( $x ); ?>-box" style="margin-bottom:20px;">
					<a href="<?php echo esc_url( $address_map_url ); ?>" target="_blank">
						<?php self::display_shipping_package_address( $order, $package, $x, false ); ?>
					</a>
				</div>

			<?php } ?>
		</div>

		<?php
		$item_addresses_html .= ob_get_contents();
		ob_end_clean();

		return $item_addresses_html;
	}

	/**
	 * Order page shipping address override.
	 *
	 * @param \WC_Order $order Order object.
	 * @return void
	 */
	public function override_order_shipping_address( $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		$packages  = $order->get_meta( '_wcms_packages' );
		$multiship = $order->get_meta( '_multiple_shipping' );

		if ( ( ! $order->get_formatted_shipping_address() && ( is_array( $packages ) && count( $packages ) > 1 ) ) || 'yes' === $multiship ) :
			?>
			<script type="text/javascript">
				jQuery( document ).ready( function( $ ) {
					var $order_data = $( 'div.order_data_column' ).eq( 2 );
					$order_data.find( 'a.edit_address' ).remove();
					$order_data.find( 'div.address' ).prepend( '<a href="#wc_multiple_shipping"><?php esc_html_e( 'Ships to multiple addresses', 'woocommerce-shipping-multiple-addresses' ); ?></a>' );
					$order_data.find( 'div.address p' ).not( '.order_note' ).remove();
				} );
			</script>
			<?php
		endif;
	}

	/**
	 * Get shipping method name for the current package.
	 *
	 * @param int   $idx Index of the package.
	 * @param array $methods Array of shipping method.
	 * @param array $available_methods Array of available method.
	 *
	 * @return string
	 */
	public function get_shipping_method_name( $idx, $methods, $available_methods ) {
		$method = $methods[ $idx ]['label'];

		foreach ( $available_methods as $ship_method ) {
			if ( $ship_method->get_method_id() . ':' . $ship_method->get_instance_id() === $methods[ $idx ]['id']
				|| $ship_method->get_method_id() === $methods[ $idx ]['id']
			) {
				$method = $ship_method->get_name();
				break;
			}
		}

		return $method;
	}

	/**
	 * Get shipping address data from cart package.
	 *
	 * @param array $package Array of information about the package.
	 *
	 * @return string
	 */
	public function get_package_shipping_address( $package ) {
		return ! empty( $package['destination'] ) ? WC()->countries->get_formatted_address( $package['destination'] ) : '';
	}

	/**
	 * Get products out of the cart package.
	 *
	 * @param array    $products Array of products.
	 * @param WC_Order $order Order object.
	 *
	 * @return array
	 */
	public function get_package_products( $products, $order ) {

		$package_items  = $order->get_meta( '_packages_item_ids' );
		$order_items    = $order->get_items();
		$cart_item_keys = $this->get_cart_item_keys( $order );

		$product_infos = array();

		foreach ( $products as $i => $product ) {

			// Get a matching order item.
			$item = false;
			if ( ! empty( $product['cart_key'] ) && ! empty( $cart_item_keys[ $product['cart_key'] ] ) && isset( $order_items[ $cart_item_keys[ $product['cart_key'] ] ] ) ) {
				$item = $order_items[ $cart_item_keys[ $product['cart_key'] ] ];
			} elseif ( ! empty( $package_items[ $i ] ) && isset( $order_items[ $package_items[ $i ] ] ) ) {
				// Fallback for items stored before WC 3.0.
				$item = $order_items[ $package_items[ $i ] ];
			}

			// Get item name and meta.
			if ( empty( $item ) ) {
				$id = empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'];

				/**
				 * Allow plugins to modify product title.
				 *
				 * @param string     Product title.
				 * @param WC_Product Product object.
				 *
				 * @since 3.3
				 */
				$name = apply_filters( 'wcms_product_title', get_the_title( $id ), $product );

				/**
				 * Allow plugins to modify package item meta.
				 *
				 * @param mix        Item meta.
				 * @param WC_Product Product object.
				 *
				 * @since 3.3
				 */
				$meta = apply_filters( 'wcms_package_item_meta', self::get_item_meta( $product ), $product );
			} else {
				$name = is_callable( array( $item, 'get_name' ) ) ? $item->get_name() : get_the_title( empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'] );

				/**
				 * Allow plugins to modify product title.
				 *
				 * @param string     Product title.
				 * @param WC_Product Product object.
				 * @param string     Item meta HTML.
				 *
				 * @since 3.3
				 */
				$name = apply_filters( 'wcms_product_title', $name, $product, $item );
				$meta = wc_display_item_meta( $item, array( 'echo' => false ) );

				/**
				 * Allow plugins to modify package item meta.
				 *
				 * @param mix        Item meta.
				 * @param WC_Product Product object.
				 * @param string     Item meta HTML.
				 *
				 * @since 3.3
				 */
				$meta = apply_filters( 'wcms_package_item_meta', $meta, $product, $item );
			}

			$product_infos[] = array(
				'name'    => $name,
				'qty'     => $product['quantity'],
				'meta'    => $meta,
				'product' => $product,
			);
		}

		return $product_infos;
	}

	/**
	 * Get all the order items (and generate a unique key for each)
	 *
	 * @param WC_Order $order WC Order object.
	 *
	 * @return array
	 */
	public function get_cart_item_keys( $order ) {
		$order_items    = $order->get_items();
		$cart_item_keys = array();
		foreach ( $order_items as $item_id => $item ) {
			if ( ! empty( $item['wcms_cart_key'] ) ) {
				$cart_item_keys[ $item['wcms_cart_key'] ] = $item_id;
			}
		}

		return $cart_item_keys;
	}

	/**
	 * Display shipping address in email.
	 *
	 * @param WC_Order $order Current order.
	 * @param bool     $email Flag to check whether the display is for email or not.
	 * @param bool     $plain_text Flag to check if the email is using plain text or not.
	 *
	 * @return void
	 */
	public function display_order_shipping_addresses( $order, $email = false, $plain_text = false ) {
		$allowed_html = array(
			'a'      => array(
				'href'  => array(),
				'title' => array(),
			),
			'br'     => array(),
			'em'     => array(),
			'strong' => array(),
		);

		if ( $order instanceof WC_Order ) {
			$order_id = $order->get_id();
		} else {
			$order_id = $order;
			$order    = wc_get_order( $order );
		}

		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return;
		}

		/**
		 * Allow other plugins to unlist the order item addresses.
		 *
		 * @param boolean Flag to unlist the order item addresses. Set `false` to unlist.
		 * @param int     Order ID.
		 *
		 * @since 3.3
		 */
		if ( false === apply_filters( 'wcms_list_order_item_addresses', true, $order_id ) ) {
			return;
		}

		$methods           = $order->get_meta( '_shipping_methods' );
		$packages          = $order->get_meta( '_wcms_packages' );
		$available_methods = $order->get_shipping_methods();

		if ( empty( $packages ) || ! is_array( $packages ) || 1 === count( $packages ) ) {
			return;
		}

		if ( $plain_text && $email ) {
			$this->display_order_shipping_addresses_plain_email( $packages, $order );
			return;
		}

		// Get all the order items (and generate a unique key for each).
		$cart_item_keys = $this->get_cart_item_keys( $order );

		if ( $email ) {
			$table_style = 'width: 100%; border: 1px solid #eee;';
			$th_style    = 'text-align:left; border: 1px solid #eee;';
			$td_style    = 'text-align:left; vertical-align:middle; border: 1px solid #eee;';
		} else {
			$table_style = '';
			$th_style    = '';
			$td_style    = '';
		}
		?>

		<h2><strong><?php esc_html_e( 'Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></strong></h2>
		<table class="shop_table shipping_packages" cellspacing="0" cellpadding="6" style="<?php echo esc_attr( $table_style ); ?>">
			<thead>
				<tr>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Products', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Address', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					<?php
					/**
					 * Allow plugins to add table head column.
					 *
					 * @since 3.3
					 */
					do_action( 'wc_ms_shop_table_head' );
					?>
					<th scope="col" style="<?php echo esc_attr( $th_style ); ?>"><?php esc_html_e( 'Notes', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				</tr>
			</thead>
			<tbody>

		<?php

		foreach ( $packages as $x => $package ) {
			$products      = $package['contents'];
			$method        = $this->get_shipping_method_name( $x, $methods, $available_methods );
			$address       = $this->get_package_shipping_address( $package );
			$product_infos = $this->get_package_products( $products, $order );

			// Products.
			echo '<tr><td style="' . esc_attr( $td_style ) . '"><ul>';

			foreach ( $product_infos as $i => $info ) {
				$info_text = $info['name'] . ' &times; ' . $info['qty'];

				if ( ! empty( $info['meta'] ) ) {
					$info_text .= '<br />' . $info['meta'];
				}

				echo '<li>' . wp_kses( $info_text, $allowed_html ) . '</li>';
			}

			echo '</ul></td>';

			// Address.
			echo '<td style="' . esc_attr( $td_style ) . '">';
				echo wp_kses( $address, $allowed_html ) . '<br/>';

			if ( ! empty( $method ) ) {
				echo '<em>(' . esc_html( $method ) . ')</em>';
			}

			echo '</td>';

			/**
			 * Allow plugins to add table cell column.
			 *
			 * @param array    Cart package.
			 * @param int      Order ID.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_shop_table_row', $package, $order_id );

			// Notes.
			echo '<td style="' . esc_attr( $td_style ) . '">';
			if ( ! empty( $package['note'] ) ) {
				echo wp_kses( nl2br( $package['note'] ), $allowed_html );
			} else {
				echo '&ndash;';
			}

			if ( ! empty( $package['date'] ) ) {
				// translators: %s is replaced with the package date.
				echo '<p>' . sprintf( esc_html__( 'Delivery date: %s', 'woocommerce-shipping-multiple-addresses' ), esc_html( $package['date'] ) ) . '</p>';
			}
			echo '</td>';

			echo '</tr>';
		}
		echo '</table>';
	}

	/**
	 * Display shipping address in plain email.
	 *
	 * @param array    $packages Array of packages.
	 * @param WC_Order $order Current order object.
	 *
	 * @return void
	 */
	public function display_order_shipping_addresses_plain_email( $packages, $order ) {
		$methods           = $order->get_meta( '_shipping_methods' );
		$available_methods = $order->get_shipping_methods();

		echo esc_html__( 'This order ships to multiple addresses.', 'woocommerce-shipping-multiple-addresses' ) . "\n\n";

		foreach ( $packages as $x => $package ) {
			$products      = $package['contents'];
			$method        = $this->get_shipping_method_name( $x, $methods, $available_methods );
			$address       = str_replace( '<br/>', "\n", $this->get_package_shipping_address( $package ) );
			$product_infos = $this->get_package_products( $products, $order );

			// translators: %d is replaced with the address index.
			echo sprintf( esc_html__( 'Address %d', 'woocommerce-shipping-multiple-addresses' ), ( intval( $x ) + 1 ) ) . "\n\n";

			// Products.
			echo esc_html__( 'Products:', 'woocommerce-shipping-multiple-addresses' ) . "\n";

			foreach ( $product_infos as $i => $info ) {
				echo esc_html( ( $i + 1 ) . '. ' . $info['name'] . ' x ' . $info['qty'] );
				if ( ! empty( $info['meta'] ) ) {
					echo "\n\t" . esc_html( $info['meta'] );
				}
				echo "\n\n";
			}

			echo esc_html__( 'Address:', 'woocommerce-shipping-multiple-addresses' ) . "\n";

			// Address.
			echo esc_html( $address ) . ' (' . esc_html( $method ) . ')' . "\n\n";

			/**
			 * Allow plugins to add table row.
			 *
			 * @param array    Cart package.
			 * @param int      Order ID.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_shop_table_row', $package, $order->get_id() );

			// Notes.
			if ( ! empty( $package['note'] ) ) {
				echo esc_html( $package['note'] );
			} else {
				echo '&ndash;';
			}

			if ( ! empty( $package['date'] ) ) {
				// translators: %s is replaced with the package date.
				echo "\n" . sprintf( esc_html__( 'Delivery date: %s', 'woocommerce-shipping-multiple-addresses' ), esc_html( $package['date'] ) );
			}

			echo "\n\n\n";
		}
	}

	/**
	 * Show custom column for multiple address.
	 *
	 * @param array        $column List of column in the admin table.
	 * @param int|WC_Order $post_id_or_order Post ID or order object.
	 */
	public function show_multiple_addresses_line( $column, $post_id_or_order ) {
		$order = ( $post_id_or_order instanceof WC_Order ) ? $post_id_or_order : wc_get_order( $post_id_or_order );

		if ( ! ( $order instanceof WC_Order ) ) {
			return;
		}

		if ( 'shipping_address' === $column ) {
			$packages = $order->get_meta( '_wcms_packages' );

			if ( ! $order->get_formatted_shipping_address() && is_array( $packages ) && count( $packages ) > 1 ) {
				esc_html_e( 'Ships to multiple addresses ', 'woocommerce-shipping-multiple-addresses' );
			}
		}
	}

	/**
	 * Add meta box in the order admin page.
	 *
	 * @param string           $post_type Type of the post.
	 * @param WP_Post|WC_Order $post_or_order_object Either WP_Post or WC_Order object.
	 */
	public function order_meta_box( $post_type, $post_or_order_object ) {
		if ( ! ( $post_or_order_object instanceof WP_Post ) && ! ( $post_or_order_object instanceof WC_Order ) ) {
			return;
		}

		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$methods   = $order->get_meta( '_shipping_methods' );
		$multiship = $order->get_meta( '_multiple_shipping' );

		if ( 'yes' === $multiship || ( is_array( $methods ) && count( $methods ) > 1 ) ) {
			add_meta_box(
				'wc_multiple_shipping',
				esc_html__( 'Order Shipping Addresses', 'woocommerce-shipping-multiple-addresses' ),
				array( $this, 'packages_meta_box' ),
				WC_MS_Compatibility::get_meta_box_screen(),
				'normal',
				'core'
			);
		}
	}

	/**
	 * Enqueue admin css.
	 */
	public function admin_css() {
		$screen = get_current_screen();

		$order_screen_id = WC_MS_Compatibility::get_meta_box_screen();
		if ( in_array( $screen->id, array( $order_screen_id, 'woocommerce_page_wc-settings' ), true ) ) {
			wp_enqueue_style( 'wc-ms-admin-css', plugins_url( 'assets/css/admin.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );
		}
	}

	/**
	 * Display a shipping package inside order meta box.
	 *
	 * @param WC_Order|WP_Post $post_or_order_object Either post or order object.
	 */
	public function packages_meta_box( $post_or_order_object ) {
		$order = $post_or_order_object instanceof WP_Post ? wc_get_order( $post_or_order_object->ID ) : $post_or_order_object;

		if ( ! $order instanceof WC_Order ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return;
		}

		$package_items          = $order->get_meta( '_packages_item_ids' );
		$methods                = $order->get_meta( '_shipping_methods' );
		$settings               = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$partial_orders         = isset( $settings['partial_orders'] ) && 'yes' === $settings['partial_orders'];
		$send_email             = isset( $settings['partial_orders_email'] ) && 'yes' === $settings['partial_orders_email'];
		$order_shipping_methods = $order->get_shipping_methods();

		// Get all the order items (and match the cart keys).
		$order_items    = $order->get_items();
		$cart_item_keys = array();
		foreach ( $order_items as $item_id => $item ) {
			if ( ! empty( $item['wcms_cart_key'] ) ) {
				$cart_item_keys[ $item['wcms_cart_key'] ] = $item_id;
			}
		}

		echo '<div class="item-addresses-holder">';

		foreach ( $packages as $x => $package ) {
			$products = $package['contents'];
			echo '<div class="item-address-box package-' . esc_attr( $x ) . '-box">';

			if ( $partial_orders && isset( $package['status'] ) && 'Completed' === $package['status'] ) {
				echo '<span class="complete">&nbsp;</span>';
			}

			foreach ( $products as $i => $product ) {

				// Get a matching order item.
				$item = false;
				if ( ! empty( $product['cart_key'] ) && ! empty( $cart_item_keys[ $product['cart_key'] ] ) && isset( $order_items[ $cart_item_keys[ $product['cart_key'] ] ] ) ) {
					$item = $order_items[ $cart_item_keys[ $product['cart_key'] ] ];
				} elseif ( ! empty( $package_items[ $i ] ) && isset( $order_items[ $package_items[ $i ] ] ) ) {
					// Fallback for items stored before WC 3.0.
					$item = $order_items[ $package_items[ $i ] ];
				}

				// Get item name and meta.
				if ( empty( $item ) ) {
					$id = empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'];
					/**
					 * Allow modifying product title.
					 *
					 * @var string $product_title Product title.
					 * @var WC_Product $product Product object.
					 * @return string
					 * @since 3.3.26
					 */
					$name = apply_filters( 'wcms_product_title', get_the_title( $id ), $product );

					/**
					 * Allow modifying item meta list.
					 *
					 * @var string $item_meta list of item meta (HTML).
					 * @var WC_Product $product Product object.
					 * @return string
					 * @since 3.3.26
					 */
					$meta = apply_filters( 'wcms_package_item_meta', self::get_item_meta( $product ), $product );
				} else {
					$name = is_callable( array( $item, 'get_name' ) ) ? $item->get_name() : get_the_title( empty( $product['variation_id'] ) ? $product['product_id'] : $product['variation_id'] );
					/**
					 * Allow modifying product title.
					 *
					 * @var string $product_title Product title.
					 * @var WC_Product $product Product object.
					 * @var WC_Order_Item $item Item object.
					 * @return string
					 * @since 3.3.26
					 */
					$name = apply_filters( 'wcms_product_title', $name, $product, $item );
					$meta = wc_display_item_meta( $item, array( 'echo' => false ) );
					/**
					 * Allow modifying item meta list.
					 *
					 * @var string $item_meta list of item meta (HTML).
					 * @var WC_Product $product Product object.
					 * @var WC_Order_Item $item Item object.
					 * @return string
					 * @since 3.3.26
					 */
					$meta = apply_filters( 'wcms_package_item_meta', $meta, $product, $item );
				}

				// Display product info.
				echo '<h4>' . esc_html( $name ) . ' &times; ' . esc_html( $product['quantity'] ) . '</h4>';
				if ( ! empty( $meta ) ) {
					echo wp_kses_post( $meta );
				}
			}

			self::display_shipping_package_address( $order, $package, $x, true );

			// Get Shipping method for this package.
			$method = isset( $methods[ $x ]['label'] ) ? $methods[ $x ]['label'] : '';

			if ( isset( $methods[ $x ]['id'] ) && empty( $method ) ) {
				foreach ( $order_shipping_methods as $ship_method ) {
					$method_info = explode( ':', $methods[ $x ]['id'] );
					$method_id   = $method_info[0];
					$method_inst = isset( $method_info[1] ) ? $method_info[1] : '';

					if ( $ship_method->get_method_id() . ':' . $ship_method->get_instance_id() === $methods[ $x ]['id']
						|| $ship_method->get_method_id() === $methods[ $x ]['id']
						|| ( $method_id === $ship_method->get_method_id() && $method_inst === $ship_method->get_instance_id() )
					) {
						$method = $ship_method->get_name();
						break;
					}
				}
			}
			if ( empty( $method ) ) {
				$order_method = current( $order_shipping_methods );
				$method       = $order_method['name'];
			}
			echo '<em>' . esc_html( $method ) . '</em>';

			// If partial orders are enabled then show package status.
			if ( $partial_orders ) {
				$current_status = isset( $package['status'] ) ? $package['status'] : 'Pending';

				if ( 'Completed' === $current_status ) {
					$select_css = 'display: none;';
					$status_css = '';
				} else {
					$select_css = '';
					$status_css = 'display: none;';
				}

				echo '<p id="package_' . esc_attr( $x ) . '_select_p" style="' . esc_attr( $select_css ) . '">
							<select id="package_' . esc_attr( $x ) . '_status">
								<option value="Pending" ' . selected( $current_status, 'Pending', false ) . '>' . esc_html__( 'Pending', 'woocommerce-shipping-multiple-addresses' ) . '</option>
								<option value="Completed" ' . selected( $current_status, 'Completed', false ) . '>' . esc_html__( 'Completed', 'woocommerce-shipping-multiple-addresses' ) . '</option>
							</select>
							<a class="button save-package-status" data-order="' . esc_attr( $order->get_id() ) . '" data-package="' . esc_attr( $x ) . '" href="#" title="Apply">' . esc_html__( 'GO', 'woocommerce-shipping-multiple-addresses' ) . '</a>
						</p>';

				echo '<p id="package_' . esc_attr( $x ) . '_status_p" style="' . esc_attr( $status_css ) . '"><strong>' . esc_html__( 'Completed', 'woocommerce-shipping-multiple-addresses' ) . '</strong> (<a href="#" class="edit_package" data-package="' . esc_attr( $x ) . '">' . esc_html__( 'Change', 'woocommerce-shipping-multiple-addresses' ) . '</a>)</p>';
			}

			/**
			 * Allow plugins to add element in package block.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block', $order, $package, $x );

			echo '</div>';
		}
		echo '</div>';
		echo '<div class="clear"></div>';

		$update_pkg_status_nonce = wp_create_nonce( 'wcms_update_package_status_save' );
		$email_enabled           = ( $send_email ) ? 'true' : 'false';
		$inline_js               = '
			var email_enabled = ' . $email_enabled . ';
			jQuery(".shipping_data a.edit_shipping_address").click(function(e) {
				e.preventDefault();
				jQuery(this).closest(".shipping_data").find("div.edit_shipping_address").show();
			});

			jQuery(".save-package-status").click(function(e) {
				e.preventDefault();
				var pkg_id      = jQuery(this).data("package");
				var order_id    = jQuery(this).data("order");
				var status      = jQuery("#package_"+ pkg_id +"_status").val();
				var email       = false;

				if ( status == "Completed" && email_enabled ) {
					if ( confirm("' . __( 'Do you want to send an email to the customer?', 'woocommerce-shipping-multiple-addresses' ) . '") ) {
						email = true;
					}
				}

				jQuery(".package-"+ pkg_id +"-box").block({ message: null, overlayCSS: { background: "#fff url(' . WC()->plugin_url() . '/assets/images/ajax-loader.gif) no-repeat center", opacity: 0.6 } });

				jQuery.post(ajaxurl, {action: "wcms_update_package_status", security: "' . esc_js( $update_pkg_status_nonce ) . '", "status": status, package: pkg_id, order: order_id, email: email}, function(resp) {
					if ( resp == "Completed" ) {
						jQuery(".package-"+ pkg_id +"-box").prepend("<span class=\'complete\'>&nbsp;</span>");
						jQuery("#package_"+ pkg_id +"_status_p").show();
						jQuery("#package_"+ pkg_id +"_select_p").hide();
					} else {
						jQuery(".package-"+ pkg_id +"-box").find("span.complete").remove();
					}

					jQuery(".package-"+ pkg_id +"-box").unblock();
				});

			});

			jQuery(".edit_package").click(function(e) {
				e.preventDefault();

				var pkg_id = jQuery(this).data("package");

				jQuery("#package_"+ pkg_id +"_status_p").hide();
				jQuery("#package_"+ pkg_id +"_select_p").show();
			});
		';

		wc_enqueue_js( $inline_js );
	}

	/**
	 * Display shipping package address.
	 *
	 * @param WC_Order $order Order object.
	 * @param array    $package Cart package.
	 * @param int      $index Package index.
	 * @param boolean  $edit Check if the address is editable.
	 */
	public static function display_shipping_package_address( $order, $package, $index, $edit = false ) {
		if ( empty( $package['destination'] ) ) {
			return;
		}

		$address_map_url = self::generate_address_map_url( $package, $order );
		?>
		<div class="shipping_data">

			<?php
			/**
			 * Allow plugins to add element before the address.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block_before_address', $order, $package, $index );
			?>

			<div class="address">
				<p>
					<a href="<?php echo esc_url( $address_map_url ); ?>" target="_blank">
						<?php // No need to escape. It's already escaped on `WC_Countries::get_formatted_address()`. ?>
						<?php echo WC()->countries->get_formatted_address( $package['destination'] ); // phpcs:ignore ?>
					</a>
				</p>
			</div>

			<?php
			/**
			 * Allow plugins to add element after the address.
			 *
			 * @param WC_Order Order object.
			 * @param array    Cart package.
			 * @param int      Cart package index.
			 *
			 * @since 3.3
			 */
			do_action( 'wc_ms_order_package_block_after_address', $order, $package, $index );
			?>

			<?php
			if ( $edit ) {

				// Get local shipping fields.
				$shipping_fields = WC()->countries->get_address_fields( $package['destination']['country'], 'shipping_' );

				if ( ! empty( $shipping_fields ) ) {
					echo '<a class="edit_shipping_address" href="#">( ' . esc_html__( 'Edit', 'woocommerce-shipping-multiple-addresses' ) . ' )</a><br />';

					// Display form.
					echo '<div class="edit_shipping_address" style="display:none;">';

					foreach ( $shipping_fields as $key => $field ) {
						$key      = str_replace( 'shipping_', '', $key );
						$addr_key = $key;
						$key      = 'pkg_' . $key . '_' . $index;

						if ( ! isset( $field['type'] ) ) {
							$field['type'] = 'text';
						}
						if ( ! isset( $field['label'] ) ) {
							$field['label'] = '';
						}
						switch ( $field['type'] ) {
							case 'select':
								woocommerce_wp_select(
									array(
										'id'      => $key,
										'label'   => $field['label'],
										'options' => $field['options'],
										'value'   => $package['destination'][ $addr_key ],
									)
								);
								break;
							default:
								woocommerce_wp_text_input(
									array(
										'id'    => $key,
										'label' => $field['label'],
										'value' => $package['destination'][ $addr_key ],
									)
								);
								break;
						}
					}

					echo '<input type="hidden" name="edit_address[]" value="' . esc_attr( $index ) . '" />';
					echo '</div>';

				}
			}
			?>

		</div>
		<?php
	}



	/**
	 * Update order addresses.
	 *
	 * @param int $post_id Order ID.
	 * @return void
	 */
	public function update_order_addresses( $post_id ) {
		$order = wc_get_order( $post_id );

		if ( ! $order ) {
			return;
		}

		// No need to verify nonce. it's already verified on `WC_Admin_Meta_Boxes::save_meta_boxes()`.
		$packages       = $order->get_meta( '_wcms_packages' );
		$edit_addresses = ( isset( $_POST['edit_address'] ) ) ? wc_clean( $_POST['edit_address'] ) : array(); //phpcs:ignore

		if ( $packages && is_array( $edit_addresses ) && 0 < count( $edit_addresses ) ) {
			foreach ( $edit_addresses as $idx ) {
				if ( ! isset( $packages[ $idx ] ) ) {
					continue;
				}

				// Get the shipping fields.
				$shipping_fields = WC()->countries->get_address_fields( $packages[ $idx ]['destination']['country'], 'shipping_' );
				$address         = array();

				// Assign value to respective address key.
				foreach ( $shipping_fields as $key => $field ) {
					$addr_key             = str_replace( 'shipping_', '', $key );
					$post_key             = 'pkg_' . $addr_key . '_' . $idx;
					$address[ $addr_key ] = isset( $_POST[ $post_key ] ) ? sanitize_text_field( wp_unslash( $_POST[ $post_key ] ) ) : ''; // phpcs:ignore
				}

				$packages[ $idx ]['destination'] = $address;
			}

			$order->update_meta_data( '_wcms_packages', $packages );
			$order->save();
		}
	}

	/**
	 * Update order taxes.
	 *
	 * @param int   $order_id Order ID.
	 * @param array $items Order items.
	 * @return void
	 */
	public function update_order_taxes( $order_id, $items ) {
		$order_taxes = isset( $items['order_taxes'] ) ? $items['order_taxes'] : array();
		$tax_total   = array();
		$order       = wc_get_order( $order_id );

		if ( ! $order ) {
			return;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! is_array( $packages ) ) {
			return;
		}

		foreach ( $order_taxes as $item_id => $rate_id ) {
			foreach ( $packages as $package ) {
				foreach ( $package['contents'] as $item ) {
					if ( isset( $item['line_tax_data']['total'][ $rate_id ] ) ) {
						if ( ! isset( $tax_total[ $item_id ] ) ) {
							$tax_total[ $item_id ] = 0;
						}
						$tax_total[ $item_id ] += $item['line_tax_data']['total'][ $rate_id ];
					}
				}
			}
		}

		$total_tax = 0;
		foreach ( $tax_total as $item_id => $total ) {
			$total_tax += $total;
			wc_update_order_item_meta( $item_id, 'tax_amount', $tax_total[ $item_id ] );
		}

		$old_total_tax = floatval( $order->get_meta( '_order_tax' ) );

		if ( $total_tax > $old_total_tax ) {
			$order_total  = floatval( $order->get_meta( '_order_total' ) );
			$order_total -= $old_total_tax;
			$order_total += $total_tax;

			$order->update_meta_data( '_order_total', $order_total );
		}

		$order->update_meta_data( '_order_tax', $total_tax );
		$order->save();
	}

	/**
	 * Set order items taxes.
	 *
	 * @param array    $items Order items.
	 * @param WC_Order $order Order object.
	 * @return array
	 */
	public function order_item_taxes( $items, $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return $items;
		}

		if ( 'yes' !== $order->get_meta( '_multiple_shipping' ) ) {
			return $items;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! $packages ) {
			return $items;
		}

		foreach ( $items as $item_id => $item ) {
			if ( 'line_item' !== $item['type'] ) {
				continue;
			}

			$item_tax_subtotal = 0;
			$item_tax_total    = 0;
			$item_tax_data     = array();
			$modified          = false;

			$item_line_tax_data = ! is_array( $item['line_tax_data'] ) ? unserialize( $item['line_tax_data'] ) : $item['line_tax_data'];// phpcs:ignore --- need to unserialize.
			$tax_rate_ids       = array_keys( $item_line_tax_data['total'] );

			foreach ( $packages as $package ) {
				foreach ( $package['contents'] as $package_item ) {

					if ( (int) $item['product_id'] === (int) $package_item['product_id'] && (int) $item['variation_id'] === (int) $package_item['variation_id'] ) {
						$modified = true;

						$item_tax_subtotal += $package_item['line_subtotal_tax'];
						$item_tax_total    += $package_item['line_tax'];

						if ( $item instanceof WC_Order_Item_Product ) {
							$item_rate_ids = array_keys( $package_item['line_tax_data']['total'] );
							$tax_rate_ids  = array_unique( array_merge( $tax_rate_ids, $item_rate_ids ) );
						}

						foreach ( $tax_rate_ids as $rate_id ) {
							if ( isset( $package_item['line_tax_data']['total'][ $rate_id ] ) ) {
								$item_tax_data['total'][ $rate_id ] = $package_item['line_tax_data']['total'][ $rate_id ];
							}

							if ( isset( $package_item['line_tax_data']['subtotal'][ $rate_id ] ) ) {
								$item_tax_data['subtotal'][ $rate_id ] = $package_item['line_tax_data']['subtotal'][ $rate_id ];
							}
						}
					}
				}
			}

			if ( $modified && is_array( $tax_rate_ids ) ) {
				if ( $item instanceof WC_Order_Item_Product ) {
					$items[ $item_id ]->set_taxes( $item_tax_data );
				} else {
					$items[ $item_id ]['line_tax']          = $item_tax_total;
					$items[ $item_id ]['line_subtotal_tax'] = $item_tax_subtotal;
					$items[ $item_id ]['line_tax_data']     = serialize( $item_tax_data );// phpcs:ignore --- Need to be serialized.
				}
			}
		}

		return $items;
	}

	/**
	 * Load a custom template body for orders with multishipping
	 *
	 * @param string   $template PIP Template path.
	 * @param WC_Order $order Order object.
	 *
	 * @return string $template
	 */
	public function pip_template_body( $template, $order ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return $template;
		}

		$packages = $order->get_meta( '_shipping_packages' );

		if ( is_array( $packages ) && 1 < count( $packages ) ) {
			$template = dirname( WC_Ship_Multiple::FILE ) . '/templates/pip-template-body.php';
		}

		return $template;
	}

	/**
	 * Send package email.
	 *
	 * @param int $order_id Order ID.
	 * @param int $package_index Loop index.
	 * @return void
	 */
	public static function send_package_email( $order_id, $package_index ) {
		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$order    = wc_get_order( $order_id );

		$subject = empty( $settings['email_subject'] ) ? esc_html__( 'Part of your order has been shipped', 'woocommerce-shipping-multiple-addresses' ) : $settings['email_subject'];
		$message = empty( $settings['email_message'] ) ? self::get_default_email_body() : $settings['email_message'];

		$mailer  = WC()->mailer();
		$message = $mailer->wrap_message( $subject, $message );

		$ts         = strtotime( WC_MS_Compatibility::get_order_prop( $order, 'order_date' ) );
		$order_date = gmdate( get_option( 'date_format' ), $ts );
		$order_time = gmdate( get_option( 'time_format' ), $ts );

		$search       = array( '{order_id}', '{order_date}', '{order_time}', '{customer_first_name}', '{customer_last_name}', '{products_table}', '{addresses_table}' );
		$replacements = array(
			$order->get_order_number(),
			$order_date,
			$order_time,
			$order->get_billing_first_name(),
			$order->get_billing_last_name(),
			self::render_products_table( $order, $package_index ),
			self::render_addresses_table( $order, $package_index ),
		);
		$message      = str_replace( $search, $replacements, $message );

		$mailer->send( $order->get_billing_email(), $subject, $message );
	}

	/**
	 * Get default email body.
	 *
	 * @return string
	 */
	public static function get_default_email_body() {
		ob_start();
		?>
		<?php // translators: %s is a blog or site name. ?>
		<p><?php printf( esc_html__( 'Hi there. Part of your recent order on %s has been completed. Your order details are shown below for your reference:', 'woocommerce-shipping-multiple-addresses' ), esc_html( get_option( 'blogname' ) ) ); ?></p>

		<h2><?php echo esc_html__( 'Order:', 'woocommerce-shipping-multiple-addresses' ) . ' {order_id}'; ?></h2>

		{products_table}

		{addresses_table}

		<?php
		return ob_get_clean();
	}

	/**
	 * Render order products table.
	 *
	 * @param WC_Order $order Order object.
	 * @param int      $idx Package index.
	 * @return string
	 */
	public static function render_products_table( $order, $idx ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return '';
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $packages[ $idx ];
		$products = $package['contents'];

		ob_start();
		?>
		<table cellspacing="0" cellpadding="6" style="width: 100%; border: 1px solid #eee;" border="1" bordercolor="#eee">
			<thead>
			<tr>
				<th scope="col" style="text-align:left; border: 1px solid #eee;"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th scope="col" style="text-align:left; border: 1px solid #eee;"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
			</tr>
			</thead>
			<tbody>
			<?php
			foreach ( $products as $item ) :
				$_product             = wc_get_product( $item['product_id'] );
				$attachment_image_src = wp_get_attachment_image_src( get_post_thumbnail_id( $_product->get_id() ), 'thumbnail' );
				$image                = $attachment_image_src ? '<img src="' . esc_url( current( $attachment_image_src ) ) . '" alt="Product Image" height="32" width="32" style="vertical-align:middle; margin-right: 10px;" />' : '';
				?>
				<tr>
					<td style="text-align:left; vertical-align:middle; border: 1px solid #eee; word-wrap:break-word;">
					<?php
					// Show title/image etc.
					/**
					 * Allow plugins to modify product image output.
					 *
					 * @param string Product image HTML output.
					 * @param WC_Product Product object.
					 * @param boolean As variable.
					 *
					 * @since 3.3
					 */
					$output_image      = apply_filters( 'woocommerce_order_product_image', $image, $_product, true );
					$allowed_image_tag = array(
						'img' => array(
							'src'    => array(),
							'alt'    => array(),
							'height' => array(),
							'width'  => array(),
							'style'  => array(),
						),
					);
					echo wp_kses( $output_image, $allowed_image_tag ); //phpcs:ignore

					// Product name.
					/**
					 * Allow plugins to modify product title.
					 *
					 * @param string     Product title,
					 * @param WC_Product Product object.
					 *
					 * @since 3.3
					 */
					$output_product_name = apply_filters( 'woocommerce_order_product_title', $_product->get_title(), $_product );
					echo wp_kses_post( $output_product_name );

					// SKU.
					echo ( $_product->get_sku() ? esc_html( ' (#' . $_product->get_sku() . ')' ) : '' );

					// File URLs.
					if ( $_product->exists() && $_product->is_downloadable() ) {

						$download_file_urls = $order->get_downloadable_file_urls( $item['product_id'], $item['variation_id'], $item );

						$i = 0;

						foreach ( $download_file_urls as $file_url => $download_file_url ) {
							echo '<br/><small>';

							$filename = wc_get_filename_from_url( $file_url );

							if ( count( $download_file_urls ) > 1 ) {
								// translators: %d is an index of the URLS array.
								printf( esc_html__( 'Download %d:', 'woocommerce-shipping-multiple-addresses' ), intval( $i ) + 1 );
							} elseif ( 0 === $i ) {
								echo esc_html__( 'Download:', 'woocommerce-shipping-multiple-addresses' );
							}

								echo ' <a href="' . esc_url( $download_file_url ) . '" target="_blank">' . esc_html( $filename ) . '</a></small>';

								++$i;
						}
					}
					?>
					</td>
					<td style="text-align:left; vertical-align:middle; border: 1px solid #eee;"><?php echo esc_html( $item['quantity'] ); ?></td>
				</tr>
			<?php endforeach; ?>
			</tbody>
		</table>
		<?php

		return ob_get_clean();
	}

	/**
	 * Render order addresses table.
	 *
	 * @param WC_Order $order Order object.
	 * @param int      $index Package index.
	 * @return string
	 */
	public static function render_addresses_table( $order, $index ) {
		if ( ! is_callable( array( $order, 'get_meta' ) ) ) {
			return '';
		}

		$packages = $order->get_meta( '_wcms_packages' );
		$package  = $packages[ $index ];

		ob_start();
		?>
		<table cellspacing="0" cellpadding="0" style="width: 100%; vertical-align: top;" border="0">
			<tr>
				<td valign="top" width="50%">
					<h3><?php esc_html_e( 'Billing address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php // no need to escape. It's already escape from `WC_Order::get_formatted_billing_address()`. ?>
					<p><?php echo $order->get_formatted_billing_address(); //phpcs:ignore ?></p>
				</td>
				<td valign="top" width="50%">
					<h3><?php esc_html_e( 'Shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php self::display_shipping_package_address( $order, $package, $index ); ?>
				</td>
			</tr>
		</table>
		<?php

		return ob_get_clean();
	}

	/**
	 * Gets and formats a list of item meta for display (fallback function for when we can't find order item)
	 *
	 * @param array $item Cart item.
	 *
	 * @return string
	 */
	public static function get_item_meta( $item ) {

		$item_data = array();

		// Variation data.
		if ( ! empty( $item['data']->variation_id ) && is_array( $item['variation'] ) ) {

			foreach ( $item['variation'] as $name => $value ) {

				if ( empty( $value ) ) {
					continue;
				}

				$taxonomy = wc_attribute_taxonomy_name( str_replace( 'attribute_pa_', '', urldecode( $name ) ) );

				// If this is a term slug, get the term's nice name.
				if ( taxonomy_exists( $taxonomy ) ) {
					$term = get_term_by( 'slug', $value, $taxonomy );
					if ( ! is_wp_error( $term ) && $term && $term->name ) {
						$value = $term->name;
					}
					$label = wc_attribute_label( $taxonomy );

					// If this is a custom option slug, get the options name.
				} else {
					/**
					 * Allow plugins to modify variation option name.
					 *
					 * @param mixed variation option value.
					 *
					 * @since 3.3
					 */
					$value = apply_filters( 'woocommerce_variation_option_name', $value );
					$label = wc_attribute_label( str_replace( 'attribute_', '', $name ), $item['data'] );
				}

				$item_data[] = array(
					'key'   => $label,
					'value' => $value,
				);
			}
		}

		$output = '';
		if ( ! empty( $item_data ) ) {
			$output .= '<ul>';
			foreach ( $item_data as $data ) {
				$output .= '<li>' . esc_html( $data['key'] ) . ': ' . wp_kses_post( $data['value'] ) . '</li>';
			}
			$output .= '</ul>';
		}

		return $output;
	}

	/**
	 * Hides metadata.
	 *
	 * @param  array $hidden Hidden meta strings.
	 * @return array Modified hidden meta strings
	 */
	public function hidden_order_item_meta( $hidden ) {
		return array_merge( $hidden, array( '_wcms_cart_key' ) );
	}

	/**
	 * Hide shipping address if order has multiple address.
	 *
	 * @param bool     $needs_shipping Whether the order need shipping or not.
	 * @param array    $hide List of shipping method that will hide the shipping address.
	 * @param WC_Order $order Order object.
	 *
	 * @return bool
	 */
	public function manipulate_needs_shipping( $needs_shipping, $hide, $order ) {
		if ( ! $order || ! is_view_order_page() ) {
			return $needs_shipping;
		}

		$packages = $order->get_meta( '_wcms_packages' );

		if ( ! empty( $packages ) && 1 < count( $packages ) ) {
			return false;
		}

		return $needs_shipping;
	}
}
</file>

<file path="templates/account-address-form.php">
<?php
/**
 * Account address form template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<form action="" method="post" id="address_form">

	<?php if ( $updating ) : ?>

		<div id="addresses">

			<div class="shipping_address address_block" id="shipping_address_<?php echo esc_attr( $idx ); ?>">
				<?php
				foreach ( $ship_fields as $key => $field ) {
					$val = '';

					if ( isset( $address[ $key ] ) ) {
						$val = $address[ $key ];
					}

					woocommerce_form_field( $key, $field, $val );
				}

				/**
				 * Action to add element on wcms address form.
				 *
				 * @param WC_Checkout $checkout checkout object.
				 *
				 * @since 3.3
				 */
				do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
				?>
			</div>

		</div>

	<?php else : ?>

		<div id="addresses" class="address-column">

		<?php
		foreach ( $ship_fields as $key => $field ) :
			$val = '';

			woocommerce_form_field( $key, $field, $val );
		endforeach;
		?>
		</div>
	<?php endif; ?>

	<div class="form-row">
		<?php wp_nonce_field( 'shipping_account_address_action' ); ?>
		<input type="submit" name="set_addresses" value="<?php esc_attr_e( 'Save Address', 'woocommerce-shipping-multiple-addresses' ); ?>" class="button alt" />
	</div>
</form>
<script type="text/javascript">
	jQuery( document ).ready( function( $ ) {
		$( '#address_form' ).submit( function() {
			var valid = true;

			$( '.input-text, select, input:checkbox' ).each( function( e ) {
				var $this             = $( this ),
					$parent           = $this.closest( '.form-row' ),
					validate_required = $parent.is( '.validate-required' );

				if ( validate_required ) {
					if ( 'checkbox' === $this.attr( 'type' ) && ! $this.is( ':checked' ) ) {
						valid = false;
					} else if ( $this.val() === '' ) {
						valid = false;
					}
				}

				if ( ! valid ) {
					$parent.removeClass( 'woocommerce-validated' ).addClass( 'woocommerce-invalid woocommerce-invalid-required-field' );
					$this.focus();
					return false;
				}
			});

			return valid;
		});
	});
</script>
</file>

<file path="templates/address-block.php">
<?php
/**
 * Address block template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<div class="address-block">
<?php
$page_id   = wc_get_page_id( 'multiple_addresses' );
$form_link = get_permalink( $page_id );
$addr      = array(
	'first_name' => $address['shipping_first_name'],
	'last_name'  => $address['shipping_last_name'],
	'company'    => $address['shipping_company'],
	'address_1'  => $address['shipping_address_1'],
	'address_2'  => $address['shipping_address_2'],
	'city'       => $address['shipping_city'],
	'state'      => $address['shipping_state'],
	'postcode'   => $address['shipping_postcode'],
	'country'    => $address['shipping_country'],
);

$formatted_address = wcms_get_formatted_address( $address );
if ( $formatted_address ) {
	// phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped --- Escaped in wcms_get_formatted_address()
	echo '<address>' . $formatted_address . '</address>';
	$ref = '';
	if ( is_account_page() ) {
		$ref = 'account';
	}
	$edit_link = add_query_arg(
		array(
			'edit'         => $idx,
			'address-form' => 1,
			'ref'          => $ref,
		),
		$form_link
	);

	if ( empty( $address['default_address'] ) || true !== $address['default_address'] ) {
		?>
		<div class="buttons">
			<a class="button" href="<?php echo esc_url( $edit_link ); ?>#shipping_address"><?php esc_html_e( 'Edit', 'woocommerce-shipping-multiple-addresses' ); ?></a>
			<a class="button ms_delete_address" data-idx="<?php echo esc_attr( $idx ); ?>" href="#"><?php esc_html_e( 'Delete', 'woocommerce-shipping-multiple-addresses' ); ?></a>
		</div>
		<?php
	}
}
?>
</div>
</file>

<file path="templates/pip-template-body.php">
<?php
/**
 * PIP template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

$packages                  = $order->get_meta( '_wcms_packages' );
$packages_shipping_methods = $order->get_meta( '_shipping_methods' );
$order_shipping_methods    = $order->get_shipping_methods();

foreach ( $packages as $pkg_idx => $package ) :
	if ( ! is_array( $packages_shipping_methods ) ) {
		$order_method              = current( $order_shipping_methods );
		$packages_shipping_methods = array(
			$pkg_idx => array(
				'id'   => $order_method['method_id'],
				'name' => $order_method['name'],
			),
		);
	}

	$method       = $packages_shipping_methods[ $pkg_idx ]['label'];
	$order_method = '';

	if ( isset( $packages_shipping_methods[ $pkg_idx ]['id'] ) ) {
		foreach ( $order_shipping_methods as $ship_id => $ship_method ) {
			if ( $ship_method['method_id'] === $packages_shipping_methods[ $pkg_idx ]['id'] ) {
				$method       = $ship_method['name'];
				$order_method = $ship_method;
				unset( $order_shipping_methods[ $ship_id ] );
				break;
			}
		}
	}
	?>
<header>
	<a class="print" href="#" onclick="window.print()"><?php esc_html_e( 'Print', 'woocommerce-shipping-multiple-addresses' ); ?></a>
	<div style="float: left; width: 49%;">
		<?php
        // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- escaping is delegated to WC-PIP
		echo woocommerce_pip_print_logo();
		?>
		<?php if ( 'print_invoice' === $action ) { ?>
			<h3><?php esc_html_e( 'Invoice', 'woocommerce-shipping-multiple-addresses' ); ?> (<?php echo woocommerce_pip_invoice_number( $order->get_id() ); ?>)</h3>
		<?php } else { ?>
			<h3><?php esc_html_e( 'Packing list', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
		<?php } ?>
		<h3><?php esc_html_e( 'Order', 'woocommerce-shipping-multiple-addresses' ); ?> <?php echo $order->get_order_number(); ?> &mdash; <time datetime="<?php echo gmdate( 'Y/m/d', strtotime( WC_MS_Compatibility::get_order_prop( $order, 'order_date' ) ) ); ?>"><?php echo gmdate( 'Y/m/d', strtotime( WC_MS_Compatibility::get_order_prop( $order, 'order_date' ) ) ); ?></time></h3>
	</div>
	<div style="float: right; width: 49%; text-align:right;">
		<?php echo woocommerce_pip_print_company_name(); ?>
		<?php echo woocommerce_pip_print_company_extra(); ?>
	</div>
	<div style="clear:both;"></div>
	<?php
    // phpcs:enable WordPress.Security.EscapeOutput.OutputNotEscaped
	?>
</header>
<section>
	<div class="article">
		<header>

			<div style="float:left; width: 49%;">

				<h3><?php esc_html_e( 'Billing address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>

				<p>
					<?php
                    // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- escaping is done in WC_Order
					echo $order->get_formatted_billing_address();
					?>
				</p>
				<?php
				/**
				 * Action to add element after billing address.
				 *
				 * @param WC_Order $order Order object.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_print_invoice_packing_template_body_after_billing_address', $order );
				?>
				<?php if ( $order->get_meta( 'VAT Number' ) && 'print_invoice' === $action ) : ?>
					<p><strong><?php esc_html_e( 'VAT:', 'woocommerce-shipping-multiple-addresses' ); ?></strong> <?php echo $order->get_meta( 'VAT Number' ); ?></p>
				<?php endif; ?>
				<?php if ( ! empty( $order->get_billing_email() ) ) : ?>
					<p><strong><?php esc_html_e( 'Email:', 'woocommerce-shipping-multiple-addresses' ); ?></strong> <?php echo $order->get_billing_email(); ?></p>
				<?php endif; ?>
				<?php if ( ! empty( $order->get_billing_phone() ) ) : ?>
					<p><strong><?php esc_html_e( 'Tel:', 'woocommerce-shipping-multiple-addresses' ); ?></strong> <?php echo $order->get_billing_phone(); ?></p>
				<?php endif; ?>

			</div>

			<div style="float:right; width: 49%;">

				<h3><?php esc_html_e( 'Shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></h3>

				<p><?php echo wcms_get_formatted_address( $package['destination'] ); ?></p>

				<?php if ( $order->get_meta( '_tracking_provider' ) ) : ?>
					<p><strong><?php esc_html_e( 'Tracking provider:', 'woocommerce-shipping-multiple-addresses' ); ?></strong> <?php echo $order->get_meta( '_tracking_provider' ); ?></p>
				<?php endif; ?>
				<?php if ( $order->get_meta( '_tracking_number' ) ) : ?>
					<p><strong><?php esc_html_e( 'Tracking number:', 'woocommerce-shipping-multiple-addresses' ); ?></strong> <?php echo $order->get_meta( '_tracking_number' ); ?></p>
				<?php endif; ?>

			</div>

			<div style="clear:both;"></div>

			<?php if ( 'print_packing' === $action && 'yes' === get_option( 'woocommerce_calc_shipping' ) ) : ?>
				<div>
					<strong><?php esc_html_e( 'Shipping:', 'woocommerce-shipping-multiple-addresses' ); ?></strong>
					<?php echo $method; ?>
				</div>
			<?php endif; ?>

			<?php if ( ! empty( $package['note'] ) ) { ?>
				<div>
					<h3><?php esc_html_e( 'Order notes', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php echo $package['note']; ?>
				</div>
			<?php } ?>

			<?php if ( ! empty( $package['date'] ) ) { ?>
				<div>
					<h3><?php esc_html_e( 'Shipping Date', 'woocommerce-shipping-multiple-addresses' ); ?></h3>
					<?php echo $package['date']; ?>
				</div>
			<?php } ?>

		</header>
		<div class="datagrid">
			<?php if ( 'print_invoice' === $action ) { ?>
				<table>
					<thead>
					<tr>
						<th scope="col" style="text-align:left; width: 15%;"><?php esc_html_e( 'SKU', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 40%;"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 15%;"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 30%;"><?php esc_html_e( 'Price', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					</tr>
					</thead>
					<tfoot>
					<tr>
						<th colspan="2" style="text-align:left; padding-top: 12px;">&nbsp;</th>
						<th scope="row" style="text-align:right; padding-top: 12px;"><?php esc_html_e( 'Subtotal:', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<td style="text-align:left; padding-top: 12px;"><?php echo wc_price( $package['contents_cost'] ); ?></td>
					</tr>
					<?php
					if ( 'yes' === get_option( 'woocommerce_calc_shipping' ) ) :
						?>
						<tr>
						<th colspan="2" style="text-align:left; padding-top: 12px;">&nbsp;</th>
						<th scope="row" style="text-align:right;"><?php esc_html_e( 'Shipping:', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<td style="text-align:left;">
						<?php
							$tax_display = get_option( 'woocommerce_tax_display_cart' );

						if ( empty( $order_method ) ) {
							echo '-';
						} else {
							if ( $order_method['cost'] > 0 ) {
								// Show shipping excluding tax.
								$shipping = wc_price(
									$order_method['cost'],
									array(
										'currency' => version_compare( WC_VERSION, '3.0', '<' ) ? $order->get_order_currency() : $order->get_currency(),
									)
								);

								// translators: %s is shipping method.
								$shipping .= sprintf( __( '&nbsp;<small>via %s</small>', 'woocommerce-shipping-multiple-addresses' ), $order_method['name'] );

							} elseif ( $order_method['name'] ) {
								$shipping = $order_method['name'];
							} else {
								$shipping = __( 'Free!', 'woocommerce-shipping-multiple-addresses' );
							}

							echo $shipping;
						}

						?>
						</td>
						</tr><?php endif; ?>
					<tr>
						<th colspan="2" style="text-align:left; padding-top: 12px;">&nbsp;</th>
						<th scope="row" style="text-align:right;"><?php esc_html_e( 'Total:', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<td style="text-align:left;"><?php echo wc_price( $package['contents_cost'] + $order_method['cost'] ); ?> <?php esc_html_e( '- via', 'woocommerce-shipping-multiple-addresses' ); ?> <?php echo ucwords( $order->get_payment_method_title() ); ?></td>
					</tr>
					</tfoot>
					<tbody>
					<?php
					foreach ( $package['contents'] as $item ) {

						// get the product; if this variation or product has been deleted, this will return null...
						$_product = $item->get_product();

						$sku       = '';
						$variation = '';

						if ( $_product ) {
							$sku = $_product->get_sku();
						}

						$attributes = wc_get_formatted_cart_item_data( $item, true );

						if ( ! empty( $attributes ) ) {
							$variation = '<small style="display: block; margin: 5px 0 10px 10px;">' . str_replace( "\n", '<br/>', $attributes ) . '</small>';
						}

						?>
						<tr>
							<td style="text-align:left; padding: 3px;"><?php echo $sku; ?></td>
							<td style="text-align:left; padding: 3px;">
								<?php
								/**
								 * Filter for manipulating product title.
								 *
								 * @param string Product title.
								 * @param WC_Product $_product Product object.
								 *
								 * @since 3.3
								 */
								echo apply_filters( 'woocommerce_order_product_title', get_the_title( $_product ), $_product ) . $variation;
								?>
							</td>
							<td style="text-align:left; padding: 3px;"><?php echo $item['quantity']; ?></td>
							<td style="text-align:left; padding: 3px;">
								<?php
								if ( 'excl' === get_option( 'woocommerce_tax_display_cart' ) || ! $order->get_prices_include_tax() ) {
									$ex_tax_label = $order->get_prices_include_tax() ? 1 : 0;
									echo wc_price( $order->get_line_subtotal( $item ), array( 'ex_tax_label' => $ex_tax_label ) );
								} else {
									echo wc_price( $order->get_line_subtotal( $item, true ) );
								}
								?>
							</td>
						</tr>
						<?php } ?>
					</tbody>
				</table>
			<?php } else { ?>
				<table>
					<thead>
					<tr>
						<th scope="col" style="text-align:left; width: 22.5%;"><?php esc_html_e( 'SKU', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 57.5%;"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 15%;"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
						<th scope="col" style="text-align:left; width: 20%;"><?php esc_html_e( 'Total Weight', 'woocommerce-shipping-multiple-addresses' ); ?></th>
					</tr>
					</thead>
						<tbody>
						<?php
						foreach ( $package['contents'] as $item_key => $item ) {

							$attributes = wc_get_formatted_cart_item_data( $item, true );

							// get the product; if this variation or product has been deleted, this will return null...
							$_product = $item->get_product();

							$sku       = '';
							$variation = '';

							if ( $_product ) {
								$sku = $_product->get_sku();
							}

							if ( ! empty( $attributes ) ) {
								$variation = '<small style="display: block; margin: 5px 0 10px 10px;">' . str_replace( "\n", '<br/>', $attributes ) . '</small>';
							}
							?>
							<tr>
							<td style="text-align:left; padding: 3px;"><?php echo $sku; ?></td>
							<td style="text-align:left; padding: 3px;">
								<?php
								/**
								 * Filter for manipulating product title.
								 *
								 * @param string Product title.
								 * @param WC_Product $_product Product object.
								 *
								 * @since 3.3
								 */
								echo apply_filters( 'woocommerce_order_product_title', $_product->get_title(), $_product ) . $variation;
								?>
							</td>
							<td style="text-align:left; padding: 3px;"><?php echo $item['quantity']; ?></td>
							<td style="text-align:left; padding: 3px;">
								<?php echo ( $_product && $_product->get_weight() ) ? $_product->get_weight() * $item['quantity'] . ' ' . get_option( 'woocommerce_weight_unit' ) : __( 'n/a', 'woocommerce-shipping-multiple-addresses' ); ?>
							</td>
						</tr>
						<?php } ?>
					</tbody>
				</table>
			<?php } ?>
		</div>
	</div>
	<div class="article">
		<?php echo woocommerce_pip_print_return_policy(); ?>
	</div>
</section>
<div class="footer">
	<?php echo woocommerce_pip_print_footer(); ?>
</div>
<p class="pagebreak"></p>
	<?php
endforeach;
</file>

<file path="templates/address-form.php">
<?php
/**
 * Address form template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( empty( $addresses ) ) {
	echo '<p>' . esc_html__( 'No address on file. Please add one below.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
} else {
	echo '<div class="address-container">';
	foreach ( $addresses as $idx => $address ) {

		wc_get_template(
			'address-block.php',
			array(
				'idx'         => $idx,
				'address'     => $address,
				'checkout'    => $checkout,
				'ship_fields' => $ship_fields,
			),
			'multi-shipping',
			dirname( WC_Ship_Multiple::FILE ) . '/templates/'
		);

	}
		echo '<div class="clear"></div>';
	echo '</div>';

}

?>

<hr />

<?php
$address_id = isset( $_GET['edit'] ) ? intval( $_GET['edit'] ) : -1; // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation
$address    = array();

if ( -1 !== $address_id ) :
	$address = $addresses[ $address_id ];
	?>
	<h2><?php esc_html_e( 'Edit address', 'woocommerce-shipping-multiple-addresses' ); ?></h2>
<?php else : ?>
	<h2><?php esc_html_e( 'Add a new address', 'woocommerce-shipping-multiple-addresses' ); ?></h2>
	<?php if ( ! isset( $_GET['ref'] ) ) : // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation ?>
	<p>
		<a href="#" class="button btn-import-billing" style="display: none;"><?php esc_html_e( 'Import billing address', 'woocommerce-shipping-multiple-addresses' ); ?></a>
		<a href="#" class="button btn-import-shipping" style="display: none;"><?php esc_html_e( 'Import shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></a>
	</p>
	<?php endif; ?>
<?php endif; ?>

<?php wc_print_notices(); ?>

<form action="" method="post" class="wcms-address-form">
	<div class="shipping_address address_block" id="shipping_address">
		<?php
		/**
		 * Action to add element on wcms before address form.
		 *
		 * @param WC_Checkout $checkout checkout object.
		 *
		 * @since 3.3
		 */
		do_action( 'woocommerce_before_checkout_shipping_form', $checkout );
		?>

		<div class="address-column">
			<?php
			$location = wc_get_customer_default_location();

			if ( empty( $address ) && ! empty( $location ) ) {
				foreach ( $location as $key => $value ) {
					$address[ 'shipping_' . $key ] = $value;
				}
			}

			foreach ( $ship_fields as $key => $field ) :
				$val             = ( isset( $address[ $key ] ) ) ? $address[ $key ] : '';
				$ship_field_id   = rtrim( str_replace( '[', '_', $key ), ']' );
				$field['return'] = true;

				$val = ( empty( $val ) && ! empty( $_GET[ $key ] ) ) ? sanitize_text_field( wp_unslash( $_GET[ $key ] ) ) : $val; // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation.

				// Making sure to get the state dropdown by checking the available shipping countries.
				// If the available country is only one, the state field will use `country` args to display the state.
				if ( 'shipping_state' === $key ) {
					$countries = WC()->countries->get_shipping_countries();

					if ( 1 === count( $countries ) ) {
						$country_code     = array_keys( $countries );
						$field['country'] = array_shift( $country_code );
					}
				}

                // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- skipping escaping since fragments are being escaped
				echo str_replace( 'name="' . esc_attr( $key ) . '"', 'name="address[' . esc_attr( $ship_field_id ) . ']"', woocommerce_form_field( $key, $field, $val ) );
			endforeach;

			/**
			 * Action to add element on wcms address form.
			 *
			 * @param WC_Checkout $checkout checkout object.
			 *
			 * @since 3.3
			 */
			do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
			?>
			<?php wp_nonce_field( 'save_to_address_book' ); ?>
			<input type="hidden" name="action" value="save_to_address_book" />
			<input type="hidden" name="id" id="address_id" value="<?php echo esc_attr( $address_id ); ?>" />

			<input type="hidden" name="return" value="list" />

			<?php if ( ! empty( $_GET['ref'] ) ) : // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation. ?>
			<input type="hidden" name="next" value="<?php echo esc_url( get_permalink( wc_get_page_id( 'myaccount' ) ) ); ?>" />
			<?php endif; ?>
		</div>

	</div>

	<?php if ( $address_id > -1 ) : ?>
		<input type="submit" class="button alt" id="use_address" value="<?php esc_html_e( 'Update Address', 'woocommerce-shipping-multiple-addresses' ); ?>" />
	<?php else : ?>
		<input type="submit" class="button alt" id="use_address" value="<?php esc_html_e( 'Save Address', 'woocommerce-shipping-multiple-addresses' ); ?>" />
	<?php endif; ?>

</form>
<script type="text/javascript">
	var billing_address = null,
		shipping_address = null;

	jQuery(document).ready(function($) {
		if ( supports_html5_storage() ) {
			billing_address = localStorage.getItem( 'wcms_billing_fields' );
			shipping_address = localStorage.getItem( 'wcms_shipping_fields' );

			if ( billing_address ) {
				billing_address = JSON.parse( billing_address );
				$(".btn-import-billing").show();
			}

			if ( shipping_address ) {
				shipping_address = JSON.parse( shipping_address );
				$(".btn-import-shipping").show();
			}

			$(".btn-import-billing").click(function(e) {
				e.preventDefault();
				$( '#ms_addresses' ).val(''); // Reset dropdown on add screen

				for ( field in billing_address ) {
					var shipping_field = field.replace('billing_', 'shipping_');
					$("#"+ shipping_field)
						.val( billing_address[field] )
						.change();
				}
			});

			$(".btn-import-shipping").click(function(e) {
				e.preventDefault();
				$( '#ms_addresses' ).val(''); // Reset dropdown on add screen

				for ( field in shipping_address ) {
					$("#"+ field)
						.val( shipping_address[field] )
						.change();
				}
			});
		}
	});
</script>
</file>

<file path="includes/class-wc-ms-address-book.php">
<?php
/**
 * Class WC_MS_Address_Book file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class for managing address book.
 */
class WC_MS_Address_Book {
	/**
	 * Multiple address class.
	 *
	 * @var WC_Ship_Multiple.
	 */
	private $wcms;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Multiple address object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		add_action( 'template_redirect', array( $this, 'save_shipping_addresses' ) );
		add_action( 'template_redirect', array( $this, 'save_account_shipping_addresses' ) );
		add_action( 'template_redirect', array( $this, 'save_addresses_book_from_post' ) );

		add_action( 'wp_ajax_wc_ms_delete_address', array( $this, 'ajax_delete_address' ) );
		add_action( 'wp_ajax_nopriv_wc_ms_delete_address', array( $this, 'ajax_delete_address' ) );
	}

	/**
	 * Get the user's default address
	 *
	 * @param int $user_id User ID.
	 *
	 * @return array
	 */
	public function get_user_default_address( $user_id ) {
		$shipping_address_fields = array(
			// current field => backwards compatibility field.
			'shipping_first_name' => 'first_name',
			'shipping_last_name'  => 'last_name',
			'shipping_company'    => 'company',
			'shipping_address_1'  => 'address_1',
			'shipping_address_2'  => 'address_2',
			'shipping_city'       => 'city',
			'shipping_state'      => 'state',
			'shipping_postcode'   => 'postcode',
			'shipping_country'    => 'country',
		);

		$customer        = new WC_Customer( $user_id );
		$default_address = array( 'default_address' => true );

		foreach ( $shipping_address_fields as $key => $backwards_key ) {
			$default_address[ $key ] = $customer->{ 'get_' . $key }();
			
			// Backwards compatibility.
			$default_address[ $backwards_key ] = $default_address[ $key ];
		}

		/**
		 * Filter to manipulate the user default address.
		 *
		 * @param array $default_address User's default address.
		 *
		 * @since 3.3.16
		 */
		return apply_filters( 'wc_ms_default_user_address', $default_address );
	}

	/**
	 * Validate multi shipping items to make sure it has the same quantity as the real cart items.
	 *
	 * @param array $items Multi shipping items.
	 *
	 * @return boolean
	 */
	public function validate_items_quantity( array $items ) {
		$cart_items = wcms_get_real_cart_items();
		foreach ( $items as $cart_key => $item ) {
			$real_qty = isset( $cart_items[ $cart_key ]['quantity'] ) ? intval( $cart_items[ $cart_key ]['quantity'] ) : 0;

			if ( ! isset( $item['qty'] ) ) {
				return false;
			}

			$ms_qty = 0;

			foreach ( $item['qty'] as $qty ) {
				$ms_qty += intval( $qty );
			}

			if ( $ms_qty !== $real_qty ) {
				return false;
			}
		}
		return true;
	}

	/**
	 * Save shipping addresses to the current cart session.
	 */
	public function save_shipping_addresses() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'shipping_address_action' ) ) {
			return;
		}

		if ( ! isset( $_POST['shipping_address_action'] ) || 'save' !== $_POST['shipping_address_action'] ) {
			return;
		}

		$cart           = WC()->cart;
		$user_addresses = $this->get_user_addresses( get_current_user_id() );
		$customer       = WC()->customer;

		// Save the guest's shipping address temporarily.
		if ( $customer instanceof WC_Customer && 0 === $customer->get_id() ) {
			$default_address = array();

			foreach ( WC()->customer->get_shipping() as $key => $value ) {
				$default_address[ 'shipping_' . $key ] = $value;
			}

			foreach ( WC()->customer->get_billing() as $key => $value ) {
				$default_address[ 'billing_' . $key ] = $value;
			}

			wcms_session_set( 'user_default_address', $default_address );
		}

		$fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

		$cart->get_cart_from_session();
		$cart_items = wcms_get_real_cart_items();

		$data = array();
		$rel  = array();

		if ( isset( $_POST['items'] ) ) {

			$items = wc_clean( wp_unslash( $_POST['items'] ) );

			if ( ! is_array( $items ) || ! $this->validate_items_quantity( $items ) ) {
				wc_add_notice( __( 'The quantity on shipping addresses does not match with real cart quantity. Please make sure all item quantity are set.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
				wp_safe_redirect( get_permalink( wc_get_page_id( 'multiple_addresses' ) ) );
				exit;
			}

			// handler for quantities update.
			foreach ( $items as $cart_key => $item ) {
				$qtys           = $item['qty'];
				$item_addresses = $item['address'];
				$cart_items     = wcms_get_real_cart_items();
				$new_qty        = 0;

				if ( empty( $cart_items[ $cart_key ] ) ) {
					continue;
				}

				foreach ( $item_addresses as $idx => $item_address ) {
					if ( intval( $qtys[ $idx ] ) > 0 ) {
						// decrement the cart item quantity by one.
						$new_qty += intval( $qtys[ $idx ] );
					}
				}

				$cart->set_quantity( $cart_key, $new_qty );
			}

			$cart_items = wcms_get_real_cart_items();
			foreach ( $items as $cart_key => $item ) {
				$qtys           = $item['qty'];
				$item_addresses = $item['address'];

				if ( empty( $cart_items[ $cart_key ] ) ) {
					continue;
				}

				$product_id = $cart_items[ $cart_key ]['product_id'];
				$sig        = $cart_key . '_' . $product_id . '_';
				$_sig       = '';

				foreach ( $item_addresses as $idx => $item_address ) {
					$address_id   = $item_address;
					$user_address = $user_addresses[ $address_id ];

					$i = 1;
					for ( $x = 0; $x < $qtys[ $idx ]; $x++ ) {

						$rel[ $address_id ][] = $cart_key;

						while ( isset( $data[ 'shipping_first_name_' . $sig . $i ] ) ) {
							++$i;
						}
						$_sig = $sig . $i;

						if ( $fields ) {
							foreach ( $fields as $key => $field ) :
								$data[ $key . '_' . $_sig ] = $user_address[ $key ];
						endforeach;
						}
					}
				}

				$cart_address_ids_session = (array) wcms_session_get( 'cart_address_ids' );

				if ( ! empty( $_sig ) && ! wcms_session_isset( 'cart_address_ids' ) || ! in_array( $_sig, $cart_address_ids_session, true ) ) {
					$cart_address_sigs_session          = wcms_session_get( 'cart_address_sigs' );
					$cart_address_sigs_session[ $_sig ] = $address_id;
					wcms_session_set( 'cart_address_sigs', $cart_address_sigs_session );
				}
			}
		}

		wcms_session_set( 'cart_item_addresses', $data );
		wcms_session_set( 'address_relationships', $rel );
		wcms_session_set( 'wcms_item_addresses', $rel );

		if ( isset( $_POST['update_quantities'] ) || isset( $_POST['delete_line'] ) ) {
			$next_url = get_permalink( wc_get_page_id( 'multiple_addresses' ) );
		} else {
			// redirect to the checkout page.
			$next_url = wc_get_checkout_url();
		}

		$this->wcms->clear_packages_cache();
		wp_safe_redirect( $next_url );
		exit;
	}

	/**
	 * Saving the shipping addresses to user meta.
	 */
	public function save_account_shipping_addresses() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'shipping_account_address_action' ) ) {
			return;
		}

		if ( ! isset( $_POST['shipping_account_address_action'] ) || 'save' !== $_POST['shipping_account_address_action'] ) {
			return;
		}

		$user = wp_get_current_user();
		$idx  = isset( $_POST['idx'] ) ? intval( $_POST['idx'] ) : 0;

		$addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

		if ( ! is_array( $addresses ) ) {
			$addresses = array();
		}

		if ( -1 === $idx ) {
			$idx = count( $addresses );

			while ( array_key_exists( $idx, $addresses ) ) {
				++$idx;
			}
		}

		unset( $_POST['shipping_account_address_action'], $_POST['set_addresses'], $_POST['idx'] );

		foreach ( $_POST as $key => $value ) {
			$addresses[ $idx ][ $key ] = $value;
		}

		update_user_meta( $user->ID, 'wc_other_addresses', $addresses );

		wc_add_notice( __( 'Address saved', 'woocommerce-shipping-multiple-addresses' ), 'success' );

		$page_id = wc_get_page_id( 'myaccount' );
		wp_safe_redirect( get_permalink( $page_id ) );
		exit;
	}

	/**
	 * Validating the address fields.
	 *
	 * @param array $ship_fields Ship fields.
	 *
	 * @return array.
	 */
	public function validate_addresses_book( $ship_fields ) {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'save_to_address_book' ) ) {
			return;
		}

		if ( ! isset( $_POST['address'] ) ) {
			return;
		}
		// phpcs:ignore WordPress.Security.ValidatedSanitizedInput.InputNotSanitized
		$address = wc_clean( wp_unslash( $_POST['address'] ) );
		$errors  = array();

		foreach ( $ship_fields as $key => $field ) {

			if ( isset( $field['required'] ) && $field['required'] && empty( $address[ $key ] ) ) {
				if ( 'shipping_state' === $key && empty( WC()->countries->get_states( $address['shipping_country'] ) ) ) {
					continue;
				}

				$errors[] = $key;
			}

			if ( ! empty( $address[ $key ] ) ) {

				// Validation rules.
				if ( ! empty( $field['validate'] ) && is_array( $field['validate'] ) ) {
					foreach ( $field['validate'] as $rule ) {
						switch ( $rule ) {
							case 'postcode':
								$address[ $key ] = trim( $address[ $key ] );

								if ( ! WC_Validation::is_postcode( $address[ $key ], $address['shipping_country'] ) ) :
									$errors[] = $key;
									wc_add_notice( __( 'Please enter a valid postcode/ZIP.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								else :
									$address[ $key ] = wc_format_postcode( $address[ $key ], $address['shipping_country'] );
								endif;
								break;
							case 'phone':
								$address[ $key ] = wc_format_phone_number( $address[ $key ] );

								if ( ! WC_Validation::is_phone( $address[ $key ] ) ) {
									$errors[] = $key;

									wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not a valid phone number.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								}

								break;
							case 'email':
								$address[ $key ] = strtolower( $address[ $key ] );

								if ( ! is_email( $address[ $key ] ) ) {
									$errors[] = $key;

									wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not a valid email address.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								}

								break;
							case 'state':
								// Get valid states.
								$valid_states = WC()->countries->get_states( $address['shipping_country'] );
								if ( $valid_states ) {
									$valid_state_values = array_flip( array_map( 'strtolower', $valid_states ) );
								}

								// Convert value to key if set.
								if ( isset( $valid_state_values[ strtolower( $address[ $key ] ) ] ) ) {
									$address[ $key ] = $valid_state_values[ strtolower( $address[ $key ] ) ];
								}

								// Only validate if the country has specific state options.
								if ( is_array( $valid_states ) && count( $valid_states ) > 0 ) {
									if ( ! in_array( $address[ $key ], array_keys( $valid_states ), true ) ) {
										$errors[] = $key;

										wc_add_notice( '<strong>' . $field['label'] . '</strong> ' . __( 'is not valid. Please enter one of the following:', 'woocommerce-shipping-multiple-addresses' ) . ' ' . implode( ', ', $valid_states ), 'error' );
									}
								}
								break;
						}
					}
				}
			}
		}

		return array(
			'errors'  => $errors,
			'address' => $address,
		);
	}

	/**
	 * Save addresses book from POST.
	 */
	public function save_addresses_book_from_post() {
		if ( ! isset( $_POST['_wpnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wpnonce'] ), 'save_to_address_book' ) ) {
			return;
		}

		if ( ! isset( $_POST['id'] ) || ! isset( $_POST['address'] ) ) {
			return;
		}

		$user         = wp_get_current_user();
		$id           = intval( $_POST['id'] );
		$address      = wc_clean( wp_unslash( $_POST['address'] ) );
		$addresses    = $this->get_user_addresses( $user );
		$ship_fields  = WC()->countries->get_address_fields( $address['shipping_country'], 'shipping_' );
		$redirect_url = ( isset( $_POST['next'] ) ) ? esc_url_raw( wp_unslash( $_POST['next'] ) ) : get_permalink( wc_get_page_id( 'multiple_addresses' ) );

		$validation = $this->validate_addresses_book( $ship_fields );

		if ( count( $validation['errors'] ) > 0 ) {
			wc_add_notice( __( 'Please enter the complete address', 'woocommerce-shipping-multiple-addresses' ), 'error' );
			$next = add_query_arg( $address, $redirect_url );
			$next = add_query_arg( 'address-form', 1, $next );
			wp_safe_redirect( esc_url( $next ) );
			exit;
		}

		$address = $validation['address'];

		// address is unique, save!
		if ( -1 === $id ) {
			$vals = '';
			foreach ( $address as $key => $value ) {
				$vals .= $value;
			}
			$md5 = md5( $vals );

			foreach ( $addresses as $addr ) {
				$vals = '';
				if ( ! is_array( $addr ) ) {
					continue; }
				foreach ( $addr as $key => $value ) {
					$vals .= $value;
				}
				$addr_md5 = md5( $vals );

				if ( $md5 === $addr_md5 ) {
					// duplicate address!
					wc_add_notice( __( 'Address is already in your address book', 'woocommerce-shipping-multiple-addresses' ), 'error' );
					$next = add_query_arg( $address, $redirect_url );
					$next = add_query_arg( 'address-form', 1, $next );
					wp_safe_redirect( esc_url( $next ) );
					exit;
				}
			}

			$addresses[] = $address;
		} else {
			$addresses[ $id ] = $address;
		}

		// Update the default address and remove it from the $addresses array.
		if ( $user->ID > 0 ) {
			if ( 0 === $id ) {
				$default_address = $addresses[0];
				unset( $addresses[0] );

				if ( $default_address['shipping_address_1'] && $default_address['shipping_postcode'] ) {
					update_user_meta( $user->ID, 'shipping_first_name', $default_address['shipping_first_name'] );
					update_user_meta( $user->ID, 'shipping_last_name', $default_address['shipping_last_name'] );
					update_user_meta( $user->ID, 'shipping_company', $default_address['shipping_company'] );
					update_user_meta( $user->ID, 'shipping_address_1', $default_address['shipping_address_1'] );
					update_user_meta( $user->ID, 'shipping_address_2', $default_address['shipping_address_2'] );
					update_user_meta( $user->ID, 'shipping_city', $default_address['shipping_city'] );
					update_user_meta( $user->ID, 'shipping_state', $default_address['shipping_state'] );
					update_user_meta( $user->ID, 'shipping_postcode', $default_address['shipping_postcode'] );
					update_user_meta( $user->ID, 'shipping_country', $default_address['shipping_country'] );
				}
				unset( $addresses[0] );
			}
		}

		$this->save_user_addresses( $user->ID, $addresses );

		if ( $id >= 0 ) {
			$next = add_query_arg( 'updated', '1', $redirect_url );
		} else {
			$next = add_query_arg( 'new', '1', $redirect_url );
		}

		wp_safe_redirect( esc_url( $next ) );
		exit;
	}

	/**
	 * Delete address using ajax.
	 */
	public function ajax_delete_address() {
		if ( ! isset( $_POST['_wcmsnonce'] ) || ! wp_verify_nonce( sanitize_key( $_POST['_wcmsnonce'] ), 'wcms-action_' . WC()->session->get_customer_unique_id() ) ) {
			exit;
		}

		if ( ! isset( $_POST['idx'] ) ) {
			exit;
		}

		$user      = wp_get_current_user();
		$idx       = absint( sanitize_text_field( wp_unslash( $_POST['idx'] ) ) );
		$addresses = $this->get_user_addresses( $user );

		unset( $addresses[ $idx ] );

		$this->save_user_addresses( $user->ID, $addresses );

		wp_send_json( array( 'ack' => 'OK' ) );
		exit;
	}

	/**
	 * Get user addresses from user meta.
	 *
	 * @param WP_User $user User object.
	 * @param boolean $include_default Include default address from WooCommerce.
	 *
	 * @return array.
	 */
	public function get_user_addresses( $user, $include_default = true ) {
		if ( ! $user instanceof WP_User ) {
			$user = new WP_User( $user );
		}

		if ( 0 !== $user->ID ) {
			$addresses = get_user_meta( $user->ID, 'wc_other_addresses', true );

			if ( ! $addresses ) {
				$addresses = array();
			}

			if ( $include_default ) {
				$default_address = $this->get_user_default_address( $user->ID );

				if ( $default_address['address_1'] && $default_address['postcode'] ) {
					array_unshift( $addresses, $default_address );
				}
			}
		} else {
			// Guest address - using sessions to store the address.
			$addresses = ( wcms_session_isset( 'user_addresses' ) ) ? wcms_session_get( 'user_addresses' ) : array();
		}

		return $this->array_sort( $addresses, 'shipping_first_name' );
	}

	/**
	 * Get available user addresses by filtering the user addresses using avaiable shipping countries.
	 *
	 * @param WP_User $user            User object.
	 * @param boolean $include_default Include default address from WooCommerce.
	 *
	 * @return array.
	 */
	public function get_available_user_addresses( $user, $include_default = true ) {
		$addresses           = $this->get_user_addresses( $user, $include_default );
		$available_countries = array_keys( WC()->countries->get_shipping_countries() );

		return array_filter(
			$addresses,
			function( $addr ) use ( $available_countries ) {
				return isset( $addr['shipping_country'] ) && in_array( $addr['shipping_country'], $available_countries, true );
			}
		);
	}

	/**
	 * Sorting the array.
	 *
	 * @param array    $existing_array Array value.
	 * @param string   $on type of address field.
	 * @param constant $order type of sorting.
	 *
	 * @return array Sorted array.
	 */
	public function array_sort( $existing_array, $on, $order = SORT_ASC ) {
		$new_array      = array();
		$sortable_array = array();

		if ( is_array( $existing_array ) && 0 < count( $existing_array ) ) {
			foreach ( $existing_array as $k => $v ) {
				if ( is_array( $v ) ) {
					foreach ( $v as $k2 => $v2 ) {
						if ( $k2 === $on ) {
							$sortable_array[ $k ] = $v2;
						}
					}
				} else {
					$sortable_array[ $k ] = $v;
				}
			}

			switch ( $order ) {
				case SORT_ASC:
					asort( $sortable_array, SORT_NATURAL | SORT_FLAG_CASE );
					break;
				case SORT_DESC:
					arsort( $sortable_array, SORT_NATURAL | SORT_FLAG_CASE );
					break;
			}

			foreach ( $sortable_array as $k => $v ) {
				$new_array[ $k ] = $existing_array[ $k ];
			}
		}

		return $new_array;
	}

	/**
	 * Save user addresses to account or session
	 * Removes the default addresses and any duplicate addresses
	 *
	 * @param  integer $user_id    Customer user ID.
	 * @param  array   $addresses  List of user addresses.
	 */
	public function save_user_addresses( $user_id, $addresses ) {

		$keys = array();

		foreach ( $addresses as $index => $address ) {
			$key = $this->unique_address_key( $address );

			if ( ! empty( $address['default_address'] ) ) {
				// Remove default address.
				unset( $addresses[ $index ] );
			} elseif ( false !== $key ) {
				// Save unique address key.
				$keys[ $index ] = $key;
			} else {
				// Remove empty address.
				unset( $addresses[ $index ] );
			}
		}

		// Remove any duplicate addresses.
		$duplicates = array_diff_assoc( $keys, array_unique( $keys ) );
		foreach ( array_keys( $duplicates ) as $index ) {
			unset( $addresses[ $index ] );
		}

		if ( $user_id > 0 ) {
			update_user_meta( $user_id, 'wc_other_addresses', $addresses );
		} else {
			wcms_session_set( 'user_addresses', $addresses );
		}
	}

	/**
	 * Check if the checkout address fields is eligible for saving or not.
	 *
	 * @return boolean.
	 */
	public function is_checkout_address_eligible_for_saving() {
		$shipping_address_fields = wc()->checkout->get_checkout_fields( 'shipping' );
		$count_unempty_address   = 0;

		foreach ( $shipping_address_fields as $key => $value ) {
			$address_value = wc()->checkout->get_value( $key );
			if ( empty( $address_value ) || 'phone' === $key ) {
				continue;
			}

			$count_unempty_address++;
		}

		// At least 5 address fields are not empty.
		return $count_unempty_address >= 5;
	}

	/**
	 * Save checkout address value.
	 *
	 * @param int $user_id User ID.
	 *
	 * @return void.
	 */
	public function save_checkout_address( int $user_id ) {
		$shipping_address_fields = wc()->checkout->get_checkout_fields( 'shipping' );

		if ( ! $this->is_checkout_address_eligible_for_saving() ) {
			return;
		}

		if ( 0 !== $user_id ) {
			// For logged-in user.
			$customer = new WC_Customer( $user_id );

			foreach ( $shipping_address_fields as $key => $value ) {
				$address_value = wc()->checkout->get_value( $key );
				$customer->{ 'set_' . $key }( $address_value );
			}

			$customer->save();
		} else {
			// For guest user.
			$addresses = array();
			foreach ( $shipping_address_fields as $key => $value ) {
				$address_value = wc()->checkout->get_value( $key );
				$addresses[0][ $key ] = $address_value;
			}
			
			$this->save_user_addresses( $user_id, $addresses );
		}
	}

	/**
	 * Check if user has saved shipping address or not.
	 *
	 * @param int $user_id User ID.
	 *
	 * @return boolean.
	 */
	public function user_has_shipping_address( int $user_id ) {
		$customer        = new WC_Customer( $user_id );
		$unempty_address = array_filter(
			$customer->get_shipping(),
			function( $shipping_value, $key ) {
				return ! empty( $shipping_value ) && 'phone' !== $key;
			},
			ARRAY_FILTER_USE_BOTH
		);

		// At least 5 address fields not empty to be valid.
		return count( $unempty_address ) >= 5;
	}

	/**
	 * Generate a unique key for an address
	 *
	 * @param  array $address Address field values.
	 *
	 * @return string Unique key
	 */
	public function unique_address_key( $address ) {

		if ( empty( $address ) || ! is_array( $address ) ) {
			return false;
		}

		return md5( implode( '_', $address ) );
	}
}
</file>

<file path="includes/class-wc-ms-front.php">
<?php
/**
 * Class WC_MS_Front file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * WC_MS_Front class.
 */
class WC_MS_Front {

	/**
	 * WC_Ship_Multiple object.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Country
	 *
	 * @var string
	 */
	private $country;

	/**
	 * Class constructor.
	 *
	 * @param WC_Ship_Multiple $wcms WC_Ship_Multiple object.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {
		$this->wcms = $wcms;

		// WCMS Front.
		add_filter( 'body_class', array( $this, 'output_body_class' ) );
		add_action( 'wp_enqueue_scripts', array( $this, 'front_scripts' ), 11 );
		add_action( 'woocommerce_email_after_order_table', array( $this, 'email_order_item_addresses' ), 10, 3 );
		add_action( 'woocommerce_order_details_after_order_table', array( $this, 'list_order_item_addresses' ) );

		// cleanup.
		add_action( 'wp_logout', array( $this->wcms, 'clear_session' ) );

		add_action( 'plugins_loaded', array( $this, 'load_account_addresses' ), 11 );

		// inline script.
		add_action( 'wp_footer', array( $this, 'inline_scripts' ) );
	}

	/**
	 * Load account addresses by adding a filter and action hooks.
	 */
	public function load_account_addresses() {
		add_filter( 'woocommerce_my_account_get_addresses', array( $this, 'account_address_labels' ), 10, 2 );
		add_filter( 'woocommerce_my_account_my_address_formatted_address', array( $this, 'account_address_formatted' ), 10, 3 );

		add_filter( 'woocommerce_my_account_edit_address_field_value', array( $this, 'edit_address_field_value' ), 10, 3 );
		add_action( 'template_redirect', array( $this, 'save_address' ), 1 );

		// Delete address in edit address page.
		add_action( 'woocommerce_before_edit_account_address_form', array( $this, 'delete_address_button' ) );
		add_action( 'wp_loaded', array( $this, 'delete_address_action' ), 20 );

		// Add address button on my account addresses page.
		add_action( 'woocommerce_account_edit-address_endpoint', array( $this, 'add_address_button' ), 90 );

		// Initialize address fields.
		add_action( 'woocommerce_account_content', array( $this, 'init_address_fields' ), 1 );
	}

	/**
	 * Add woocommerce and woocommerce-page classes to the body tag of WCMS pages
	 *
	 * @param array $classes Registered body css class.
	 *
	 * @return array
	 */
	public function output_body_class( $classes ) {
		if ( is_page( wc_get_page_id( 'multiple_addresses' ) ) || is_page( wc_get_page_id( 'account_addresses' ) ) ) {
			$classes[] = 'woocommerce';
			$classes[] = 'woocommerce-page';
		}

		if ( WC_Blocks_Utils::has_block_in_page( get_the_ID(), 'woocommerce/checkout' ) ) {
			$classes[] = 'wcms-checkout-blocks';
		}

		if ( WC_Blocks_Utils::has_block_in_page( get_the_ID(), 'woocommerce/cart' ) ) {
			$classes[] = 'wcms-cart-blocks';
		}

		if ( $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() ) {
			$classes[] = 'wcms-activated';
		}

		return $classes;
	}

	/**
	 * Enqueue scripts and styles for the frontend
	 */
	public function front_scripts() {
		global $post;

		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		$page_ids = array(
			wc_get_page_id( 'account_addresses' ),
			wc_get_page_id( 'multiple_addresses' ),
			wc_get_page_id( 'myaccount' ),
			wc_get_page_id( 'checkout' ),
			wc_get_page_id( 'cart' ),
		);

		if ( ! $post || ( $post && ! in_array( $post->ID, $page_ids, true ) ) ) {
			return;
		}

		$user = wp_get_current_user();

		wp_enqueue_script( 'jquery' );
		wp_enqueue_script( 'jquery-ui-core' );
		wp_enqueue_script( 'jquery-ui-mouse' );
		wp_enqueue_script( 'jquery-ui-draggable' );
		wp_enqueue_script( 'jquery-ui-droppable' );
		wp_enqueue_script( 'jquery-ui-datepicker' );
		wp_enqueue_script( 'jquery-masonry' );
		wp_enqueue_script( 'thickbox' );
		wp_enqueue_style( 'thickbox' );
		wp_enqueue_script( 'jquery-blockui' );

		// Touchpunch to support mobile browsers.
		wp_enqueue_script( 'jquery-ui-touch-punch', plugins_url( 'assets/js/jquery.ui.touch-punch' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery-ui-mouse', 'jquery-ui-widget' ), '0.2.3' );// phpcs:ignore --- This third party library need to be on top.

		if ( 0 !== $user->ID ) {
			$page_id = wc_get_page_id( 'account_addresses' );
			$url     = get_permalink( $page_id );
			$url     = add_query_arg( 'height', '400', add_query_arg( 'width', '400', add_query_arg( 'addressbook', '1', $url ) ) );
			?>
			<script type="text/javascript">
				var address = null;
				var wc_ship_url = '<?php echo esc_url( $url ); ?>';
			</script>
			<?php
		}

		wp_enqueue_script( 'jquery-tiptip', plugins_url( 'assets/js/jquery.tiptip' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery', 'jquery-ui-core' ), '1.3' );// phpcs:ignore --- This third party library need to be on top.

		wp_enqueue_script( 'modernizr', plugins_url( 'assets/js/modernizr' . $suffix . '.js', WC_Ship_Multiple::FILE ), array(), '2.6.2' );// phpcs:ignore --- This third party library need to be on top.

		$id                = wc_get_page_id( 'multiple_addresses' );
		$reset_url         = add_query_arg(
			array(
				'wcms_reset_address' => true,
				'nonce'              => wp_create_nonce( 'wcms_reset_address_security' ),
			),
			wc_get_checkout_url()
		);
		$modify_addr_link  = get_permalink( $id );
		$add_addr_link     = add_query_arg( 'cart', 1, get_permalink( $id ) );
		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		$sess_cart_address = wcms_session_get( 'cart_addresses' );
		$has_item_address  = ( ! wcms_session_isset( 'cart_item_addresses' ) || empty( $sess_item_address ) ) ? false : true;
		$has_cart_address  = ( ! wcms_session_isset( 'cart_addresses' ) || empty( $sess_cart_address ) ) ? false : true;
		$no_multi_address  = ( ! $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() );

		wp_enqueue_script( 'multiple_shipping_checkout', plugins_url( 'assets/js/woocommerce-checkout' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'woocommerce', 'jquery-ui-draggable', 'jquery-ui-droppable', 'jquery-ui-mouse' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
		wp_localize_script(
			'multiple_shipping_checkout',
			'WCMS',
			/**
			 * Filter to manipulate WCMS js variable on checkout page.
			 *
			 * @param array List of JS variable.
			 *
			 * @since 3.3.16
			 */
			apply_filters(
				'wc_ms_checkout_locale',
				array(
					// URL to wp-admin/admin-ajax.php to process the request.
					'ajaxurl'           => admin_url( 'admin-ajax.php' ),
					'base_url'          => plugins_url( '', WC_Ship_Multiple::FILE ),
					'wc_url'            => WC()->plugin_url(),
					'countries'         => wp_json_encode( array_merge( WC()->countries->get_allowed_country_states(), WC()->countries->get_shipping_country_states() ) ),
					'select_state_text' => esc_attr__( 'Select an option&hellip;', 'woocommerce-shipping-multiple-addresses' ),
					'_wcmsnonce'        => wp_create_nonce( 'wcms-action_' . WC()->session->get_customer_unique_id() ),
					'not_eligible_wcms' => ( ! $this->wcms->cart->cart_is_eligible_for_multi_shipping() ),
					'has_item_address'  => $has_item_address,
					'has_cart_address'  => $has_cart_address,
					'no_multi_address'  => $no_multi_address,
					'reset_url'         => esc_url( $reset_url ),
					'modify_addr_link'  => esc_url( $modify_addr_link ),
					'add_addr_link'     => esc_url( $add_addr_link ),
					'save_nonce'        => wp_create_nonce( 'wcms_save_billing_fields_nonce' ),
					'modify_title_text' => esc_html__( 'Shipping Address', 'woocommerce-shipping-multiple-addresses' ),
					'modify_addr_text'  => esc_html__( 'Modify/Add Address', 'woocommerce-shipping-multiple-addresses' ),
					'reset_addr_text'   => esc_html__( 'Reset Address', 'woocommerce-shipping-multiple-addresses' ),
				)
			)
		);

		if ( ! is_checkout() ) {
			wp_register_script( 'wcms-country-select', plugins_url( 'assets/js/country-select' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery', 'selectWoo', 'select2' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
			wp_localize_script(
				'wcms-country-select',
				'wcms_country_select_params',
				/**
				 * Filter to manipulate the countries parameters on JS.
				 *
				 * @param array JSON encoded list of countries and select text.
				 *
				 * @since 3.3.19
				 */
				apply_filters(
					'wc_country_select_params',
					array(
						'countries'              => wp_json_encode( array_merge( WC()->countries->get_allowed_country_states(), WC()->countries->get_shipping_country_states() ) ),
						'i18n_select_state_text' => esc_attr__( 'Select an option&hellip;', 'woocommerce-shipping-multiple-addresses' ),
					)
				)
			);
			wp_enqueue_script( 'wc-address-i18n' );
			wp_enqueue_script( 'wcms-country-select' );
			wp_enqueue_style( 'select2', WC()->plugin_url() . '/assets/css/select2.css', array(), WC_VERSION );
		}

		wp_enqueue_style( 'multiple_shipping_styles', plugins_url( 'assets/css/front.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );
		wp_enqueue_style( 'tiptip', plugins_url( 'assets/css/jquery.tiptip.css', WC_Ship_Multiple::FILE ), array(), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION );

		global $wp_scripts;
		$ui_version = $wp_scripts->registered['jquery-ui-core']->ver;
		wp_enqueue_style( 'jquery-ui-css', "//ajax.googleapis.com/ajax/libs/jqueryui/{$ui_version}/themes/ui-lightness/jquery-ui.min.css", array(), $ui_version );

		// Address validation support.
		if ( class_exists( 'WC_Address_Validation' ) && is_page( wc_get_page_id( 'multiple_addresses' ) ) ) {
			$this->enqueue_address_validation_scripts();
		}

		// On the thank you page, remove the Shipping Address block if the order ships to multiple addresses.
		$order_id = isset( $_GET['view-order'] ) ? intval( $_GET['view-order'] ) : 0; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$order_id = isset( $_GET['order-received'] ) ? intval( $_GET['order-received'] ) : $order_id; // phpcs:ignore WordPress.Security.NonceVerification.Recommended

		if ( ! empty( $order_id ) ) {
			$order = wc_get_order( $order_id );

			if ( ! $order ) {
				return;
			}

			$packages  = $order->get_meta( '_wcms_packages' );
			$multiship = $order->get_meta( '_multiple_shipping' );

			if ( ( is_array( $packages ) && 1 < count( $packages ) ) || 'yes' === $multiship ) {
				wp_enqueue_script( 'wcms_shipping_address_override', plugins_url( 'assets/js/address-override' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
			}
		}

		wp_register_script( 'wcms-address-selection', plugins_url( 'assets/js/address-selection' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'woocommerce' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );
	}

	/**
	 * Address Validation scripts
	 */
	public function enqueue_address_validation_scripts() {
		$suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

		if ( function_exists( 'wc_address_validation' ) ) {
			$validator = wc_address_validation();
			$handler   = $validator->get_handler_instance();
		} else {
			$validator = $GLOBALS['wc_address_validation'];
			$handler   = $validator->handler;
		}

		$params = array(
			'nonce'                 => wp_create_nonce( 'wc_address_validation' ),
			'debug_mode'            => 'yes' === get_option( 'wc_address_validation_debug_mode' ),
			'force_postcode_lookup' => 'yes' === get_option( 'wc_address_validation_force_postcode_lookup' ),
			'ajax_url'              => admin_url( 'admin-ajax.php', 'relative' ),
		);

		// Load postcode lookup JS.
		$provider = $handler->get_active_provider();

		if ( $provider && $provider->supports( 'postcode_lookup' ) ) {
			wp_enqueue_script( 'wc_address_validation_postcode_lookup', $validator->get_plugin_url() . '/assets/js/frontend/wc-address-validation-postcode-lookup' . $suffix . '.js', array( 'jquery', 'woocommerce' ), WC_Address_Validation::VERSION, true );
			wp_localize_script( 'wc_address_validation_postcode_lookup', 'wc_address_validation_postcode_lookup', $params );
		}

		// Load address validation JS.
		if ( $provider && $provider->supports( 'address_validation' ) && 'WC_Address_Validation_Provider_SmartyStreets' === get_class( $provider ) ) {

			// Load SmartyStreets LiveAddress jQuery plugin.
			wp_enqueue_script( 'wc_address_validation_smarty_streets', '//d79i1fxsrar4t.cloudfront.net/jquery.liveaddress/2.4/jquery.liveaddress.min.js', array( 'jquery' ), '2.4', true );

			wp_enqueue_script( 'wcms_address_validation', plugins_url( 'assets/js/address-validation' . $suffix . '.js', WC_Ship_Multiple::FILE ), array( 'jquery' ), WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION, true );

			$params['smarty_streets_key'] = $provider->html_key;

			wp_localize_script( 'wcms_address_validation', 'wc_address_validation', $params );

			// Add a bit of CSS to fix address correction popup from expanding to page width because of Chosen selects.
			echo '<style type="text/css">.chzn-done{position:absolute!important;visibility:hidden!important;display:block!important;width:120px!important;</style>';
		}

		/**
		 * Allow other providers to load JS.
		 *
		 * @param array  $provider Third party provider.
		 * @param object $handler Validator handler.
		 * @param string $suffix Suffix of the script or style file. Usually '.min'.
		 *
		 * @since 3.3.16
		 */
		do_action( 'wc_address_validation_load_js', $provider, $handler, $suffix );
	}

	/**
	 * Prints the email table of items and their shipping addresses.
	 *
	 * @param WC_Order $order Order object or order ID.
	 * @param boolean  $sent_to_admin Whether it is sent to admin or not.
	 * @param boolean  $plain_text Whether the format is plain or HTML.
	 */
	public function email_order_item_addresses( $order, $sent_to_admin, $plain_text ) {
		/**
		 * Action to add element on shipping package table.
		 *
		 * @param int     $order_id Order ID.
		 * @param boolean Is email or not.
		 * @param boolean Is plain text or not.
		 *
		 * @since 2.1.8
		 */
		do_action( 'wcms_order_shipping_packages_table', $order, true, $plain_text );
	}

	/**
	 * Prints the table of items and their shipping addresses.
	 *
	 * @param int|WC_Order $order_id Order ID or order object.
	 */
	public function list_order_item_addresses( $order_id ) {
		/**
		 * Action to add element on shipping package table.
		 *
		 * @param int     $order_id Order ID.
		 * @param boolean Is email or not.
		 * @param boolean Is plain text or not.
		 *
		 * @since 2.1.8
		 */
		do_action( 'wcms_order_shipping_packages_table', $order_id, false, false );
	}

	/**
	 * Get account address labels.
	 *
	 * @param array $labels Address labels.
	 * @param int   $customer_id Customer ID.
	 *
	 * @return array
	 */
	public function account_address_labels( $labels, $customer_id ) {
		$user      = get_user_by( 'id', $customer_id );
		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		$address_id = 0;

		foreach ( $addresses as $index => $address ) {
			++$address_id;

			// translators: %d is address id.
			$labels[ 'wcms_address_' . $index ] = sprintf( __( 'Shipping address %d', 'woocommerce-shipping-multiple-addresses' ), $address_id );
		}

		return $labels;
	}

	/**
	 * Get account address in formatted array.
	 *
	 * @param array  $address Current address.
	 * @param int    $customer_id Customer ID.
	 * @param string $address_id Address ID.
	 *
	 * @return array
	 */
	public function account_address_formatted( $address, $customer_id, $address_id ) {
		if ( strpos( $address_id, 'wcms_address_' ) === 0 ) {
			$user      = get_user_by( 'id', $customer_id );
			$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

			$parts = explode( '_', $address_id );
			$index = $parts[2];

			if ( isset( $addresses[ $index ] ) ) {
				$account_address = $addresses[ $index ];

				foreach ( $account_address as $key => $value ) {
					$key                     = str_replace( 'shipping_', '', $key );
					$account_address[ $key ] = $value;
				}

				$address = $account_address;
			}
		}

		return $address;
	}

	/**
	 * Edit the address field value.
	 *
	 * @param mixed  $value Field value.
	 * @param string $key Field key.
	 * @param string $load_address Load address.
	 *
	 * @return mixed
	 */
	public function edit_address_field_value( $value, $key, $load_address ) {
		if ( strpos( $load_address, 'wcms_address_' ) === 0 ) {
			$parts = explode( '_', $load_address );
			$index = $parts[2];

			if ( 'new' === $index ) {
				// No need to do nonce verification. No saving data operation here.
				return empty( $_POST[ $key ] ) ? '' : wc_clean( $_POST[ $key ] );// phpcs:ignore
			}

			$user      = wp_get_current_user();
			$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

			if ( ! isset( $addresses[ $index ] ) ) {
				return $value;
			}

			$key   = str_replace( $load_address, 'shipping', $key );
			$value = $addresses[ $index ][ $key ];
		}

		return $value;
	}

	/**
	 * Save and update a billing or shipping address if the
	 * form was submitted through the user account page.
	 * Copied from WC_Form_Handler::save_address and modified to save to address book
	 */
	public function save_address() {
		global $wp;

		$request_method = isset( $_SERVER['REQUEST_METHOD'] ) ? sanitize_text_field( wp_unslash( $_SERVER['REQUEST_METHOD'] ) ) : '';
		if ( 'POST' !== strtoupper( $request_method ) ) {
			return;
		}

		$nonce = isset( $_POST['woocommerce-edit-address-nonce'] ) ? sanitize_text_field( wp_unslash( $_POST['woocommerce-edit-address-nonce'] ) ) : '';
		if ( ! wp_verify_nonce( $nonce, 'woocommerce-edit_address' ) ) {
			return;
		}

		if ( empty( $_POST['action'] ) || 'edit_address' !== $_POST['action'] ) {
			return;
		}

		$user_id = get_current_user_id();

		if ( $user_id <= 0 ) {
			return;
		}

		$load_address = isset( $wp->query_vars['edit-address'] ) ? wc_edit_address_i18n( sanitize_title( $wp->query_vars['edit-address'] ), true ) : 'billing';

		// Only save our own addresses.
		if ( strpos( $load_address, 'wcms_address_' ) !== 0 ) {
			return;
		}

		$shipping_country  = isset( $_POST['shipping_country'] ) ? sanitize_text_field( wp_unslash( $_POST['shipping_country'] ) ) : '';
		$address           = WC()->countries->get_address_fields( esc_attr( $shipping_country ), 'shipping_' );
		$post_load_country = isset( $_POST[ $load_address . '_country' ] ) ? wc_clean( wp_unslash( $_POST[ $load_address . '_country' ] ) ) : '';
		$post_fields       = array();

		foreach ( $address as $key => $field ) {

			if ( ! isset( $field['type'] ) ) {
				$field['type'] = 'text';
			}

			// Get Value.
			switch ( $field['type'] ) {
				case 'checkbox':
					$post_fields[ $key ] = intval( isset( $_POST[ $key ] ) );
					break;
				default:
					$post_fields[ $key ] = isset( $_POST[ $key ] ) ? wc_clean( wp_unslash( $_POST[ $key ] ) ) : '';
					break;
			}

			/**
			 * Filter to manipulate the POST field value.
			 *
			 * @param mixed POST value of the field.
			 *
			 * @since 3.3.18
			 */
			$post_fields[ $key ] = apply_filters( 'woocommerce_process_myaccount_field_' . $key, $post_fields[ $key ] );

			// Validation: Required fields.
			if ( ! empty( $field['required'] ) && empty( $post_fields[ $key ] ) ) {
				// translators: %s is a field label.
				wc_add_notice( sprintf( __( '%s is a required field.', 'woocommerce-shipping-multiple-addresses' ), $field['label'] ), 'error' );
			}

			if ( ! empty( $post_fields[ $key ] ) ) {

				// Validation rules.
				if ( ! empty( $field['validate'] ) && is_array( $field['validate'] ) ) {
					foreach ( $field['validate'] as $rule ) {
						switch ( $rule ) {
							case 'postcode':
								$post_fields[ $key ] = trim( $post_fields[ $key ] );

								if ( empty( $post_load_country ) ) {
									continue 2;
								}

								if ( ! WC_Validation::is_postcode( $post_fields[ $key ], $post_load_country ) ) {
									wc_add_notice( esc_html__( 'Please enter a valid postcode / ZIP.', 'woocommerce-shipping-multiple-addresses' ), 'error' );
								} else {
									$post_fields[ $key ] = wc_format_postcode( $post_fields[ $key ], $post_load_country );
								}
								break;
							case 'phone':
								$post_fields[ $key ] = wc_format_phone_number( $post_fields[ $key ] );

								if ( ! WC_Validation::is_phone( $post_fields[ $key ] ) ) {
									// translators: %s is a label name.
									wc_add_notice( sprintf( esc_html__( '%s is not a valid phone number.', 'woocommerce-shipping-multiple-addresses' ), '<strong>' . $field['label'] . '</strong>' ), 'error' );
								}
								break;
							case 'email':
								$post_fields[ $key ] = strtolower( $post_fields[ $key ] );

								if ( ! is_email( $post_fields[ $key ] ) ) {
									// translators: %s is a field label.
									wc_add_notice( sprintf( esc_html__( '%s is not a valid email address.', 'woocommerce-shipping-multiple-addresses' ), '<strong>' . $field['label'] . '</strong>' ), 'error' );
								}
								break;
						}
					}
				}
			}
		}

		$customer = new WC_Customer( $user_id );

		/**
		 * Hook: woocommerce_after_save_address_validation.
		 *
		 * Allow developers to add custom validation logic and throw an error to prevent save.
		 *
		 * @since 3.6.0
		 * @param int         $user_id User ID being saved.
		 * @param string      $load_address Type of address; 'billing' or 'shipping'.
		 * @param array       $address The address fields.
		 * @param WC_Customer $customer The customer object being saved.
		 */
		do_action( 'woocommerce_after_save_address_validation', $user_id, $load_address, $address, $customer );

		if ( 0 === wc_notice_count( 'error' ) ) {

			$user        = new WP_User( $user_id );
			$addresses   = $this->wcms->address_book->get_user_addresses( $user, false );
			$parts       = explode( '_', $load_address );
			$index       = $parts[2];
			$new_address = array();

			foreach ( $address as $key => $field ) {
				$new_address[ $key ] = sanitize_text_field( wp_unslash( $_POST[ $key ] ) );
			}

			if ( 'new' === $index ) {
				$addresses[] = $new_address;
				end( $addresses );
				$index = key( $addresses );
				wc_add_notice( __( 'Address added successfully.', 'woocommerce-shipping-multiple-addresses' ) );
			} else {
				$addresses[ $index ] = $new_address;
				wc_add_notice( __( 'Address changed successfully.', 'woocommerce-shipping-multiple-addresses' ) );
			}

			$default_address = $this->wcms->address_book->get_user_default_address( $user->ID );

			if ( $default_address['address_1'] && $default_address['postcode'] ) {
				array_unshift( $addresses, $default_address );
			}

			$this->wcms->address_book->save_user_addresses( $user_id, $addresses );

			/**
			 * Action after user address is saved to WCMS address book.
			 *
			 * @param int    $user_id User ID.
			 * @param string $load_address Address key that's being loaded.
			 *
			 * @since 3.3.18
			 */
			do_action( 'woocommerce_customer_save_address', $user_id, $load_address );

			wp_safe_redirect( wc_get_endpoint_url( 'edit-address', '', wc_get_page_permalink( 'myaccount' ) ) );
			exit;
		}

		// Prevent WC_Form_Handler::save_address.
		unset( $_POST['action'] );
	}

	/**
	 * Generate inline scripts for wp_footer
	 */
	public function inline_scripts() {
		$order_id = isset( $_GET['order'] ) ? intval( $_GET['order'] ) : false; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		$order    = wc_get_order( $order_id );

		if ( $order ) {
			if ( method_exists( $order, 'get_checkout_order_received_url' ) ) {
				$page_id = $order->get_checkout_order_received_url();
			} else {
				$page_id = wc_get_page_id( get_option( 'woocommerce_thanks_page_id', 'thanks' ) );
			}

			$shipping_addresses = $order->get_meta( '_shipping_addresses', false );

			if ( is_page( $page_id ) && ! empty( $shipping_addresses ) ) {
				$html     = '<div>';
				$packages = $order->get_meta( '_wcms_packages' );

				foreach ( $packages as $package ) {
					$html .= '<address>' . wcms_get_formatted_address( $package['destination'] ) . '</address><br /><hr/>';
				}
				$html .= '</div>';
				$html  = str_replace( '"', '\"', $html );
				$html  = str_replace( "\n", ' ', $html );
				?>
				<script type="text/javascript">
					jQuery(document).ready(function() {
						jQuery(jQuery("address")[1]).replaceWith("<?php echo esc_js( $html ); ?>");
					});
				</script>
				<?php
			}
		}
	}

	/**
	 * Add a delete address button on the edit address page
	 */
	public function delete_address_button() {
		$address      = get_query_var( 'edit-address' );
		$edit_address = wc_get_endpoint_url( 'edit-address' );

		// Only show on multiple addresses.
		if ( 0 !== strpos( $address, 'wcms_address_' ) || empty( $edit_address ) ) {
			return;
		}

		$remove_link = wp_nonce_url( add_query_arg( 'remove_address', $address, $edit_address ), 'wcms-delete-address' );
		printf( '<a href="%1$s" class="remove delete-address-button" aria-label="%2$s">&times;</a>', esc_url( $remove_link ), esc_html__( 'Delete address', 'woocommerce-shipping-multiple-addresses' ) );
	}

	/**
	 * Handle the delete address action
	 */
	public function delete_address_action() {
		$nonce = isset( $_GET['_wpnonce'] ) ? sanitize_text_field( wp_unslash( $_GET['_wpnonce'] ) ) : '';
		if ( ! wp_verify_nonce( $nonce, 'wcms-delete-address' ) ) {
			return;
		}

		if ( ! empty( $_GET['remove_address'] ) ) {

			$user = wp_get_current_user();
			if ( $user->ID ) {
				$address   = wc_clean( wp_unslash( $_GET['remove_address'] ) );
				$index     = ( 0 === strpos( $address, 'wcms_address_' ) ) ? substr( $address, 13 ) : '';
				$addresses = $this->wcms->address_book->get_user_addresses( $user );

				if ( isset( $addresses[ $index ] ) ) {
					unset( $addresses[ $index ] );
					$this->wcms->address_book->save_user_addresses( $user->ID, $addresses );
					wc_add_notice( __( 'Deleted address', 'woocommerce-shipping-multiple-addresses' ) );
				} else {
					wc_add_notice( __( 'Address could not be found', 'woocommerce-shipping-multiple-addresses' ), 'error' );
				}

				// Redirect to edit address page.
				wp_safe_redirect( wc_get_account_endpoint_url( 'edit-address' ) );
				exit;
			}
		}
	}

	/**
	 * Add address button on my account page
	 */
	public function add_address_button() {
		$address = get_query_var( 'edit-address' );

		if ( empty( $address ) ) {
			$url = wc_get_endpoint_url( 'edit-address', 'wcms_address_new' );
			printf( '<a href="%s" class="button">%s</a>', esc_url( $url ), esc_html__( 'Add address', 'woocommerce-shipping-multiple-addresses' ) );
		}
	}

	/**
	 * Init address fields
	 */
	public function init_address_fields() {
		$address = get_query_var( 'edit-address' );

		if ( 0 === strpos( $address, 'wcms_address_' ) ) {
			add_filter( 'woocommerce_' . $address . '_fields', array( $this, 'country_address_fields' ), 10, 2 );

			// Override checkout value for states field, see following issue for better way to resolve this:
			// https://github.com/woocommerce/woocommerce/issues/15632.
			add_filter( 'woocommerce_checkout_get_value', array( $this, 'country_address_value' ), 10, 2 );
		}
	}

	/**
	 * Override address fields with country specific ones.
	 *
	 * @param array  $address_fields Address fields.
	 * @param string $country Address country.
	 *
	 * @return string
	 */
	public function country_address_fields( $address_fields, $country ) {
		$address_country = $this->get_address_country();
		if ( false !== $address_country ) {
			$country = $address_country;
		}

		return WC()->countries->get_address_fields( $country, 'shipping_' );
	}


	/**
	 * Override address country field (to show correct list of states).
	 *
	 * @param mixed  $value Filter value.
	 * @param string $input Input field name.
	 *
	 * @return mixed
	 */
	public function country_address_value( $value, $input ) {
		if ( 'shipping_country' === $input ) {
			$country = $this->get_address_country();
			if ( false !== $country ) {
				return $country;
			}
		}
		return $value;
	}


	/**
	 * Helper function to get address country
	 * Saves it in the class to prevent multiple lookups
	 */
	public function get_address_country() {
		if ( ! empty( $this->country ) ) {
			return $this->country;
		}

		$user      = wp_get_current_user();
		$addresses = $this->wcms->address_book->get_user_addresses( $user, false );

		$address = get_query_var( 'edit-address' );
		$parts   = explode( '_', $address );
		$index   = $parts[2];

		if ( isset( $addresses[ $index ] ) && ! empty( $addresses[ $index ]['shipping_country'] ) ) {
			$this->country = $addresses[ $index ]['shipping_country'];
			return $this->country;
		}

		return false;
	}
}
</file>

<file path="composer.json">
{
    "name": "woocommerce/woocommerce-shipping-multiple-addresses",
    "description": "Allow customers to ship orders with multiple products or quantities to separate addresses instead of forcing them to place multiple orders for different delivery addresses.",
    "homepage": "https://woocommerce.com/products/shipping-multiple-addresses/",
    "type": "wordpress-plugin",
    "license": "GPL-2.0+",
    "archive": {
        "exclude": [
            "!/assets",
            "!/languages",
            "!/assets/js/*.js",
            "!/assets/js/*.min.js",
            "/assets/css/*.scss",
            "bin",
            "tests",
            "node_modules",
            "/vendor",
            "Gruntfile.js",
            "README.md",
            "package.json",
            "package-lock.json",
            "composer.json",
            "composer.lock",
            "phpunit.xml.dist",
            ".*",
            "woocommerce-shipping-multiple-addresses.zip",
            "vendor",
			"pnpm-lock.yaml",
			"client",
			"webpack.config.js",
			"babel.config.js"
        ]
    },
    "require-dev": {
        "woocommerce/qit-cli": "*",
        "squizlabs/php_codesniffer": "*",
        "dealerdirect/phpcodesniffer-composer-installer": "*",
        "wp-coding-standards/wpcs": "*",
        "woocommerce/woocommerce-sniffs": "*"
    },
    "config": {
        "allow-plugins": {
            "dealerdirect/phpcodesniffer-composer-installer": true
        }
    },
    "scripts": {
        "check-security": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=./.phpcs.security.xml  --report-full --report-summary"
        ],
        "check-php": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
        ],
        "check-php:fix": [
            "./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"
        ],
        "check-all": [
            "./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"
        ],
        "check-all:fix": [
            "./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests,dist --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"
        ],
        "qit:security": [
            "npm run build && ./vendor/bin/qit run:security woocommerce-shipping-multiple-addresses --zip=woocommerce-shipping-multiple-addresses.zip"
        ]
    }
}
</file>

<file path="includes/class-wc-ship-multiple.php">
<?php
/**
 * Class WC_Ship_Multiple file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use WooCommerce\Multiple_Addresses\Blocks_Integration;
use WooCommerce\Multiple_Addresses\Store_API_Extension;

/**
 * Class WC_Ship_Multiple.
 */
class WC_Ship_Multiple {

	const FILE = WC_MS_FILE;

	/**
	 * WC_MS_Front object.
	 *
	 * @var WC_MS_Front
	 */
	public $front;

	/**
	 * WC_MS_Cart object.
	 *
	 * @var WC_MS_Cart
	 */
	public $cart;

	/**
	 * WC_MS_Packages object.
	 *
	 * @var WC_MS_Packages
	 */
	public $packages;

	/**
	 * WC_MS_Address_Book object.
	 *
	 * @var WC_MS_Address_Book
	 */
	public $address_book;

	/**
	 * WC_MS_Checkout object.
	 *
	 * @var WC_MS_Checkout
	 */
	public $checkout;

	/**
	 * WC_MS_Notes object.
	 *
	 * @var WC_MS_Notes
	 */
	public $notes;

	/**
	 * WC_MS_Gifts object.
	 *
	 * @var WC_MS_Gifts
	 */
	public $gifts;

	/**
	 * WC_MS_Admin object.
	 *
	 * @var WC_MS_Admin
	 */
	public $admin;

	/**
	 * WC_MS_Order object.
	 *
	 * @var WC_MS_Order
	 */
	public $order;

	/**
	 * WC_MS_Order_Shipment object.
	 *
	 * @var WC_MS_Order_Shipment
	 */
	public $shipments;

	/**
	 * WC_MS_Customer_Order_Csv_Export object.
	 *
	 * @var WC_MS_Customer_Order_Csv_Export
	 */
	public $csv_export;

	/**
	 * Order meta key name.
	 *
	 * @var string
	 */
	public $meta_key_order = '_shipping_methods';

	/**
	 * Meta key settings ( Might not be used anymore ).
	 *
	 * @var string
	 */
	public $meta_key_settings = '_shipping_settings';

	/**
	 * Settings from shipping settings.
	 *
	 * @var array
	 */
	public $settings = null;

	/**
	 * Saved settings.
	 *
	 * @var array
	 */
	public $gateway_settings = null;

	/**
	 * Notification and button text.
	 *
	 * @var array
	 */
	public static $lang = array(
		'notification' => 'You may use multiple shipping addresses on this cart',
		'btn_items'    => 'Set Multiple Addresses',
	);

	/**
	 * Class constructor.
	 */
	public function __construct() {
		// Load the shipping options.
		$this->settings = get_option( $this->meta_key_settings, array() );

		add_action( 'after_setup_theme', array( $this, 'load_textdomain' ) );

		// Define shortcodes.
		add_shortcode( 'woocommerce_select_multiple_addresses', array( $this, 'draw_form' ) );
		add_shortcode( 'woocommerce_account_addresses', array( $this, 'account_addresses' ) );

		// Override needs shipping method and totals.
		add_action( 'woocommerce_init', array( $this, 'wc_init' ) );
		add_action( 'woocommerce_init', array( $this, 'maybe_install_pages' ) );

		// Declare the HPOS compatibility for this plugin.
		add_action( 'before_woocommerce_init', array( $this, 'declare_hpos_compatibility' ) );

		require_once WC_MS_ABSPATH . 'includes/class-wc-multiple-shipping-settings.php';

		add_filter( 'woocommerce_shipping_methods', array( $this, 'add_multiple_shipping_method' ) );

		// Register Blocks Integration.
		add_action( 'woocommerce_blocks_loaded', array( $this, 'register_blocks_integration' ) );
		add_action( 'woocommerce_blocks_loaded', array( $this, 'extend_store_api' ) );

		// Subscribe to automated translations.
		add_filter( 'woocommerce_translations_updates_for_' . basename( WC_MS_FILE, '.php' ), '__return_true' );

		$settings               = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$this->gateway_settings = $settings;

		if ( isset( $settings['lang_notification'] ) ) {
			self::$lang['notification'] = $settings['lang_notification'];
		}

		if ( isset( $settings['lang_btn_items'] ) ) {
			self::$lang['btn_items'] = $settings['lang_btn_items'];
		}

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-post-types.php';

		include_once WC_MS_ABSPATH . 'includes/functions.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-gifts.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-notes.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-checkout.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-cart.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-packages.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-address-book.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-front.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-admin.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order-type-order-shipment.php';
		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-order-shipment.php';

		include_once WC_MS_ABSPATH . 'includes/integrations/class-wc-ms-customer-order-csv-export.php';

		include_once ABSPATH . 'wp-admin/includes/plugin.php';

		if (
			is_plugin_active( 'se_woocommerce/shippingeasy_order.php' ) ||
			is_plugin_active( 'woocommerce-shippingeasy/woocommerce-shippingeasy.php' )
		) {
			include_once WC_MS_ABSPATH . 'includes/class-wc-ms-shipping-easy.php';
		}

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-api.php';
		new WC_MS_API();

		$this->gifts        = new WC_MS_Gifts( $this );
		$this->notes        = new WC_MS_Notes( $this );
		$this->checkout     = new WC_MS_Checkout( $this );
		$this->cart         = new WC_MS_Cart( $this );
		$this->packages     = new WC_MS_Packages( $this );
		$this->address_book = new WC_MS_Address_Book( $this );
		$this->front        = new WC_MS_Front( $this );
		$this->admin        = new WC_MS_Admin( $this );
		$this->order        = new WC_MS_Order( $this );
		$this->shipments    = new WC_MS_Order_Shipment( $this );
		$this->csv_export   = new WC_MS_Customer_Order_Csv_Export( $this );

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-shipworks.php';

		$this->load_compat_classes();
	}

	/**
	 * Load textdomain for localization.
	 */
	public function load_textdomain() {
		load_plugin_textdomain( 'woocommerce-shipping-multiple-addresses', false, basename( WC_MS_ABSPATH ) . '/languages' );
	}

	/**
	 * Add multiple shipping settings inside WooCommerce > Settings > Shipping page.
	 *
	 * @param array $methods All of registered shipping method.
	 */
	public function add_multiple_shipping_method( $methods ) {
		if ( in_array( 'woocommerce-shipping-multiple-addresses/woocommerce-shipping-multiple-addresses.php', get_option( 'active_plugins' ), true ) ) {
			$methods['multiple_shipping'] = 'WC_Multiple_Shipping_Settings';
		}

		return $methods;
	}

	/**
	 * Creating multiship pages.
	 */
	public function maybe_install_pages() {

		$page_id = wc_get_page_id( 'multiple_addresses' );

		if ( -1 === $page_id || null === get_post( $page_id ) ) {
			// get the checkout page.
			$checkout_id = wc_get_page_id( 'checkout' );

			// add page and assign.
			$page = array(
				'menu_order'     => 0,
				'comment_status' => 'closed',
				'ping_status'    => 'closed',
				'post_author'    => 1,
				'post_content'   => '[woocommerce_select_multiple_addresses]',
				'post_name'      => 'shipping-addresses',
				'post_parent'    => $checkout_id,
				'post_title'     => 'Shipping Addresses',
				'post_type'      => 'page',
				'post_status'    => 'publish',
				'post_category'  => array( 1 ),
			);

			$page_id = wp_insert_post( $page );

			update_option( 'woocommerce_multiple_addresses_page_id', $page_id );
		}

		$page_id = wc_get_page_id( 'account_addresses' );

		if ( -1 === $page_id || null === get_post( $page_id ) ) {
			// Get the checkout page.
			$account_id = wc_get_page_id( 'myaccount' );

			// Add page and assign.
			$page = array(
				'menu_order'     => 0,
				'comment_status' => 'closed',
				'ping_status'    => 'closed',
				'post_author'    => 1,
				'post_content'   => '[woocommerce_account_addresses]',
				'post_name'      => 'account-addresses',
				'post_parent'    => $account_id,
				'post_title'     => 'Shipping Addresses',
				'post_type'      => 'page',
				'post_status'    => 'publish',
				'post_category'  => array( 1 ),
			);

			$page_id = wp_insert_post( $page );

			update_option( 'woocommerce_account_addresses_page_id', $page_id );
		}
	}

	/**
	 * Load compatibility classes.
	 *
	 * @since 3.6.13
	 * @return void
	 */
	public function load_compat_classes() {
		if ( class_exists( 'WC_PIP' ) ) {
			include_once WC_MS_ABSPATH . 'includes/compat/class-wc-pip-compat.php';
			new WC_Pip_Compat();
		}
	}

	/**
	 * Declaring HPOS compatibility.
	 */
	public function declare_hpos_compatibility() {
		if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
			\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', 'woocommerce-shipping-multiple-addresses/woocommerce-shipping-multiple-addresses.php', true );
		}
	}

	/**
	 * Check if multiship is enabled.
	 *
	 * @return boolean.
	 */
	public function is_multiship_enabled(): bool {
		$enabled = true;

		if ( class_exists( 'WC_Role_Methods' ) ) {
			// Process Role-based shipping methods.
			$enabled = false;

			// Get the current logged-in user roles, or set to Guest if not logged in.
			$current_user_roles = is_user_logged_in() ? array_map( 'strtolower', wp_get_current_user()->roles ) : array( 'Guest' );

			foreach ( $current_user_roles as $user_role ) {
				if ( WC_Role_Methods::get_instance()->check_rolea_methods( $user_role, 'multiple_shipping:0' ) ) {
					$enabled = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate multiship activation value.
		 *
		 * @param boolean $enabled is the multiship activation value.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_is_multiship_enabled', $enabled );
	}

	/**
	 * Display privacy notice.
	 */
	public function wc_init() {

		include_once WC_MS_ABSPATH . 'includes/class-wc-ms-privacy.php';

		add_action( 'woocommerce_before_order_total', array( $this, 'display_shipping_methods' ) );
		add_action( 'woocommerce_review_order_before_order_total', array( $this, 'display_shipping_methods' ) );
	}

	/**
	 * Display shipping option per product.
	 */
	public function product_options() {
		global $post, $thepostid;

		$settings  = $this->settings;
		$thepostid = $post->ID;

		$ship = WC()->shipping;

		$shipping_methods   = WC()->shipping->shipping_methods;
		$ship_methods_array = array();
		$categories_array   = array();

		foreach ( $shipping_methods as $id => $object ) {
			if ( 'yes' === $object->enabled && 'multiple_shipping' !== $id ) {
				$ship_methods_array[ $id ] = $object->method_title;
			}
		}

		$method = $this->get_product_shipping_method( $thepostid );
		?>
		<p style="border-top: 1px solid #DFDFDF;">
			<strong><?php esc_html_e( 'Shipping Options', 'periship' ); ?></strong>
		</p>
		<p class="form-field method_field">
			<label for="product_method"><?php esc_html_e( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' ); ?></label>
			<select name="product_method[]" id="product_method" class="chzn-select" multiple>
				<option value=""></option>
				<?php
				foreach ( $ship_methods_array as $value => $label ) :
					?>
					<option value="<?php echo esc_attr( $value ); ?>" <?php selected( $method, $value ); ?>><?php echo esc_html( $label ); ?></option>
				<?php endforeach; ?>
			</select>
			<input type="hidden" name="product_option_nonce" value="<?php echo esc_attr( wp_create_nonce( 'wcms_product_option' ) ); ?>" />
		</p>
		<script type="text/javascript">jQuery("#product_method").chosen();</script>
		<?php
	}

	/**
	 * Process metabox. ( Might not be used anymore. )
	 *
	 * @param int $post_id Post ID.
	 */
	public function process_metabox( $post_id ) {
		$settings = $this->settings;

		$nonce = ( isset( $_POST['product_option_nonce'] ) ) ? sanitize_text_field( wp_unslash( $_POST['product_option_nonce'] ) ) : '';
		if ( empty( $nonce ) || ! wp_verify_nonce( $nonce, 'wcms_product_option' ) ) {
			return;
		}

		$zip_origin = null;
		$method     = ( ! empty( $_POST['product_method'] ) && is_array( $_POST['product_method'] ) ) ? sanitize_text_field( wp_unslash( $_POST['product_method'] ) ) : false;

		if ( ! $method ) {
			return;
		}

		// Remove all instances of this product is first.
		foreach ( $settings as $idx => $setting ) {
			$post_id          = intval( $post_id );
			$setting_products = array_map( 'intval', $setting['products'] );

			if ( in_array( $post_id, $setting_products, true ) ) {
				foreach ( $setting_products as $pid => $id ) {
					if ( $id === $post_id ) {
						unset( $settings[ $idx ]['products'][ $pid ] );
					}
				}
			}
		}

		// Look for a matching zip code.
		$matched   = false;
		$zip_match = false;
		foreach ( $settings as $idx => $setting ) {

			if ( $setting['zip'] === $zip_origin ) {
				$zip_match = $idx;
				// Methods must match.
				if ( $method && 0 === count( array_diff( $setting['method'], $method ) ) ) {
					// Zip and method matched
					// add to existing setting.
					$matched                        = true;
					$settings[ $idx ]['products'][] = $post_id;
					break;
				}
			}
		}

		if ( ! $matched ) {
			$settings[] = array(
				'zip'        => $zip_origin,
				'products'   => array( $post_id ),
				'categories' => array(),
				'method'     => $method,
			);
		}

		// Finally, do some cleanup.
		foreach ( $settings as $idx => $setting ) {
			if ( empty( $setting['products'] ) && empty( $setting['categories'] ) ) {
				unset( $settings[ $idx ] );
			}
		}
		$settings = array_merge( $settings, array() );

		// Update the settings.
		update_option( $this->meta_key_settings, $settings );
	}

	/**
	 * Display current user's addresses.
	 */
	public function account_addresses() {
		ob_start();

		$this->cart->load_cart_files();

		$checkout    = WC()->checkout;
		$user        = wp_get_current_user();
		$ship_fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

		if ( 0 === $user->ID ) {
			return;
		}

		$idx = isset( $_GET['edit'] ) ? absint( $_GET['edit'] ) : -1; // phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( -1 !== $idx ) {
			$updating   = true;
			$other_addr = get_user_meta( $user->ID, 'wc_other_addresses', true );
			$address    = $other_addr[ $idx ];
		} else {
			$updating = false;
			$address  = array();
		}

		// Enqueue scripts.
		wp_enqueue_script( 'wc-country-select' );
		wp_enqueue_script( 'wc-address-i18n' );

		wc_get_template(
			'account-address-form.php',
			array(
				'checkout'    => $checkout,
				'user'        => $user,
				'ship_fields' => $ship_fields,
				'address'     => $address,
				'idx'         => $idx,
				'updating'    => $updating,
			),
			'multi-shipping',
			WC_MS_ABSPATH . 'templates/'
		);

		return ob_get_clean();
	}

	/**
	 * Displaying a form.
	 */
	public function draw_form() {
		if ( is_null( WC()->cart ) ) {
			return '';
		}

		ob_start();

		if ( ! isset( $_GET['order_id'] ) || empty( $_GET['order_id'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended

			$this->cart->load_cart_files();

			$user        = wp_get_current_user();
			$cart        = WC()->cart;
			$checkout    = WC()->checkout;
			$contents    = wcms_get_real_cart_items();
			$ship_fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );

			if ( ( 0 !== $user->ID && ! $this->address_book->user_has_shipping_address( $user->ID ) )
				|| ( 0 === $user->ID && 0 === count( $this->address_book->get_user_addresses( $user->ID ) ) )
			) {
				$this->address_book->save_checkout_address( $user->ID );
			}

			$addresses = $this->address_book->get_available_user_addresses( $user );
			unset( $ship_fields['shipping_state']['country'] );

			if ( isset( $_GET['new'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				wc_add_notice( __( 'New address saved', 'woocommerce-shipping-multiple-addresses' ) );
			}

			wc_print_notices();

			if ( empty( $addresses ) || isset( $_REQUEST['address-form'] ) ) { // phpcs:ignore WordPress.Security.NonceVerification.Recommended
				wc_get_template(
					'address-form.php',
					array(
						'checkout'    => $checkout,
						'addresses'   => $addresses,
						'ship_fields' => $ship_fields,
					),
					'multi-shipping',
					WC_MS_ABSPATH . 'templates/'
				);
			} elseif ( ! empty( $contents ) ) {
				$relations = wcms_session_get( 'wcms_item_addresses' );

				if ( $addresses ) {
					foreach ( $addresses as $x => $addr ) {
						foreach ( $contents as $key => $value ) {
							if ( isset( $relations[ $x ] ) && ! empty( $relations[ $x ] ) ) :
								$qty = array_count_values( $relations[ $x ] );

								if ( in_array( $key, $relations[ $x ], true ) ) {
									if ( isset( $placed[ $key ] ) ) {
										$placed[ $key ] += $qty[ $key ];
									} else {
										$placed[ $key ] = $qty[ $key ];
									}
								}

							endif;
						}
					}
				}

				$minimized_contents = array_map(
					function( $content ) {
						$product_id   = isset( $content['data'] ) ? $content['data']->get_id() : 0;
						$product_name = isset( $content['data'] ) ? $content['data']->get_name() : '';
						return array(
							'key'          => $content['key'],
							'product_id'   => $product_id,
							'product_name' => $product_name,
							'quantity'     => $content['quantity'],

						);
					},
					$contents
				);

				$minimized_contents = array_filter(
					$minimized_contents,
					function( $content ) {
						return ! empty( $content['product_name'] ) && ! empty( $content['product_id'] );
					}
				);

				wp_enqueue_script( 'wcms-address-selection' );
				wp_localize_script(
					'wcms-address-selection',
					'wcms_address_selection_params',
					/**
					 * Filter to manipulate the countries parameters on JS.
					 *
					 * @param array JSON encoded list of countries and select text.
					 *
					 * @since 3.3.19
					 */
					apply_filters(
						'wcms_address_selection_params',
						array(
							'addresses' => $addresses,
							'contents'  => $minimized_contents,
						)
					)
				);

				$relations = wcms_session_get( 'wcms_item_addresses' );
				if ( empty( $relations ) ) {
					$relations = array();
					foreach ( $contents as $key => $content ) {
						for ( $i = 0; $i < $content['quantity']; $i++ ) {
							$relations[] = $key;
						}
					}
					$relations = array( $relations );
				}

				wc_get_template(
					'shipping-address-table.php',
					array(
						'addresses'   => $addresses,
						'relations'   => $relations,
						'checkout'    => $checkout,
						'contents'    => $contents,
						'ship_fields' => $ship_fields,
						'user'        => $user,
					),
					'multi-shipping',
					WC_MS_ABSPATH . 'templates/'
				);
			}
		} else {
			// Load order and display the addresses.
			$order_id = intval( $_GET['order_id'] ); // phpcs:ignore WordPress.Security.NonceVerification.Recommended
			$order    = wc_get_order( $order_id );

			if ( ! is_user_logged_in() ) {
				return esc_html__( 'Please log in to access this page', 'woocommerce-shipping-multiple-addresses' );
			}

			if ( ! $order instanceof WC_Order ) {
				return esc_html__( 'Order could not be found', 'woocommerce-shipping-multiple-addresses' );
			}

			$current_user = wp_get_current_user();

			if ( $order->get_user_id() !== $current_user->ID && $order->get_billing_email() !== $current_user->user_email ) {
				return esc_html__( 'You don\'t have access to this page', 'woocommerce-shipping-multiple-addresses' );
			}

			$packages = $order->get_meta( '_wcms_packages' );

			if ( ! is_array( $packages ) || empty( $packages ) ) {
				return esc_html__( 'This order does not ship to multiple addresses', 'woocommerce-shipping-multiple-addresses' );
			}

			// load the address fields.
			$this->cart->load_cart_files();

			echo '<table class="shop_tabe"><thead><tr><th class="product-name">' . esc_html__( 'Product', 'woocommerce-shipping-multiple-addresses' ) . '</th><th class="product-quantity">' . esc_html__( 'Qty', 'woocommerce-shipping-multiple-addresses' ) . '</th><th class="product-address">' . esc_html__( 'Address', 'woocommerce-shipping-multiple-addresses' ) . '</th></thead>';
			echo '<tbody>';

			$tr_class = '';
			foreach ( $packages as $x => $package ) {
				$products  = $package['contents'];
				$item_meta = '';
				foreach ( $products as $i => $product ) {
					$tr_class = ( '' === $tr_class ) ? 'alt-table-row' : '';

					if ( isset( $product['data']->item_meta ) && ! empty( $product['data']->item_meta ) ) {
						$item_meta .= '<pre>';
						foreach ( $product['data']->item_meta as $meta ) {
							$item_meta .= $meta['meta_name'] . ': ' . $meta['meta_value'] . "\n";
						}
						$item_meta .= '</pre>';
					}

					/**
					 * Filter to manipulate product title.
					 *
					 * @param string Product title.
					 * @param WC_Product $product Product object.
					 *
					 * @since 3.1
					 */
					$package_product_title = apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product );
					echo '<tr class="' . esc_attr( $tr_class ) . '">';
					echo '<td class="product-name"><a href="' . esc_url( get_permalink( $product['data']->get_id() ) ) . '">' . wp_kses_post( $package_product_title ) . '</a><br />' . wp_kses_post( $item_meta ) . '</td>';
					echo '<td class="product-quantity">' . esc_html( $product['quantity'] ) . '</td>';
					// no need to escape. It's already been filtered by `wcms_get_formatted_address()`.
					echo '<td class="product-address"><address>' . wcms_get_formatted_address( $package['destination'] ) . '</td>'; //phpcs:ignore
					echo '</tr>';
				}
			}

			echo '</table>';
		}

		return ob_get_clean();
	}

	/**
	 * Display multiple shipping methods.
	 */
	public function display_shipping_methods() {

		$packages          = WC()->cart->get_shipping_packages();
		$shipping_packages = WC()->shipping->get_packages();

		foreach ( $shipping_packages as $index => $package ) {
			if ( ! isset( $packages[ $index ] ) ) {
				continue;
			}

			$packages[ $index ]['rates'] = $package['rates'];
		}

		if ( ! $this->cart->cart_is_eligible_for_multi_shipping() ) {
			return;
		}

		$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
		if ( isset( $sess_cart_addresses ) && ! empty( $sess_cart_addresses ) ) {
			// Always allow users to select shipping.
			$this->render_shipping_row( $packages, 0 );
		} elseif ( $this->packages_have_different_origins( $packages ) || $this->packages_have_different_methods( $packages ) ) {
			// Show shipping methods available to each package.
			$this->render_shipping_row( $packages, 1 );
		} elseif ( $this->packages_contain_methods( $packages ) ) {
			// Methods must be combined.
			$this->render_shipping_row( $packages, 2 );
		}
	}

	/**
	 * Render shipping row for multiple shipping.
	 *
	 * @param array $packages Cart packages.
	 * @param int   $type 0=multi-shipping; 1=different packages; 2=same packages.
	 */
	public function render_shipping_row( $packages, $type = 2 ) {

		$page_id         = wc_get_page_id( 'multiple_addresses' );
		$rates_available = false;

		$field_name = 'shipping_method';
		$post       = array();

		// No need for nonce verification. It has been verified on `WC_AJAX::update_order_review()`.
		if ( isset( $_POST['post_data'] ) ) { //phpcs:ignore
			parse_str($_POST['post_data'], $post); //phpcs:ignore
		}

		$settings = get_option( 'woocommerce_multiple_shipping_settings', array() );
		$title    = ! empty( $settings['title'] ) ? $settings['title'] : __( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' );

		if ( 0 === $type || 1 === $type ) :

			?>
			<tr class="multi_shipping">
				<td style="vertical-align: top;" colspan="1">
					<?php echo esc_html( $title ); ?>

					<div id="shipping_addresses">
						<?php
						foreach ( $packages as $x => $package ) :
							$error_message = '';

							if ( $this->is_address_empty( $package['destination'] ) ) {

								$error_message = esc_html__( 'The following items do not have a shipping address assigned.', 'woocommerce-shipping-multiple-addresses' );

							} elseif ( ! isset( $package['rates'] ) || empty( $package['rates'] ) ) {

								$error_message = esc_html__( 'There are no shipping options available for the following items.', 'woocommerce-shipping-multiple-addresses' );

							}

							if ( ! empty( $error_message ) ) {
								// We have cart items with no set address.
								$products = $package['contents'];
								?>
								<div class="ship_address no_shipping_address">
									<em><?php echo esc_html( $error_message ); ?></em>
									<ul>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<li>
												<strong>
													<?php
													/**
													 * Filter to manipulate product title
													 *
													 * @param string Product title.
													 * @param WC_Product $product Product object.
													 *
													 * @since 3.1
													 */
													echo wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
													?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</li>
										<?php endforeach; ?>
									</ul>
									<?php
									echo '<p style="text-align: center"><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="button modify-address-button">' . esc_html__( 'Assign Shipping Address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									?>
								</div>
								<?php
								continue;
							}

							$shipping_methods = array();
							$products         = $package['contents'];
							$selected         = wcms_session_get( 'shipping_methods' );
							$rates_available  = true;

							if ( 0 === $type ) :
								?>
								<div class="ship_address">
									<dl>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<dd>
												<strong>
													<?php
													/**
													 * Filter to manipulate product title
													 *
													 * @param string Product title.
													 * @param WC_Product $product Product object.
													 *
													 * @since 3.1
													 */
													echo wp_kses_post( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
													?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</dd>
										<?php endforeach; ?>
									</dl>
									<?php
									$formatted_address = wcms_get_formatted_address( $package['destination'] );

									// no need to escape. it's already filtered by `wcms_get_formatted_address()`.
									echo '<address>'. $formatted_address .'</address><br />'; //phpcs:ignore ?>
									<?php

									/**
									 * Filter to manipulate product title
									 *
									 * @param int   $x Package index.
									 * @param array $package Cart package.
									 *
									 * @since 3.1
									 */
									do_action( 'wc_ms_shipping_package_block', $x, $package );

									// If at least one shipping method is available.
									$ship_package['rates'] = array();

									foreach ( $package['rates'] as $rate ) {
										$ship_package['rates'][ $rate->id ] = $rate;
									}

									foreach ( $ship_package['rates'] as $method ) {
										if ( 'multiple_shipping' === $method->id ) {
											continue;
										}

										$method->label = esc_html( $method->label );

										if ( $method->cost > 0 ) {
											$shipping_tax   = $method->get_shipping_tax();
											$method->label .= ' &mdash; ';

											// Append price to label using the correct tax settings.
											if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {

												if ( $shipping_tax > 0 ) {
													if ( WC()->cart->prices_include_tax ) {
														$method->label .= wc_price( $method->cost ) . ' ' . WC()->countries->ex_tax_or_vat();
													} else {
														$method->label .= wc_price( $method->cost );
													}
												} else {
													$method->label .= wc_price( $method->cost );
												}
											} else {
												$method->label .= wc_price( $method->cost + $shipping_tax );
												if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
													$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
												}
											}
										}

										$shipping_methods[] = $method;
									}

									// Print the single available shipping method as plain text.
									if ( 1 === count( $shipping_methods ) ) {
										$method = $shipping_methods[0];

										echo esc_html( $method->label );
										echo '<input type="hidden" class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '" value="' . esc_attr( $method->id ) . '">';

										// Show multiple shipping methods in a select list.
									} elseif ( count( $shipping_methods ) > 1 ) {
										if ( ! is_array( $selected ) || ! isset( $selected[ $x ] ) ) {
											$cheapest_rate = wcms_get_cheapest_shipping_rate( $package['rates'] );

											if ( $cheapest_rate ) {
												$selected[ $x ] = $cheapest_rate;
											}
										}

										echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';

										foreach ( $package['rates'] as $rate ) {
											if ( 'multiple_shipping' === $rate->id ) {
												continue;
											}
											$sel = '';

											if ( isset( $selected[ $x ]['id'] ) && $selected[ $x ]['id'] === $rate->id ) {
												$sel = 'selected';
											}

											echo '<option value="' . esc_attr( $rate->id ) . '" ' . esc_attr( $sel ) . '>';
											// No need to escape. Already use `strip_tags()`.
											echo strip_tags( $rate->label ); //phpcs:ignore
											echo '</option>';
										}

										echo '</select>';
									} else {
										echo '<p>' . esc_html__( '(1) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
									}

									$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
									if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
										echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									}
									?>
								</div>
								<?php
							elseif ( 1 === $type ) :
								?>
								<div class="ship_address">
									<dl>
										<?php
										foreach ( $products as $i => $product ) :
											$attributes = html_entity_decode( wc_get_formatted_cart_item_data( $product ), ENT_COMPAT );
											?>
											<dd>
												<strong>
												<?php
												/**
												 * Filter to manipulate product title
												 *
												 * @param string Product title.
												 * @param WC_Product $product Product object.
												 *
												 * @since 3.1
												 */
												echo esc_html( apply_filters( 'wcms_product_title', get_the_title( $product['data']->get_id() ), $product ) ) . ' x ' . esc_html( $product['quantity'] );
												?>
												</strong>
												<?php
												if ( ! empty( $attributes ) ) {
													$output_attr = str_replace( "\n", '<br/>', $attributes );
													echo '<small class="data">' . wp_kses_post( $output_attr ) . '</small>';
												}
												?>
											</dd>
										<?php endforeach; ?>
									</dl>
									<?php
									// If at least one shipping method is available.
									// Calculate shipping method rates.
									$ship_package['rates'] = array();

									foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

										if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
											continue;
										}

										if ( $shipping_method->is_available( $package ) ) {

											// Reset Rates.
											$shipping_method->rates = array();

											// Calculate Shipping for package.
											$shipping_method->calculate_shipping( $package );

											// Place rates in package array.
											if ( ! empty( $shipping_method->rates ) && is_array( $shipping_method->rates ) ) {
												foreach ( $shipping_method->rates as $rate ) {
													$ship_package['rates'][ $rate->id ] = $rate;
												}
											}
										}
									}

									foreach ( $ship_package['rates'] as $method ) {
										if ( 'multiple_shipping' === $method->id ) {
											continue;
										}

										$method->label = esc_html( $method->label );

										if ( $method->cost > 0 ) {
											$shipping_tax   = $method->get_shipping_tax();
											$method->label .= ' &mdash; ';

											// Append price to label using the correct tax settings.
											if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {

												if ( $shipping_tax > 0 ) {
													if ( WC()->cart->prices_include_tax ) {
														$method->label .= wc_price( $method->cost ) . ' ' . WC()->countries->ex_tax_or_vat();
													} else {
														$method->label .= wc_price( $method->cost );
													}
												} else {
													$method->label .= wc_price( $method->cost );
												}
											} else {
												$method->label .= wc_price( $method->cost + $shipping_tax );
												if ( $shipping_tax > 0 && ! WC()->cart->prices_include_tax ) {
													$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
												}
											}
										}

										$shipping_methods[] = $method;
									}

									// Print a single available shipping method as plain text.
									if ( 1 === count( $shipping_methods ) ) {
										$method = $shipping_methods[0];

										echo esc_html( $method->label );
										// no need to escape. it's already using `strip_tags`.
										echo '<input type="hidden" class="shipping_methods shipping_method" name="'. $field_name .'['. $x .']" value="'.esc_attr( $method->id ).'||'. strip_tags($method->label) .'">';//phpcs:ignore

										// Show multiple shipping methods in a select list.
									} elseif ( count( $shipping_methods ) > 1 ) {
										echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';
										foreach ( $shipping_methods as $method ) {
											if ( 'multiple_shipping' === $method->id ) {
												continue;
											}
											$current_selected = ( isset( $selected[ $x ] ) ) ? $selected[ $x ]['id'] : '';

											// no need to escape. it's already using `strip_tags`.
											echo '<option value="'.esc_attr( $method->id ).'||'. strip_tags($method->label) .'" '.selected( $current_selected, $method->id, false).'>';//phpcs:ignore

											echo wp_kses_post( wc_cart_totals_shipping_method_label( $method ) );

											echo '</option>';
										}
										echo '</select>';
									} else {
										echo '<p>' . esc_html__( '(2) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
									}

									$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
									if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
										echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
									}
									?>
								</div>
								<?php
							endif;

							$all_shippings = array();
							foreach ( $shipping_methods as $shipping_method ) {
								if ( ! array_key_exists( $shipping_method->get_id(), $all_shippings ) ) {
									$all_shippings[ $shipping_method->get_id() ] = array(
										'id'    => $shipping_method->get_id(),
										'label' => $shipping_method->get_label(),
									);
								}
							}

						endforeach;
						?>
						<div style="clear:both;"></div>

						<input type="hidden" name="all_shipping_methods" value="<?php echo ! empty( $all_shippings ) ? esc_attr( wp_json_encode( $all_shippings ) ) : ''; ?>" />
					</div>

				</td>
				<td style="vertical-align: top;">
					<?php
					$shipping_total = WC()->cart->shipping_total;
					$shipping_tax   = WC()->cart->shipping_tax_total;
					$inc_or_exc_tax = '';

					if ( $shipping_total > 0 && wc_tax_enabled() ) {

						// Append price to label using the correct tax settings.
						if ( ! WC()->cart->display_totals_ex_tax ) {
							$shipping_total += $shipping_tax;

							if ( 0 < $shipping_tax ) {
								$inc_or_exc_tax = WC()->countries->inc_tax_or_vat();
							}
						}
					}
					// no need to escape. It has been filtered by `wc_price`.
					echo wc_price( $shipping_total ) . ' ' . esc_html( $inc_or_exc_tax ); //phpcs:ignore
					?>
				</td>
				<script type="text/javascript">
					jQuery(document).ready(function() {
						jQuery("tr.shipping").remove();
					});
					<?php
					if ( null === wcms_session_get( 'shipping_methods' ) && $rates_available ) {
						echo 'jQuery("body").trigger("update_checkout");';
					}
					?>
				</script>
			</tr>
			<?php
		else :
			?>
			<tr class="multi_shipping">
				<td style="vertical-align: top;" colspan="1">
					<?php esc_html_e( 'Shipping Methods', 'woocommerce-shipping-multiple-addresses' ); ?>

					<?php
					foreach ( $packages as $x => $package ) :
						$shipping_methods = array();
						$products         = $package['contents'];

						if ( 2 === $type ) :
							// If at least one shipping method is available.
							// Calculate shipping method rates.
							$ship_package['rates'] = array();

							foreach ( WC()->shipping->shipping_methods as $shipping_method ) {

								if ( isset( $package['method'] ) && ! in_array( $shipping_method->id, $package['method'], true ) ) {
									continue;
								}

								if ( $shipping_method->is_available( $package ) ) {

									// Reset Rates.
									$shipping_method->rates = array();

									// Calculate Shipping for package.
									$shipping_method->calculate_shipping( $package );

									// Place rates in package array.
									if ( ! empty( $shipping_method->rates ) && is_array( $shipping_method->rates ) ) {
										foreach ( $shipping_method->rates as $rate ) {
											$ship_package['rates'][ $rate->id ] = $rate;
										}
									}
								}
							}

							foreach ( $ship_package['rates'] as $method ) {
								if ( 'multiple_shipping' === $method->id ) {
									continue;
								}

								$method->label = esc_html( $method->label );

								if ( $method->cost > 0 ) {
									$method->label .= ' &mdash; ';

									// Append price to label using the correct tax settings.
									if ( WC()->cart->display_totals_ex_tax || ! WC()->cart->prices_include_tax ) {
										$method->label .= wc_price( $method->cost );
										if ( $method->get_shipping_tax() > 0 && WC()->cart->prices_include_tax ) {
											$method->label .= ' ' . WC()->countries->ex_tax_or_vat();
										}
									} else {
										$method->label .= wc_price( $method->cost + $method->get_shipping_tax() );
										if ( $method->get_shipping_tax() > 0 && ! WC()->cart->prices_include_tax ) {
											$method->label .= ' ' . WC()->countries->inc_tax_or_vat();
										}
									}
								}
								$shipping_methods[] = $method;
							}

							// Print a single available shipping method as plain text.
							if ( 1 === count( $shipping_methods ) ) {
								$method = $shipping_methods[0];
								echo esc_html( $method->label );
								echo '<input type="hidden" class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '" value="' . esc_attr( $method->id ) . '">';

								// Show multiple shipping methods in a select list.
							} elseif ( count( $shipping_methods ) > 1 ) {
								echo '<select class="shipping_methods shipping_method" name="' . esc_attr( $field_name . '[' . $x . ']' ) . '">';
								foreach ( $shipping_methods as $method ) {
									if ( 'multiple_shipping' === $method->id ) {
										continue;
									}
									echo '<option value="' . esc_attr( $method->id ) . '" ' . selected( $method->id, ( isset( $post['shipping_method'] ) ) ? $post['shipping_method'] : '', false ) . '>';
									echo esc_html( $method->label );
									echo '</option>';
								}
								echo '</select>';
							} else {
								echo '<p>' . esc_html__( '(3) Sorry, it seems that there are no available shipping methods for your state. Please contact us if you require assistance or wish to make alternate arrangements.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
							}

							$sess_cart_addresses = wcms_session_get( 'cart_item_addresses' );
							if ( $sess_cart_addresses && ! empty( $sess_cart_addresses ) ) {
								echo '<p><a href="' . esc_url( get_permalink( $page_id ) ) . '" class="modify-address-button">' . esc_html__( 'Modify address', 'woocommerce-shipping-multiple-addresses' ) . '</a></p>';
							}
						endif;
					endforeach;
					?>
				</td>
				<?php // no need to escape. It has been filtered from `wc_price`. ?>
				<td style="vertical-align: top;"><?php echo wc_price( WC()->cart->shipping_total + WC()->cart->shipping_tax_total ); ?></td><?php // phpcs:ignore ?>
				<script type="text/javascript">
					jQuery("tr.shipping").remove();
					<?php
					if ( null === wcms_session_get( 'shipping_methods' ) && $rates_available ) {
						echo 'jQuery("body").trigger("update_checkout");';
					}
					?>
				</script>
			</tr>
			<?php
		endif;
	}

	/**
	 * Get available shipping methods based on the current cart packages.
	 *
	 * @return array Available shipping methods.
	 */
	public function get_available_shipping_methods() {

		$packages = WC()->cart->get_shipping_packages();

		// Loop packages and merge rates to get a total for each shipping method.
		$available_methods = array();

		foreach ( $packages as $package ) {
			if ( ! isset( $package['rates'] ) || ! $package['rates'] ) {
				continue;
			}

			foreach ( $package['rates'] as $id => $rate ) {

				if ( isset( $available_methods[ $id ] ) ) {
					// Merge cost and taxes - label and ID will be the same.
					$available_methods[ $id ]->cost += $rate->cost;

					foreach ( array_keys( $available_methods[ $id ]->taxes + $rate->taxes ) as $key ) {
						$available_methods[ $id ]->taxes[ $key ] = ( isset( $rate->taxes[ $key ] ) ? $rate->taxes[ $key ] : 0 ) + ( isset( $available_methods[ $id ]->taxes[ $key ] ) ? $available_methods[ $id ]->taxes[ $key ] : 0 );
					}
				} else {
					$available_methods[ $id ] = $rate;
				}
			}
		}

		/**
		 * Filter to manipulate the available shipping methods.
		 *
		 * @param array $available_methods Available shipping methods.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wcms_available_shipping_methods', $available_methods );
	}

	/**
	 * Clear sessions and transients from the saved packages.
	 *
	 * @param int|WC_Order|string $order_obj Order ID or Order Object.
	 */
	public function clear_session( $order_obj = '' ) {
		$order = ( $order_obj instanceof WC_Order ) ? $order_obj : wc_get_order( $order_obj );

		if ( $order instanceof WC_Order && in_array( $order->get_status(), array( 'pending', 'failed' ), true ) ) {
			return;
		}

		$packages = wcms_session_get( 'wcms_packages' );

		// Clear packages transient.
		if ( is_array( $packages ) ) {
			foreach ( $packages as $package ) {
				$package_hash = 'wc_ship_' . md5( wp_json_encode( $package ) );
				delete_transient( $package_hash );
			}
		}

		wcms_session_delete( 'cart_item_addresses' );
		wcms_session_delete( 'wcms_item_addresses' );
		wcms_session_delete( 'cart_address_sigs' );
		wcms_session_delete( 'address_relationships' );
		wcms_session_delete( 'shipping_methods' );
		wcms_session_delete( 'wcms_original_cart' );
		wcms_session_delete( 'wcms_packages' );
		wcms_session_delete( 'wcms_packages_after_tax_calc' );
		wcms_session_delete( 'wcms_item_delivery_dates' );
		wcms_session_delete( 'user_default_address' );
		wcms_session_delete( 'wcms_package_notes' );
		wcms_session_delete( 'wcms_delivery_dates' );
		wcms_session_delete( 'wcms_package_gifts' );

		/**
		 * Action after session is cleared.
		 *
		 * @since 3.1
		 */
		do_action( 'wc_ms_cleared_session' );
	}

	/**
	 * Get shipping rate from the cart package.
	 *
	 * @param array $package Cart package.
	 */
	public function get_package_shipping_rates( $package = array() ) {

		$_tax = new WC_Tax();

		// See if we have an explicitly set shipping tax class.
		$shipping_tax_class = get_option( 'woocommerce_shipping_tax_class' );
		if ( $shipping_tax_class ) {
			$tax_class = 'standard' === $shipping_tax_class ? '' : $shipping_tax_class;
		}

		if ( ! empty( $package['destination'] ) ) {
			$country  = $package['destination']['country'];
			$state    = $package['destination']['state'];
			$postcode = $package['destination']['postcode'];
			$city     = $package['destination']['city'];

			// Prices which include tax should always use the base rate if we don't know where the user is located
			// Prices excluding tax however should just not add any taxes, as they will be added during checkout.
		} elseif ( 'yes' === get_option( 'wc_prices_include_tax' ) || 'base' === get_option( 'woocommerce_default_customer_address' ) ) {
				$country  = WC()->countries->get_base_country();
				$state    = WC()->countries->get_base_state();
				$postcode = '';
				$city     = '';
		} else {
				return array();
		}

		// If we are here then shipping is taxable - work it out.
		// This will be per order shipping - loop through the order and find the highest tax class rate.
		$found_tax_classes = array();
		$matched_tax_rates = array();
		$rates             = false;

		// Loop cart and find the highest tax band.
		if ( count( WC()->cart->get_cart() ) > 0 ) {
			foreach ( WC()->cart->get_cart() as $item ) {
				$found_tax_classes[] = $item['data']->get_tax_class();
			}
		}

		$found_tax_classes = array_unique( $found_tax_classes );

		// If multiple classes are found, use highest.
		if ( count( $found_tax_classes ) > 1 ) {
			if ( in_array( '', $found_tax_classes, true ) ) {
				$rates = $_tax->find_rates(
					array(
						'country'  => $country,
						'state'    => $state,
						'city'     => $city,
						'postcode' => $postcode,
					)
				);
			} else {
				$tax_classes = array_filter( array_map( 'trim', explode( "\n", get_option( 'woocommerce_tax_classes' ) ) ) );

				foreach ( $tax_classes as $tax_class ) {
					if ( in_array( $tax_class, $found_tax_classes, true ) ) {
						$rates = $_tax->find_rates(
							array(
								'country'   => $country,
								'state'     => $state,
								'postcode'  => $postcode,
								'city'      => $city,
								'tax_class' => $tax_class,
							)
						);
						break;
					}
				}
			}

			// If a single tax class is found, use it.
		} elseif ( 1 === count( $found_tax_classes ) ) {

			$rates = $_tax->find_rates(
				array(
					'country'   => $country,
					'state'     => $state,
					'postcode'  => $postcode,
					'city'      => $city,
					'tax_class' => $found_tax_classes[0],
				)
			);

		}

		// If no class rate are found, use standard rates.
		if ( ! $rates ) {
			$rates = $_tax->find_rates(
				array(
					'country'  => $country,
					'state'    => $state,
					'postcode' => $postcode,
					'city'     => $city,
				)
			);
		}

		if ( $rates ) {
			foreach ( $rates as $key => $rate ) {
				if ( isset( $rate['shipping'] ) && 'yes' === $rate['shipping'] ) {
					$matched_tax_rates[ $key ] = $rate;
				}
			}
		}

		return $matched_tax_rates;
	}

	/**
	 * Get cart item subtotal.
	 *
	 * @param array $cart_item Cart item.
	 *
	 * @return float.
	 */
	public function get_cart_item_subtotal( $cart_item ) {

		$_product = $cart_item['data'];
		$quantity = $cart_item['quantity'];

		$price   = $_product->get_price();
		$taxable = $_product->is_taxable();

		if ( $taxable ) {
			if ( 'excl' === WC()->cart->get_tax_price_display_mode() ) {
				$row_price = wc_get_price_excluding_tax( $_product, array( 'qty' => $quantity ) );
			} else {
				$row_price = wc_get_price_including_tax( $_product, array( 'qty' => $quantity ) );
			}

			// Non-taxable.
		} else {
			$row_price = $price * $quantity;
		}

		return $row_price;
	}

	/**
	 * Get product shipping method based on product ID.
	 *
	 * @param int $product_id Product ID.
	 *
	 * @return bool|string
	 */
	public function get_product_shipping_method( $product_id ) {
		$settings     = $this->settings;
		$product_cats = wp_get_post_terms( $product_id, 'product_cat', array( 'fields' => 'ids' ) );

		// Look for direct product matches.
		foreach ( $settings as $idx => $setting ) {
			$products = is_array( $setting['products'] ) ? array_map( 'intval', $setting['products'] ) : array( intval( $setting['products'] ) );
			if ( in_array( $product_id, $products, true ) ) {
				return $setting['method'];
			}
		}

		// Look for category matches.
		foreach ( $settings as $idx => $setting ) {
			$categories = array_map( 'intval', $setting['categories'] );

			foreach ( $product_cats as $product_cat_id ) {
				if ( in_array( intval( $product_cat_id ), $categories, true ) ) {
					return $setting['method'];
				}
			}
		}

		return false;
	}

	/**
	 * Check if the package has different methods.
	 *
	 * @param array $packages Cart package.
	 *
	 * @return boolean.
	 */
	public function packages_have_different_methods( $packages = array() ) {
		$last_method = false;
		$_return     = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['method'] ) ) {
				if ( ! $last_method ) {
					$last_method = $package['method'];
				} elseif ( $last_method !== $package['method'] ) {
					$_return = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate the packages value that has different methods.
		 *
		 * @param array $return packages that has different methods.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_have_different_methods', $_return, $packages );
	}

	/**
	 * Check if the package has a different origin.
	 *
	 * @param array $packages Cart package.
	 *
	 * @return boolean
	 */
	public function packages_have_different_origins( $packages = array() ) {
		$last_origin = false;
		$_return     = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['origin'] ) ) {
				if ( ! $last_origin ) {
					$last_origin = $package['origin'];
				} elseif ( $last_origin !== $package['origin'] ) {
					$_return = true;
					break;
				}
			}
		}

		/**
		 * Filter to manipulate the packages value that has different origins.
		 *
		 * @param array $return packages that has different origins.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_have_different_origins', $_return, $packages );
	}

	/**
	 * Check if the packages contain any shipping method.
	 *
	 * @param array $packages Cart package.
	 */
	public function packages_contain_methods( $packages = array() ) {
		$return = false;

		foreach ( $packages as $package ) {
			if ( isset( $package['method'] ) ) {
				$return = true;
				break;
			}
		}

		/**
		 * Filter to manipulate the packages value that has method.
		 *
		 * @param array $return packages that has method.
		 * @param array $packages Original list of packages.
		 *
		 * @since 3.1
		 */
		return apply_filters( 'wc_ms_packages_contain_methods', $return, $packages );
	}

	/**
	 * Clear current package cache.
	 */
	public function clear_packages_cache() {

		WC()->cart->calculate_totals();
		$packages = WC()->cart->get_shipping_packages();

		foreach ( $packages as $idx => $package ) {
			$package_hash = 'wc_ship_' . md5( wp_json_encode( $package ) );
			delete_transient( $package_hash );
		}
	}

	/**
	 * Check if address is empty or not.
	 *
	 * @param array $address_array Address data.
	 */
	public function is_address_empty( $address_array ) {
		if ( empty( $address_array['country'] ) ) {
			return true;
		}

		$address_fields = WC()->countries->get_address_fields( $address_array['country'], 'shipping_' );

		foreach ( $address_fields as $key => $field ) {
			$key = str_replace( 'shipping_', '', $key );

			if ( isset( $field['required'] ) && $field['required'] && empty( $address_array[ $key ] ) ) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Generate session for cart item and addresses.
	 *
	 * @param array $packages Cart package.
	 */
	public function generate_address_session( $packages ) {

		$fields = WC()->countries->get_address_fields( WC()->countries->get_base_country(), 'shipping_' );
		$data   = array();
		$rel    = array();

		foreach ( $packages as $pkg_idx => $package ) {

			if (
				! isset( $package['destination'] ) ||
				empty( $package['destination']['postcode'] ) ||
				empty( $package['destination']['country'] )
			) {
				continue;
			}

			$items = $package['contents'];

			foreach ( $items as $cart_key => $item ) {

				$qty = $item['quantity'];

				$product_id = $item['product_id'];
				$sig        = $cart_key . '_' . $product_id . '_';
				$address_id = 0;

				$i = 1;
				for ( $x = 0; $x < $qty; $x++ ) {
					$rel[ $address_id ][] = $cart_key;

					while ( isset( $data[ 'shipping_first_name_' . $sig . $i ] ) ) {
						++$i;
					}
					$_sig = $sig . $i;

					if ( $fields ) {
						foreach ( $fields as $key => $field ) {
							$address_key                = str_replace( 'shipping_', '', $key );
							$data[ $key . '_' . $_sig ] = $package['destination'][ $address_key ];
						}
					}
				}
			}
		}

		wcms_session_set( 'cart_item_addresses', $data );
		wcms_session_set( 'address_relationships', $rel );
	}

	/**
	 * Register blocks integration.
	 */
	public function register_blocks_integration() {
		require_once WC_MS_ABSPATH . 'includes/class-blocks-integration.php';
		add_action(
			'woocommerce_blocks_checkout_block_registration',
			function ( $integration_registry ) {
				$integration_registry->register( new Blocks_Integration() );
			}
		);
	}

	/**
	 * Extend the store API.
	 */
	public function extend_store_api() {
		require_once WC_MS_ABSPATH . 'includes/class-store-api-extension.php';
		Store_API_Extension::init();
	}
}
</file>

<file path="includes/class-wc-ms-checkout.php">
<?php
/**
 * Class WC_MS_Checkout file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

use Automattic\WooCommerce\Utilities\NumberUtil;
use Automattic\WooCommerce\StoreApi\Exceptions\RouteException;

/**
 * Class WC_MS_Checkout.
 */
class WC_MS_Checkout {

	/**
	 * Main class instance.
	 *
	 * @var WC_Ship_Multiple
	 */
	private $wcms;

	/**
	 * Constructor.
	 *
	 * @param WC_Ship_Multiple $wcms Main class instance.
	 */
	public function __construct( WC_Ship_Multiple $wcms ) {

		$this->wcms = $wcms;

		// free shipping minimum order.
		add_filter( 'woocommerce_shipping_free_shipping_is_available', array( $this, 'free_shipping_is_available_for_package' ), 10, 3 );

		add_filter( 'woocommerce_package_rates', array( $this, 'remove_multishipping_from_methods' ), 10, 2 );
		add_action( 'woocommerce_before_checkout_shipping_form', array( $this, 'display_set_addresses_button' ), 5 );
		add_action( 'woocommerce_before_checkout_shipping_form', array( $this, 'render_user_addresses_dropdown' ) );
		add_action( 'wp_ajax_wcms_ajax_save_billing_fields', array( $this, 'save_billing_fields' ) );
		add_action( 'wp_ajax_nopriv_wcms_ajax_save_billing_fields', array( $this, 'save_billing_fields' ) );
		add_action( 'woocommerce_after_checkout_validation', array( $this, 'legacy_checkout_validation' ) );

		add_action( 'woocommerce_checkout_update_order_meta', array( $this, 'legacy_checkout_process' ) );
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( $this, 'blocks_checkout_process' ), 10 );
		add_action( 'woocommerce_store_api_cart_select_shipping_rate', array( $this, 'select_shipping_rate_on_store_api' ), 10, 3 );
		add_action( 'woocommerce_store_api_cart_update_customer_from_request', array( $this, 'update_customer_on_store_api' ), 10, 2 );
		add_action( 'woocommerce_order_after_calculate_totals', array( $this, 'calculate_order_taxes_for_wc_blocks' ), 10, 2 );
		add_action( 'woocommerce_store_api_checkout_update_customer_from_request', array( $this, 'blocks_checkout_validation' ) );
		add_action( 'woocommerce_store_api_checkout_update_order_from_request', array( $this, 'add_temporary_shipping_address_on_parent' ), 10, 2 );

		add_filter( 'woocommerce_order_item_meta', array( $this, 'add_item_meta' ), 10, 2 );
		// Start WC Blocks Changes.
		add_filter( 'pre_option_woocommerce_ship_to_destination', array( $this, 'force_use_billing_address' ), 10, 3 );

		// handle order review events.
		add_action( 'woocommerce_checkout_update_order_review', array( $this, 'update_order_review' ) );
		add_action( 'woocommerce_after_calculate_totals', array( $this, 'calculate_totals' ) );

		// modify a cart item's subtotal to include taxes.
		add_filter( 'woocommerce_cart_item_subtotal', array( $this, 'subtotal_item_include_taxes' ), 10, 3 );

		add_action( 'woocommerce_checkout_order_processed', array( $this, 'legacy_clear_session' ) );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'blocks_clear_session' ), 40 );

		// split order.
		add_action( 'woocommerce_checkout_order_processed', array( $this, 'create_order_shipments' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'remove_temporary_shipping_address_on_parent' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'create_order_shipments' ), 20, 1 );
		add_action( 'woocommerce_store_api_checkout_order_processed', array( $this, 'restore_customer_default_data' ), 30, 1 );

		// stop WC from updating the customer's shipping address.
		// instead, store it in the address book if it's a new shipping address.
		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );
		add_action( 'woocommerce_checkout_update_user_meta', array( $this, 'maybe_store_shipping_address' ), 10, 1 );
		add_action( 'woocommerce_store_api_checkout_update_customer_from_request', array( $this, 'maybe_store_shipping_address' ), 10, 1 );

		// Initialize order meta.
		add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'store_item_key' ), 10, 2 );

		// Reset multiple address.
		add_action( 'init', array( $this, 'reset_multiple_shipping_address' ), 10 );

		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'maybe_prevent_update_customer_data' ), 100, 1 );
		add_action( 'woocommerce_checkout_update_user_meta', array( $this, 'maybe_save_customer_data' ), 10, 2 );

		add_action('woocommerce_applied_coupon', array( $this, 'coupon_flush_cache' ) );
		add_action('woocommerce_removed_coupon', array( $this, 'coupon_flush_cache' ) );
	}

	/**
	 * Flush the cache when coupon is applied or removed.
	 */
	public function coupon_flush_cache () {
		if ( class_exists( 'WC_Cache_Helper' ) ) {
			WC_Cache_Helper::get_transient_version( 'shipping', true );
		}
	}

	/**
	 * Prevent updating customer data if it's using saved address.
	 *
	 * @param bool $need_update Whether need to update customer data or not.
	 *
	 * return Int.
	 */
	public function maybe_prevent_update_customer_data( $need_update ) {
		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return $need_update;
		}

		// The nonce has been implemented on WooCommerce plugin part.
		// It has been verified on `WC_Checkout::process_checkout()`.
		$use_saved_address    = isset( $_POST['ms_addresses'] ) && '' !== $_POST['ms_addresses'];  // phpcs:ignore WordPress.Security.NonceVerification.Missing
		$to_different_address = ! empty( $_POST['ship_to_different_address'] ); // phpcs:ignore WordPress.Security.NonceVerification.Missing

		if ( $to_different_address && $use_saved_address ) {
			return false;
		}

		return $need_update;
	}

	/**
	 * Save the other customer data if it's using saved address.
	 *
	 * @param Int   $customer_id Customer ID or User ID.
	 * @param Array $data        Post data.
	 */
	public function maybe_save_customer_data( $customer_id, $data ) {
		// The nonce has been implemented on WooCommerce plugin part.
		// It has been verified on `WC_Checkout::process_checkout()`.
		$use_saved_address    = isset( $_POST['ms_addresses'] ) && '' !== $_POST['ms_addresses'];  // phpcs:ignore WordPress.Security.NonceVerification.Missing
		$to_different_address = ! empty( $data['ship_to_different_address'] );

		if ( ! $to_different_address || ! $use_saved_address ) {
			return;
		}

		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		if ( ! is_array( $data ) ) {
			return;
		}

		$customer = new WC_Customer( $customer_id );

		if ( ! empty( $data['billing_first_name'] ) && '' === $customer->get_first_name() ) {
			$customer->set_first_name( $data['billing_first_name'] );
		}

		if ( ! empty( $data['billing_last_name'] ) && '' === $customer->get_last_name() ) {
			$customer->set_last_name( $data['billing_last_name'] );
		}

		// If the display name is an email, update to the user's full name.
		if ( is_email( $customer->get_display_name() ) ) {
			$customer->set_display_name( $customer->get_first_name() . ' ' . $customer->get_last_name() );
		}

		// To make sure it only skips these keys.
		$shipping_address_keys = array(
			'shipping_first_name',
			'shipping_last_name',
			'shipping_company',
			'shipping_country',
			'shipping_address_1',
			'shipping_address_2',
			'shipping_city',
			'shipping_state',
			'shipping_postcode',
		);

		$filtered_data = array_filter(
			$data,
			function ( $key ) use ( $shipping_address_keys ) {
				return ! in_array( $key, $shipping_address_keys, true );
			},
			ARRAY_FILTER_USE_KEY
		);

		foreach ( $filtered_data as $key => $value ) {
			// Use setters where available.
			if ( is_callable( array( $customer, "set_{$key}" ) ) ) {
				$customer->{"set_{$key}"}( $value );

				// Store custom fields prefixed with wither billing_.
			} elseif ( 0 === stripos( $key, 'billing_' ) ) {
				$customer->update_meta_data( $key, $value );
			}
		}

		$customer->save();
	}

	/**
	 * Actions for order item meta.
	 *
	 * @since 3.3.23
	 * @return void
	 */
	public function init_order_meta() {
		add_action( 'woocommerce_checkout_create_order_line_item', array( $this, 'store_item_key' ), 10, 2 );
	}

	/**
	 * This method checks if free shipping is available for the current package,
	 * depending on min_amount/requires.
	 *
	 * @param bool               $is_available Is free shipping available.
	 * @param array              $package Cart package.
	 * @param WC_Shipping_Method $shipping_method Shipping method object.
	 *
	 * @return bool
	 */
	public function free_shipping_is_available_for_package( $is_available, $package, $shipping_method ) {
		$min_amount         = $shipping_method->get_option( 'min_amount' );
		$requires           = $shipping_method->get_option( 'requires' );
		$ignore_discounts   = $shipping_method->get_option( 'ignore_discounts' );
		$has_met_min_amount = false;
		$has_coupon         = false;

		if ( in_array( $requires, array( 'coupon', 'either', 'both' ), true ) ) {
			$coupons = WC()->cart->get_coupons();

			if ( $coupons ) {
				foreach ( $coupons as $code => $coupon ) {
					if ( $coupon->is_valid() && $coupon->get_free_shipping() ) {
						$has_coupon = true;
						break;
					}
				}
			}
		}

		if ( in_array( $requires, array( 'min_amount', 'either', 'both' ), true ) && isset( $package['cart_subtotal'] ) ) {
			$total = $package['cart_subtotal'];

			if ( WC()->cart->display_prices_including_tax() ) {
				$total = $total - WC()->cart->get_discount_tax();
			}

			if ( 'no' === $ignore_discounts ) {
				$total = $total - WC()->cart->get_discount_total();
			}

			$total = round( $total, wc_get_price_decimals() );

			if ( $total >= $min_amount ) {
				$has_met_min_amount = true;
			}
		}

		switch ( $requires ) {
			case 'min_amount':
				$is_available = $has_met_min_amount;
				break;
			case 'coupon':
				$is_available = $has_coupon;
				break;
			case 'both':
				$is_available = $has_met_min_amount && $has_coupon;
				break;
			case 'either':
				$is_available = $has_met_min_amount || $has_coupon;
				break;
			default:
				$is_available = true;
				break;
		}

		return $is_available;
	}

	/**
	 * Remove multishipping from shipping methods.
	 *
	 * @param array $rates Shipping rates.
	 *
	 * @return array
	 */
	public function remove_multishipping_from_methods( $rates ) {

		if ( ! wcms_session_isset( 'wcms_packages' ) && isset( $rates['multiple_shipping'] ) ) {
			unset( $rates['multiple_shipping'] );
		}

		return $rates;
	}

	/**
	 * Display multiple address button on checkout page.
	 */
	public function display_set_addresses_button() {
		if ( is_checkout() && ! $this->wcms->cart->cart_has_multi_shipping() && WC()->cart->needs_shipping() ) {

			$css = 'display:none;';

			if ( $this->wcms->is_multiship_enabled() && $this->wcms->cart->cart_is_eligible_for_multi_shipping() ) {
				$css = '';
			} else {
				// clear all session, so we don't use old cart addresses in case the customer adds more valid products to the cart.
				$this->wcms->clear_session();
			}

			echo '
				<p class="woocommerce-info woocommerce_message" id="wcms_message" style="' . esc_attr( $css ) . '">
					' . esc_html( WC_Ship_Multiple::$lang['notification'] ) . '<br /><br />
					<input type="button" id="wcms_set_addresses" name="wcms_set_addresses" value="' . esc_attr( WC_Ship_Multiple::$lang['btn_items'] ) . '" />
				</p>';
		}
	}

	/**
	 * Save billing address fields.
	 */
	public function save_billing_fields() {
		$nonce = ! empty( $_REQUEST['security'] ) ? sanitize_text_field( wp_unslash( $_REQUEST['security'] ) ) : '';

		if ( ! wp_verify_nonce( $nonce, 'wcms_save_billing_fields_nonce' ) ) {
			wp_send_json(
				array(
					'result'  => 'error',
					'message' => esc_html__( 'Permission denied: Security check failed', 'woocommerce-shipping-multiple-addresses' ),
				)
			);
		}

		foreach ( WC()->checkout->get_checkout_fields( 'billing' ) as $key => $field ) {
			if ( is_callable( array( WC()->customer, "set_{$key}" ) ) ) {
				$post_key = isset( $_POST[ $key ] ) ? wc_clean( wp_unslash( $_POST[ $key ] ) ) : '';
				WC()->customer->{"set_{$key}"}( $post_key );
			}
		}

		WC()->customer->save();

		wp_send_json( array( 'result' => 'success' ) );
	}

	/**
	 * Render user addresses dropdown.
	 */
	public function render_user_addresses_dropdown() {

		$addresses = $this->wcms->address_book->get_available_user_addresses( wp_get_current_user() );

		if ( count( $addresses ) ) :
			?>
		<p id="ms_shipping_addresses_field" class="form-row form-row-wide ms-addresses-field">
			<label><?php esc_html_e( 'Stored Addresses', 'woocommerce-shipping-multiple-addresses' ); ?></label>
			<select id="ms_addresses" name="ms_addresses">
				<option value=""><?php esc_html_e( 'Select an address to use&hellip;', 'woocommerce-shipping-multiple-addresses' ); ?></option>
				<?php
				foreach ( $addresses as $key => $address ) {
					$formatted_address = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ', ' . $address['shipping_address_1'] . ', ' . $address['shipping_city'];
					echo '<option value="' . esc_attr( $key ) . '"';
					foreach ( $address as $key => $value ) {
						echo ' data-' . esc_attr( $key ) . '="' . esc_attr( $value ) . '"';
					}
					echo '>' . esc_html( $formatted_address ) . '</option>';
				}
				?>
			</select>
		</p>
			<?php
		endif;
	}

	/**
	 * Store original cart item key
	 *
	 * @param  WC_Order_Item_Product $item          Order item.
	 * @param  string                $cart_key      Cart item key.
	 */
	public function store_item_key( $item, $cart_key ) {
		$item->add_meta_data( '_wcms_cart_key', $cart_key, true );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout for legacy checkout.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 */
	public function legacy_checkout_process( $new_order ) {
		$this->checkout_process( $new_order, false );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout for WC Blocks.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 */
	public function blocks_checkout_process( $new_order ) {
		$this->checkout_process( $new_order, true );
	}

	/**
	 * Calculating tax and adding order meta when processing the checkout.
	 *
	 * @param int|WC_Order $new_order Either Order ID or WC_Order object.
	 * @param boolean      $is_wc_blocks Is using WC Blocks or not.
	 */
	public function checkout_process( $new_order, $is_wc_blocks = false ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		$order_id = $order->get_id();

		$sess_item_address = wcms_session_get( 'cart_item_addresses' );
		$has_item_address  = ( ! wcms_session_isset( 'cart_item_addresses' ) || empty( $sess_item_address ) ) ? false : true;

		/**
		 * Allow plugins to add action before checkout multi shipping process.
		 *
		 * @param int $order_id Order ID.
		 *
		 * @since 3.3
		 */
		do_action( 'wc_ms_before_checkout_process', $order_id );

		$packages = WC()->cart->get_shipping_packages();

		$sess_item_address = wcms_session_isset( 'cart_item_addresses' ) ? wcms_session_get( 'cart_item_addresses' ) : false;
		$sess_packages     = wcms_session_isset( 'wcms_packages' ) ? wcms_session_get( 'wcms_packages' ) : false;
		$sess_methods      = wcms_session_isset( 'shipping_methods' ) ? wcms_session_get( 'shipping_methods' ) : false;
		$sess_rates        = wcms_session_isset( 'wcms_package_rates' ) ? wcms_session_get( 'wcms_package_rates' ) : false;

		/**
		 * Allow plugins to modify session cart item addresses one last time.
		 *
		 * @param array $sess_item_address Session cart item addresses data.
		 *
		 * @since 3.3
		 */
		$sess_item_address = apply_filters( 'wc_ms_checkout_session_item_address', $sess_item_address );

		/**
		 * Allow plugins to modify session packages one last time.
		 *
		 * @param array $sess_packages Session packages data.
		 *
		 * @since 3.3
		 */
		$sess_packages = apply_filters( 'wc_ms_checkout_session_packages', $sess_packages );

		/**
		 * Allow plugins to modify session method data one last time.
		 *
		 * @param array $sess_methods Session methods data.
		 *
		 * @since 3.3
		 */
		$sess_methods = apply_filters( 'wc_ms_checkout_session_methods', $sess_methods );

		/**
		 * Allow plugins to modify session rate data one last time.
		 *
		 * @param array $sess_rates Session rates data.
		 *
		 * @since 3.3
		 */
		$sess_rates = apply_filters( 'wc_ms_checkout_session_rates', $sess_rates );

		if ( $has_item_address ) {
			$order->update_meta_data( '_multiple_shipping', 'yes' );
		} else {
			$order->delete_meta_data( '_multiple_shipping' );
		}

		// Update the taxes.
		$packages      = $this->calculate_taxes( null, $packages, true );
		$packages      = $this->apply_extra_data_to_package( $packages, true );
		$sess_packages = $this->calculate_taxes( null, $sess_packages, true );
		$sess_packages = $this->apply_extra_data_to_package( $sess_packages, true );

		if ( $packages ) {
			$order->update_meta_data( '_shipping_packages', $packages );
		}

		if ( false !== $sess_item_address && ! empty( $sess_item_address ) ) {
			$order->update_meta_data( '_shipping_addresses', $sess_item_address );

			if ( is_array( $sess_packages ) ) {
				if ( $has_item_address ) {
					$shipping_address = array(
						'first_name' => '',
						'last_name'  => '',
						'company'    => '',
						'address_1'  => '',
						'address_2'  => '',
						'city'       => '',
						'postcode'   => '',
						'country'    => '',
						'state'      => '',
					);

					if ( 1 === count( $sess_packages ) ) {
						$current_package  = current( $sess_packages );
						$shipping_address = $current_package['destination'];
					}

					// Remove the shipping address.
					$order->set_shipping_first_name( $shipping_address['first_name'] );
					$order->set_shipping_last_name( $shipping_address['last_name'] );
					$order->set_shipping_company( $shipping_address['company'] );
					$order->set_shipping_address_1( $shipping_address['address_1'] );
					$order->set_shipping_address_2( $shipping_address['address_2'] );
					$order->set_shipping_city( $shipping_address['city'] );
					$order->set_shipping_postcode( $shipping_address['postcode'] );
					$order->set_shipping_state( $shipping_address['state'] );

					// Only set shipping country when using WC blocks.
					// Because WC Blocks will check if shipping country is empty or not.
					// Legacy checkout doesn't need to do this. Because they get the shipping country directly from the post data.
					if ( ! $is_wc_blocks ) {
						$order->set_shipping_country( $shipping_address['country'] );
					}
				}
			}
		}

		if ( false !== $sess_packages && ! empty( $sess_packages ) && $has_item_address ) {
			$order->update_meta_data( '_wcms_packages', $sess_packages );
		}

		if ( false !== $sess_methods && ! empty( $sess_methods ) && $has_item_address ) {
			$methods = $sess_methods;
			$order->update_meta_data( '_shipping_methods', $methods );

		} else {
			$methods    = $order->get_shipping_methods();
			$ms_methods = array();

			if ( $sess_packages ) {
				foreach ( $sess_packages as $pkg_idx => $package ) {
					foreach ( $methods as $method ) {
						$ms_methods[ $pkg_idx ] = array(
							'id'    => $method['method_id'],
							'label' => $method['name'],
						);
						continue 2;
					}
				}
			}

			$order->update_meta_data( '_shipping_methods', $ms_methods );
		}

		if ( false !== $sess_rates ) {
			$order->update_meta_data( '_shipping_rates', $sess_rates );
		}

		$order->save();

		/**
		 * Allow plugins to add more action after multi shipping checkout process.
		 *
		 * @param int $order_id Order ID.
		 *
		 * @since 3.3
		 */
		do_action( 'wc_ms_after_checkout_process', $order_id );
	}

	/**
	 * Add temporary shipping address to bypass the WC Store API validation in `Automattic\WooCommerce\StoreApi\Utilities\OrderController::validate_address_fields()`.
	 * Context: https://github.com/woocommerce/woocommerce/blob/2a0ad34d999417bbce78aaa490b869a2368d95f8/plugins/woocommerce/src/StoreApi/Utilities/OrderController.php#L389-L398
	 *
	 * @param \WC_Order        $order Order object.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function add_temporary_shipping_address_on_parent( $order, $request ) {
		$billing_address = $order->get_address( 'billing' );

		$order->set_shipping_first_name( $billing_address['first_name'] );
		$order->set_shipping_last_name( $billing_address['last_name'] );
		$order->set_shipping_company( $billing_address['company'] );
		$order->set_shipping_address_1( $billing_address['address_1'] );
		$order->set_shipping_address_2( $billing_address['address_2'] );
		$order->set_shipping_city( $billing_address['city'] );
		$order->set_shipping_postcode( $billing_address['postcode'] );
		$order->set_shipping_state( $billing_address['state'] );

		$order->save();
	}

	/**
	 * Remove temporary shipping address after the shipping address validation has been bypassed.
	 *
	 * @param \WC_Order $order Order object.
	 */
	public function remove_temporary_shipping_address_on_parent( $order ) {
		$order->set_shipping_first_name( '' );
		$order->set_shipping_last_name( '' );
		$order->set_shipping_company( '' );
		$order->set_shipping_address_1( '' );
		$order->set_shipping_address_2( '' );
		$order->set_shipping_city( '' );
		$order->set_shipping_postcode( '' );
		$order->set_shipping_state( '' );

		$order->save();
	}

	/**
	 * Checkout validation for legacy checkout.
	 *
	 * @param array $post POST data.
	 */
	public function legacy_checkout_validation( $post ) {
		$this->checkout_validation( $post );
	}

	/**
	 * Checkout validation for WC checkout blocks.
	 *
	 * @param array $post POST data.
	 */
	public function blocks_checkout_validation( $post ) {
		$this->checkout_validation( $post, true );
	}

	/**
	 * Making sure all multiple shipping package has address on checkout.
	 *
	 * @param array   $post         POST data.
	 * @param boolean $is_wc_blocks Whether is WC Blocks or not.
	 */
	public function checkout_validation( $post, $is_wc_blocks = false ) {

		if ( ! $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		if ( $is_wc_blocks || empty( $post['shipping_method'] ) || 'multiple_shipping' === $post['shipping_method'] || ( is_array( $post['shipping_method'] ) && count( $post['shipping_method'] ) > 1 ) ) {
			$packages  = wcms_session_get( 'wcms_packages' );
			$has_empty = false;

			foreach ( $packages as $package ) {
				if ( empty( $package['contents'] ) || ( isset( $package['bundled_by'] ) && ! empty( $package['bundled_by'] ) ) ) {
					continue;
				}

				if ( $this->wcms->is_address_empty( $package['destination'] ) ) {
					$has_empty = true;
				}
			}

			if ( ! $has_empty ) {
				return;
			}

			$error_text = __( 'One or more items has no shipping address.', 'woocommerce-shipping-multiple-addresses' );

			if ( ! $is_wc_blocks ) {
				wc_add_notice( $error_text, 'error' );
			} else {
				throw new RouteException(
					'wcms_rest_items_no_shipping_address',
					$error_text,
					400,
					array()
				);
			}
		}
	}

	/**
	 * Add order item meta.
	 *
	 * @param WC_Order_Item_Meta $meta Order item meta object.
	 * @param array              $values Package content.
	 */
	public function add_item_meta( $meta, $values ) {

		$packages = wcms_session_get( 'wcms_packages' );
		$methods  = wcms_session_isset( 'shipping_methods' ) ? wcms_session_get( 'shipping_methods' ) : false;

		if ( false !== $methods && ! empty( $methods ) ) {
			if ( isset( $values['package_idx'] ) && isset( $packages[ $values['package_idx'] ] ) ) {
				$meta->add( 'Shipping Method', $methods[ $values['package_idx'] ]['label'] );
			}
		}
	}

	/**
	 * Update order data on checkout page.
	 *
	 * @param array $post POST data.
	 */
	public function update_order_review( $post ) {
		// #39: Not processing single-package shipment causes an infinite loop - check for an empty session instead.
		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return;
		}
		$packages = wcms_session_get( 'wcms_packages' );
		if ( empty( $packages ) ) {
			return;
		}

		$ship_methods = array();
		$data         = array();
		$field        = 'shipping_method';
		parse_str( $post, $data );

		$all_shippings = isset( $data['all_shipping_methods'] ) ? json_decode( $data['all_shipping_methods'], true ) : array();

		if ( isset( $data[ $field ] ) && is_array( $data[ $field ] ) ) {
			$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );

			foreach ( $data[ $field ] as $x => $method ) {
				$method_info = isset( $all_shippings[ $method ] ) ? $all_shippings[ $method ] : $method;

				if ( empty( $method_info['label'] ) ) {
					$method_label = $method;
				} else {
					$explode_method = explode( ' ', $method_info['label'] );
					unset( $explode_method[ count( $explode_method ) - 1 ] );
					$method_label = implode( ' ', $explode_method );
				}

				$ship_methods[ $x ] = array(
					'id'    => $method,
					'label' => $method_label,
				);

				// Update chosen methods in WooCommerce session.
				$chosen_shipping_methods[ $x ] = $method;
			}

			wcms_session_set( 'shipping_methods', $ship_methods );
			WC()->session->set( 'chosen_shipping_methods', $chosen_shipping_methods );
		}
	}

	/**
	 * Return packages with subscription product only.
	 *
	 * @param  array $packages array of packages.
	 * @return array $packages array of packages that contain subscription product.
	 *
	 * @since 3.6.28
	 */
	private function get_shippable_subscription_product_packages( $packages ) {

		if ( ! class_exists( 'WC_Subscriptions_Product' ) ) {
			return $packages;
		}

		if ( ! is_array( $packages ) ) {
			return $packages;
		}

		if ( empty( $packages ) ) {
			return $packages;
		}

		$non_subscription_package_idx = array();

		$temp_packages = $packages;

		foreach ( $temp_packages as $x => $package ) {

			$package_contains_subscriptions_needing_shipping = false;

			foreach ( $package['contents'] as $cart_item_key => $values ) {
				$_product = $values['data'];
				if ( WC_Subscriptions_Product::is_subscription( $_product ) && $_product->needs_shipping() && ! WC_Subscriptions_Product::needs_one_time_shipping( $_product ) ) {
					$package_contains_subscriptions_needing_shipping = true;
				}
			}

			if ( ! $package_contains_subscriptions_needing_shipping ) {
				unset( $packages[ $x ] );
			}
		}

		return $packages;
	}

	/**
	 * Check the cart if it only contains a recurring product.
	 *
	 * @param array|WC_Cart $cart Cart object.
	 *
	 * @return boolean
	 *
	 * @since 3.6.28
	 */
	private function is_recurring_cart( $cart ) {

		if ( ! class_exists( 'WC_Subscriptions_Cart' ) ) {
			return false;
		}

		if ( true === WC_Subscriptions_Cart::cart_contains_subscriptions_needing_shipping( $cart ) && empty( $this->get_cart_non_subscription_item( $cart ) ) ) {

			return true;

		} else {

			return false;

		}
	}

	/**
	 * Get non WC_Subscriptions_Product items from cart.
	 *
	 * @param WC_Cart $cart Cart object.
	 *
	 * @return WC_Cart Cart object without subscriptions product in the cart content.
	 *
	 * @since 3.6.28
	 */
	private function get_cart_non_subscription_item( $cart ) {

		if ( ! class_exists( 'WC_Subscriptions_Product' ) ) {
			return $cart;
		}

		$non_subscription_cart_items = array();

		foreach ( $cart->get_cart() as $cart_item_key => $cart_item ) {

			$_product = $cart_item['data'];
			if ( ! WC_Subscriptions_Product::is_subscription( $_product ) && $_product->needs_shipping() ) {
				$non_subscription_cart_items[ $cart_item_key ] = $cart_item;
			}
		}

		return $non_subscription_cart_items;
	}

	/**
	 * Calculate multiple shipping totals on cart object.
	 *
	 * @param WC_Cart $cart Cart object.
	 */
	public function calculate_totals( $cart ) {
		// phpcs:ignore WordPress.Security.NonceVerification.Recommended
		if ( isset( $_REQUEST['wc-ajax'] ) && 'update_shipping_method' === $_REQUEST['wc-ajax'] ) {

			// Update chosen shipping methods to match with WooCommerce session variable.
			$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );
			$methods                 = wcms_session_get( 'shipping_methods' );

			if ( ! empty( $methods ) ) {
				foreach ( $methods as $key => $method ) {
					if ( isset( $chosen_shipping_methods[ $key ] ) ) {
						$methods[ $key ] = array(
							'id'    => $chosen_shipping_methods[ $key ],
							'label' => $chosen_shipping_methods[ $key ],
						);
					}
				}
			}

			wcms_session_set( 'shipping_methods', $methods );
		}

		$shipping_total = 0;
		$shipping_taxes = array();

		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return $cart;
		}
		if ( ! wcms_session_isset( 'shipping_methods' ) ) {
			return $cart;
		}
		if ( ! wcms_session_isset( 'cart_item_addresses' ) ) {
			return $cart;
		}

		$methods  = wcms_session_get( 'shipping_methods' );
		$packages = wcms_session_get( 'wcms_packages' );
		$rates    = array();

		if ( ! $packages ) {
			$packages = WC()->cart->get_shipping_packages();
			WC()->shipping->calculate_shipping( $packages );
		}

		$packages     = WC()->shipping->get_packages();
		$tax_based_on = get_option( 'woocommerce_tax_based_on', 'billing' );

		// Remove non subscription packages if the current cart is a recurring cart object.
		if ( $this->is_recurring_cart( $cart ) ) {
			$packages = $this->get_shippable_subscription_product_packages( $packages );
		}

		foreach ( $packages as $x => $package ) {
			$chosen = isset( $methods[ $x ] ) ? $methods[ $x ]['id'] : '';

			if ( $chosen ) {
				WC()->customer->set_calculated_shipping( false );
				WC()->customer->set_shipping_location(
					$package['destination']['country'],
					$package['destination']['state'],
					$package['destination']['postcode'],
					$package['destination']['city']
				);

				$ship = $chosen;

				if ( isset( $package['rates'] ) ) {
					if ( ! isset( $package['rates'][ $ship ] ) ) {
						$rate = wcms_get_cheapest_shipping_rate( $package['rates'] );

						if ( isset( $rate['id'] ) ) {
							$ship = $rate['id'];
						}
					}

					if ( isset( $package['rates'][ $ship ] ) ) {
						$rate            = $package['rates'][ $ship ];
						$rates[ $x ]     = $package['rates'];
						$shipping_total += $rate->cost;
						// @see: https://github.com/woocommerce/woocommerce/issues/19131 .
						$rate_options = get_option( 'woocommerce_' . $rate->get_method_id() . '_' . $rate->get_instance_id() . '_settings', true );

						// Calculate tax based on package shipping address.
						if ( 'shipping' === $tax_based_on
							&& ( ! isset( $rate_options['tax_status'] ) || 'none' !== $rate_options['tax_status'] ) && ! WC()->customer->get_is_vat_exempt() ) {
							$shipping_tax_rates = WC_Tax::get_shipping_tax_rates();
							$rate->taxes        = WC_Tax::calc_tax( $rate->cost, $shipping_tax_rates );
						}

						// calculate tax.
						foreach ( array_keys( $shipping_taxes + $rate->taxes ) as $key ) {
							$shipping_taxes[ $key ] = ( isset( $rate->taxes[ $key ] ) ? $rate->taxes[ $key ] : 0 ) + ( isset( $shipping_taxes[ $key ] ) ? $shipping_taxes[ $key ] : 0 );
						}

						// Round shipping tax calculation.
						$shipping_taxes = $this->round_shipping_taxes( $shipping_taxes );
					}
				}
			}

			$packages[ $x ] = $package;

		}

		$cart->set_shipping_taxes( $shipping_taxes );

		$cart->shipping_total     = $shipping_total;
		$cart->shipping_tax_total = ( is_array( $shipping_taxes ) ) ? array_sum( $shipping_taxes ) : 0;

		// Store the shipping rates.
		wcms_session_set( 'wcms_package_rates', $rates );

		if ( wc_tax_enabled() && ! WC()->customer->get_is_vat_exempt() ) {
			$this->calculate_taxes( $cart, $packages );
		}

		$this->apply_extra_data_to_package( $packages, false );
	}

	/**
	 * Get discounted price.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $package_content Package content value.
	 *
	 * @return float
	 */
	private function get_discounted_price( $cart, $package_content ) {
		if ( isset( $package_content['key'] ) ) {
			$cart_item_key = $package_content['key'];
		} elseif ( ! isset( $package_content['line_total'] ) ) {
			$cart_item_key = current( array_keys( $cart->cart_contents ) );
		} else {
			return $package_content['line_total'];
		}
		$cart_item = $cart->cart_contents[ $cart_item_key ];
		return $cart_item['line_total'] / $cart_item['quantity'];
	}

	/**
	 * Calculate taxes on multiple shipping.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $packages Cart packages.
	 * @param boolean $return_packages Function should return the value as cart package when `true`.
	 *
	 * @return array|WC_Cart
	 */
	public function calculate_taxes( $cart = null, $packages = null, $return_packages = false ) {

		if ( ! $this->wcms->is_multiship_enabled() || ! $this->wcms->cart->cart_is_eligible_for_multi_shipping() ) {
			return $packages;
		}

		if ( 'yes' !== get_option( 'woocommerce_calc_taxes', 0 ) ) {
			if ( $return_packages ) {
				return $packages;
			}

			return;
		}

		$merge = false;
		if ( ! is_object( $cart ) ) {
			$cart  = WC()->cart;
			$merge = true;
		}
        // phpcs:ignore WordPress.Security.NonceVerification.Missing
		if ( isset( $_POST['action'] ) && 'woocommerce_update_shipping_method' === sanitize_text_field( wp_unslash( $_POST['action'] ) ) ) {
			return $cart;
		}

		if ( ! $packages ) {
			$packages = $cart->get_shipping_packages();
		}

		if ( empty( $packages ) ) {
			return;
		}

		// clear the taxes arrays remove tax totals from the grand total.
		$old_shipping_tax_total = $cart->shipping_tax_total;

		$item_taxes = array();
		$cart_taxes = array();

		foreach ( $packages as $idx => $package ) {
			if ( isset( $package['destination'] ) && ! $this->wcms->is_address_empty( $package['destination'] ) ) {
				WC()->customer->set_calculated_shipping( false );
				WC()->customer->set_shipping_location(
					$package['destination']['country'],
					$package['destination']['state'],
					$package['destination']['postcode'],
					$package['destination']['city']
				);
			}

			$tax_rates      = array();
			$shop_tax_rates = array();

			// Calculate subtotals for items. This is done first so that discount logic can use the values.
			foreach ( $package['contents'] as $cart_item_key => $values ) {

				if ( ! isset( $cart->cart_contents[ $values['key'] ] ) ) {
					continue;
				}

				$_product = $values['data'];

				// Prices.
				$line_price        = $_product->get_price() * $values['quantity'];
				$line_subtotal     = 0;
				$line_subtotal_tax = 0;

				// WC Composite Products.
				if ( isset( $values['composite_data'] ) ) {
					$line_price = 0;

					foreach ( $values['composite_data'] as $composite ) {
						if ( isset( $composite['price'] ) ) {
							$line_price += $composite['price'];
						}
					}

					if ( isset( $values['quantity'] ) ) {
						$line_price *= $values['quantity'];
					}
				}

				if ( ! $_product->is_taxable() ) {
					$line_subtotal = $line_price;
				} elseif ( $cart->prices_include_tax ) {

					// Get base tax rates.
					if ( empty( $shop_tax_rates[ $_product->get_tax_class() ] ) ) {
						$shop_tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_base_tax_rates( $_product->get_tax_class() );
					}

					// Get item tax rates.
					if ( empty( $tax_rates[ $_product->get_tax_class() ] ) ) {
						$tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_rates( $_product->get_tax_class() );
					}

					$base_tax_rates = $shop_tax_rates[ $_product->get_tax_class() ];
					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// ADJUST TAX - Calculations when base tax is not equal to the item tax.
					if ( $item_tax_rates !== $base_tax_rates ) {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $base_tax_rates, true, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal = $line_price - array_sum( $taxes );

						// Now add modified taxes.
						$tax_result        = WC_Tax::calc_tax( $line_subtotal, $item_tax_rates );
						$line_subtotal_tax = array_sum( $tax_result );

						// Regular tax calculation (customer inside base and the tax class is unmodified.
					} else {

						// Calc tax normally.
						$taxes             = WC_Tax::calc_tax( $line_price, $item_tax_rates, true );
						$line_subtotal_tax = array_sum( $taxes );
						$line_subtotal     = $line_price - array_sum( $taxes );

					}

					// Prices exclude tax.
					// This calculation is simpler - work with the base, untaxed price.
				} else {
					// Get item tax rates.
					if ( empty( $tax_rates[ $_product->get_tax_class() ] ) ) {
						$tax_rates[ $_product->get_tax_class() ] = WC_Tax::get_rates( $_product->get_tax_class() );
					}

					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// Base tax for line before discount - we will store this in the order data.
					$taxes             = WC_Tax::calc_tax( $line_price, $item_tax_rates );
					$line_subtotal_tax = array_sum( $taxes );
					$line_subtotal     = $line_price;
				}
			}

			// Calculate totals for items.
			foreach ( $package['contents'] as $cart_item_key => $values ) {
				// To make sure that the 'line_total' will always be declared.
				// In order to fix PHP Notice:  Undefined index: line_total in /wp-includes/class-wp-list-util.php on line 170.
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_total'] = 0;

				if ( ! isset( $cart->cart_contents[ $values['key'] ] ) ) {
					continue;
				}

				$_product = $values['data'];

				// Prices.
				$base_price = $_product->get_price();
				$line_price = $_product->get_price() * $values['quantity'];

				// WC Composite Products.
				if ( isset( $values['composite_data'] ) ) {
					$line_price = 0;

					foreach ( $values['composite_data'] as $composite ) {
						$line_price += $composite['price'];
					}
					$base_price  = $line_price;
					$line_price *= $values['quantity'];
				}

				// Tax data.
				$taxes            = array();
				$discounted_taxes = array();

				if ( ! $_product->is_taxable() ) {
					// Discounted Price (price with any pre-tax discounts applied).
					$discounted_price  = $this->get_discounted_price( $cart, $values );
					$line_subtotal_tax = 0;
					$line_subtotal     = $line_price;
					$line_tax          = 0;
					$line_total        = WC_Tax::round( $discounted_price * $values['quantity'] );

					// Prices include tax.
				} elseif ( $cart->prices_include_tax ) {

					$base_tax_rates = $shop_tax_rates[ $_product->get_tax_class() ];
					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// ADJUST TAX - Calculations when base tax is not equal to the item tax.
					if ( $item_tax_rates !== $base_tax_rates ) {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $base_tax_rates, true, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal = wc_round_tax_total( $line_price - array_sum( $taxes ) );

						// Now add modifed taxes.
						$taxes             = WC_Tax::calc_tax( $line_subtotal, $item_tax_rates );
						$line_subtotal_tax = array_sum( $taxes );

						// Adjusted price (this is the price including the new tax rate).
						$adjusted_price = ( $line_subtotal + $line_subtotal_tax ) / $values['quantity'];

						// Apply discounts.
						$discounted_price = $this->get_discounted_price( $cart, $values );
						$discounted_taxes = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates, false );
						$discounted_taxes = $this->round_line_taxes( $discounted_taxes );
						$line_tax         = array_sum( $discounted_taxes );
						$line_total       = ( $discounted_price * $values['quantity'] ) - $line_tax;

						// Regular tax calculation (customer inside base and the tax class is unmodified.
					} else {

						// Work out a new base price without the shop's base tax.
						$taxes = WC_Tax::calc_tax( $line_price, $item_tax_rates, true );

						// Now we have a new item price (excluding TAX).
						$line_subtotal     = $line_price - array_sum( $taxes );
						$line_subtotal_tax = array_sum( $taxes );

						// Calc prices and tax (discounted).
						$discounted_price = $this->get_discounted_price( $cart, $values );
						$discounted_taxes = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates, false );
						$discounted_taxes = $this->round_line_taxes( $discounted_taxes );
						$line_tax         = array_sum( $discounted_taxes );
						$line_total       = ( $discounted_price * $values['quantity'] ) - $line_tax;
					}

					// Tax rows - merge the totals we just got.
					foreach ( array_keys( $cart_taxes + $discounted_taxes ) as $key ) {
						$cart_taxes[ $key ] = ( isset( $discounted_taxes[ $key ] ) ? $discounted_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
					}

					// Prices exclude tax.
				} else {

					$item_tax_rates = $tax_rates[ $_product->get_tax_class() ];

					// Work out a new base price without the shop's base tax.
					$taxes = WC_Tax::calc_tax( $line_price, $item_tax_rates );

					// Now we have the item price (excluding TAX).
					$line_subtotal     = $line_price;
					$line_subtotal_tax = array_sum( $taxes );

					// Now calc product rates.
					$discounted_price      = $this->get_discounted_price( $cart, $values );
					$discounted_taxes      = WC_Tax::calc_tax( $discounted_price * $values['quantity'], $item_tax_rates );
					$discounted_taxes      = $this->round_line_taxes( $discounted_taxes );
					$discounted_tax_amount = array_sum( $discounted_taxes );
					$line_tax              = $discounted_tax_amount;
					$line_total            = $discounted_price * $values['quantity'];

					// Tax rows - merge the totals we just got.
					foreach ( array_keys( $cart_taxes + $discounted_taxes ) as $key ) {
						$cart_taxes[ $key ] = ( isset( $discounted_taxes[ $key ] ) ? $discounted_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
					}
				}

				// Calculate the discount total from cart line data.
				$discount_total     = $line_subtotal - $line_total;
				$discount_tax_total = $line_subtotal_tax - $line_tax;

				// Store costs + taxes for lines.
				if ( ! isset( $item_taxes[ $cart_item_key ] ) ) {
					$item_taxes[ $cart_item_key ]['line_total']          = $line_total;
					$item_taxes[ $cart_item_key ]['line_tax']            = $line_tax;
					$item_taxes[ $cart_item_key ]['line_subtotal']       = $line_subtotal;
					$item_taxes[ $cart_item_key ]['line_subtotal_tax']   = $line_subtotal_tax;
					$item_taxes[ $cart_item_key ]['line_tax_data']       = array(
						'total'    => $discounted_taxes,
						'subtotal' => $taxes,
					);
					$item_taxes[ $cart_item_key ]['line_disc_total']     = $discount_total;
					$item_taxes[ $cart_item_key ]['line_disc_total_tax'] = $discount_tax_total;
				} else {
					$item_taxes[ $cart_item_key ]['line_total']                += $line_total;
					$item_taxes[ $cart_item_key ]['line_tax']                  += $line_tax;
					$item_taxes[ $cart_item_key ]['line_subtotal']             += $line_subtotal;
					$item_taxes[ $cart_item_key ]['line_subtotal_tax']         += $line_subtotal_tax;
					$item_taxes[ $cart_item_key ]['line_tax_data']['total']    += $discounted_taxes;
					$item_taxes[ $cart_item_key ]['line_tax_data']['subtotal'] += $taxes;
					$item_taxes[ $cart_item_key ]['line_disc_total']           += $discount_total;
					$item_taxes[ $cart_item_key ]['line_disc_total_tax']       += $discount_tax_total;
				}

				$packages[ $idx ]['contents'][ $cart_item_key ]['line_total']          = $line_total;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_tax']            = $line_tax;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_subtotal']       = $line_subtotal;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_subtotal_tax']   = $line_subtotal_tax;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_tax_data']       = array(
					'total'    => $discounted_taxes,
					'subtotal' => $taxes,
				);
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_disc_total']     = $discount_total;
				$packages[ $idx ]['contents'][ $cart_item_key ]['line_disc_total_tax'] = $discount_tax_total;
			}
		}

		// Calculate taxes for virtual product.
		foreach ( $cart->get_cart() as $cart_item_key => $cart_item ) {

			$_product = $cart_item['data'];

			if ( $_product->is_virtual() ) {

				$item_taxes[ $cart_item_key ]['line_subtotal']       = $cart_item['line_subtotal'];
				$item_taxes[ $cart_item_key ]['line_subtotal_tax']   = $cart_item['line_subtotal_tax'];
				$item_taxes[ $cart_item_key ]['line_total']          = $cart_item['line_total'];
				$item_taxes[ $cart_item_key ]['line_tax']            = $cart_item['line_tax'];
				$item_taxes[ $cart_item_key ]['line_disc_total']     = $cart_item['line_subtotal'] - $cart_item['line_total'];
				$item_taxes[ $cart_item_key ]['line_disc_total_tax'] = $cart_item['line_subtotal_tax'] - $cart_item['line_tax'];

				// Get tax rates total from cart contents if exists.
				if ( isset( $cart_item['line_tax_data']['total'] ) ) {

					$line_taxes = $cart_item['line_tax_data']['total'];

					// Manually calculate tax rates total.
				} else {

					$item_tax_rates = WC_Tax::get_rates( $_product->get_tax_class() );
					$line_taxes     = WC_Tax::calc_tax( $cart_item['line_total'], $item_tax_rates, false );

				}

				// Rounding the line taxes first before summing.
				$line_taxes = $this->round_line_taxes( $line_taxes );

				// Tax rows - merge the totals we just got.
				foreach ( array_keys( $cart_taxes + $line_taxes ) as $key ) {
					$cart_taxes[ $key ] = ( isset( $line_taxes[ $key ] ) ? $line_taxes[ $key ] : 0 ) + ( isset( $cart_taxes[ $key ] ) ? $cart_taxes[ $key ] : 0 );
				}
			}
		}

		// Total up/round taxes and shipping taxes.
		if ( $cart->round_at_subtotal ) {
			$cart->tax_total = WC_Tax::get_tax_total( $cart_taxes );
		} else {
			$cart->tax_total = array_sum( $cart_taxes );
		}

		// Get discount tax discount data by calculating the discount with packages instead of cart.
		$coupon_discount_tax_totals = $this->calculate_coupon_discount_tax_amounts( $cart, $packages );
		$cart->set_discount_total( array_sum( $cart->get_coupon_discount_totals() ) );
		$cart->set_coupon_discount_tax_totals( $coupon_discount_tax_totals );

		$cart->set_cart_contents_taxes( array_map( 'WC_Tax::round', $cart_taxes ) );

		// Get total discount total tax by checking on 'line_disc_total_tax' array value.
		$cart_disc_total_tax = array_sum( array_column( $item_taxes, 'line_disc_total_tax' ) );

		// Set the new cart total data to the cart.
		$cart->set_discount_tax( $cart_disc_total_tax );
		$cart->set_subtotal_tax( array_sum( array_column( $item_taxes, 'line_subtotal_tax' ) ) );

		$discounts = array_sum( $cart->coupon_discount_totals ) + array_sum( $cart->coupon_discount_tax_totals );

		$cart->set_total( ( $cart->get_subtotal() + $cart->get_subtotal_tax() ) + $cart->get_shipping_total() + $old_shipping_tax_total - $discounts );
		$cart->set_total_tax( $cart->get_taxes_total() );

		if ( $merge ) {
			WC()->cart = $cart;
		}

		if ( $return_packages ) {
			return $packages;
		}

		// store the modified packages array.
		wcms_session_set( 'wcms_packages', $packages );
		// store the modified packages array to different session.
		wcms_session_set( 'wcms_packages_after_tax_calc', $packages );

		return $cart;
	}

	/**
	 * Apply extra data ( notes, dates, gifts ) for WC blocks.
	 * The data will be retrieved from the session.
	 *
	 * @param array   $packages  Cart packages.
	 * @param boolean $is_return Is return the package value or not.
	 *
	 * @return array.
	 */
	public function apply_extra_data_to_package( $packages, $is_return ) {
		$notes = wcms_session_get( 'wcms_package_notes' ) ?? array();
		$dates = wcms_session_get( 'wcms_delivery_dates' ) ?? array();
		$gifts = wcms_session_get( 'wcms_package_gifts' ) ?? array();

		foreach ( $packages as $idx => $package ) {
			if ( ! empty( $notes[ $idx ] ) && empty( $package['note'] ) ) {
				$package['note'] = esc_html( $notes[ $idx ] );
			}

			if ( ! empty( $dates[ $idx ] ) && empty( $package['date'] ) ) {
				$package['date'] = esc_html( $dates[ $idx ] );
			}

			if ( ! empty( $gifts[ $idx ] ) && empty( $package['gift'] ) ) {
				$package['gift'] = ( 'yes' === $gifts[ $idx ] );
			}

			$packages[ $idx ] = $package;
		}

		if ( true === $is_return ) {
			return $packages;
		}

		wcms_session_set( 'wcms_packages', $packages );
		wcms_session_set( 'wcms_packages_after_tax_calc', $packages );
	}

	/**
	 * Get the coupons from the cart.
	 *
	 * @param WC_Cart $cart Cart object.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function get_coupons_from_cart( $cart ) {
		$coupons = $cart->get_coupons();

		foreach ( $coupons as $coupon ) {
			switch ( $coupon->get_discount_type() ) {
				case 'fixed_product':
					$coupon->sort = 1;
					break;
				case 'percent':
					$coupon->sort = 2;
					break;
				case 'fixed_cart':
					$coupon->sort = 3;
					break;
				default:
					$coupon->sort = 0;
					break;
			}

			/**
			 * Allow plugins to override the default order.
			 *
			 * @param int       Current coupon order.
			 * @param WC_Coupon Coupon object.
			 *
			 * @since 3.3.23
			 */
			$coupon->sort = apply_filters( 'woocommerce_coupon_sort', $coupon->sort, $coupon );
		}

		uasort( $coupons, array( $this, 'sort_coupons_callback' ) );

		return $coupons;
	}

	/**
	 * Sort coupons so discounts apply consistently across installs.
	 *
	 * In order of priority;
	 *  - sort param
	 *  - usage restriction
	 *  - coupon value
	 *  - ID
	 *
	 * @param WC_Coupon $a Coupon object.
	 * @param WC_Coupon $b Coupon object.
	 *
	 * @since 3.6.34
	 * @return int
	 */
	public function sort_coupons_callback( $a, $b ) {
		if ( $a->sort === $b->sort ) {
			if ( $a->get_limit_usage_to_x_items() === $b->get_limit_usage_to_x_items() ) {
				if ( $a->get_amount() === $b->get_amount() ) {
					return $b->get_id() - $a->get_id();
				}
				return ( $a->get_amount() < $b->get_amount() ) ? -1 : 1;
			}
			return ( $a->get_limit_usage_to_x_items() < $b->get_limit_usage_to_x_items() ) ? -1 : 1;
		}
		return ( $a->sort < $b->sort ) ? -1 : 1;
	}

	/**
	 * Recalculate the discount tax rates based on package destination.
	 *
	 * @param WC_Cart $cart Cart object.
	 * @param array   $packages Cart packages.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function calculate_coupon_discount_tax_amounts( $cart, $packages ) {
		$coupons                     = $this->get_coupons_from_cart( $cart );
		$coupon_discount_tax_amounts = array();

		// Calculate the line value.
		foreach ( $packages as $idx => $package ) {
			$items = array();

			foreach ( $package['contents'] as $key => $cart_item ) {
				$item                     = new stdClass();
				$item->key                = $key;
				$item->object             = $cart_item;
				$item->product            = $cart_item['data'];
				$item->quantity           = $cart_item['quantity'];
				$item->taxable            = 'taxable' === $cart_item['data']->get_tax_status();
				$item->price_includes_tax = false;
				$item->price              = wc_add_number_precision_deep( $cart_item['line_subtotal'] );
				$item->tax_rates          = $this->get_item_tax_rates( $item, $package );

				$items[ $key ] = $item;
			}

			$discounts = new WC_Discounts( $cart );

			// Set items directly so the discounts class can see any tax adjustments made thus far using subtotals.
			$discounts->set_items( $items );

			foreach ( $coupons as $coupon ) {
				$discounts->apply_coupon( $coupon );
			}

			$coupon_discount_amounts = $discounts->get_discounts_by_coupon( true );

			// See how much tax was 'discounted' per item and per coupon.
			foreach ( $discounts->get_discounts( true ) as $coupon_code => $coupon_discounts ) {
				$coupon_discount_tax_amounts[ $coupon_code ] = isset( $coupon_discount_tax_amounts[ $coupon_code ] ) ? $coupon_discount_tax_amounts[ $coupon_code ] : 0;

				foreach ( $coupon_discounts as $item_key => $coupon_discount ) {
					$item = $items[ $item_key ];

					if ( $item->product->is_taxable() ) {
						// Item subtotals were sent, so set 3rd param.
						$item_tax = array_sum( WC_Tax::calc_tax( $coupon_discount, $item->tax_rates, $item->price_includes_tax ) );

						// Sum total tax.
						$coupon_discount_tax_amounts[ $coupon_code ] += $item_tax;

						// Remove tax from discount total.
						if ( $item->price_includes_tax ) {
							$coupon_discount_amounts[ $coupon_code ] -= $item_tax;
						}
					}
				}
			}
		}

		return wc_remove_number_precision_deep( $coupon_discount_tax_amounts );
	}

	/**
	 * Apply rounding to an array of taxes before summing.
	 *
	 * @param array $item_taxes Item taxes.
	 * @return array
	 */
	public function round_line_taxes( $item_taxes ) {

		foreach ( $item_taxes as $key => $item_tax ) {

			if ( 'yes' !== get_option( 'woocommerce_tax_round_at_subtotal' ) ) {

				$item_tax           = wc_add_number_precision( $item_tax, false );
				$item_tax           = wc_round_tax_total( $item_tax, 0 );
				$item_tax           = wc_remove_number_precision( $item_tax );
				$item_taxes[ $key ] = $item_tax;

			}
		}

		return $item_taxes;
	}

	/**
	 * Apply rounding to an array of shipping taxes before summing.
	 *
	 * @param array $shipping_taxes Shipping taxes.
	 *
	 * @return array
	 */
	public function round_shipping_taxes( $shipping_taxes ) {

		$shipping_taxes = wc_add_number_precision_deep( $shipping_taxes, false );
		$shipping_taxes = array_map( array( $this, 'round_item_subtotal' ), $shipping_taxes );
		$shipping_taxes = wc_remove_number_precision_deep( $shipping_taxes );

		return $shipping_taxes;
	}

	/**
	 * Apply rounding to item subtotal before summing.
	 *
	 * @since 3.9.0
	 * @param float $value Item subtotal value.
	 * @return float
	 */
	public function round_item_subtotal( $value ) {
		if ( 'yes' !== get_option( 'woocommerce_tax_round_at_subtotal' ) ) {
			$value = NumberUtil::round( $value );
		}
		return $value;
	}

	/**
	 * This method manipulate the subtotal item value for price with tax included.
	 *
	 * @param float  $product_subtotal Product subtotal in cart.
	 * @param array  $cart_item Cart item.
	 * @param string $cart_item_key Cart item key.
	 *
	 * @return float
	 */
	public function subtotal_item_include_taxes( $product_subtotal, $cart_item, $cart_item_key ) {
		$packages     = wcms_session_isset( 'wcms_packages_after_tax_calc' ) ? wcms_session_get( 'wcms_packages_after_tax_calc' ) : wcms_session_get( 'wcms_packages' );
		$tax_based_on = get_option( 'woocommerce_tax_based_on', 'billing' );

		// only process subtotal if multishipping is being used.
		if ( ( is_array( $packages ) && count( $packages ) <= 1 ) || 'shipping' !== $tax_based_on ) {
			return $product_subtotal;
		}

		// Value that needs to be updated.
		$package_item = array(
			'line_total'        => 0,
			'line_tax'          => 0,
			'line_subtotal'     => 0,
			'line_subtotal_tax' => 0,
		);

		if ( is_array( $packages ) ) {
			// Calculate the line value.
			$number_of_package = count( $packages );
			for ( $i = 0; $i < $number_of_package; $i++ ) {
				if ( isset( $packages[ $i ]['contents'][ $cart_item_key ] ) ) {
					$new_cart_item                      = $packages[ $i ]['contents'][ $cart_item_key ];
					$package_item['line_total']        += isset( $new_cart_item['line_total'] ) ? $new_cart_item['line_total'] : 0;
					$package_item['line_tax']          += isset( $new_cart_item['line_tax'] ) ? $new_cart_item['line_tax'] : 0;
					$package_item['line_subtotal']     += isset( $new_cart_item['line_subtotal'] ) ? $new_cart_item['line_subtotal'] : 0;
					$package_item['line_subtotal_tax'] += isset( $new_cart_item['line_subtotal_tax'] ) ? $new_cart_item['line_subtotal_tax'] : 0;
				}
			}
		}

		// Replace the cart item with the modified one.
		if ( isset( $new_cart_item ) ) {
			$cart_item = $new_cart_item;

			foreach ( $package_item as $key => $value ) {
				$cart_item[ $key ] = $value;
			}
		}

		$subtotal = $this->wcms->get_cart_item_subtotal( $cart_item );
		$taxable  = $cart_item['data']->is_taxable();

		if ( $taxable && ( $cart_item['line_total'] + $cart_item['line_tax'] ) !== $subtotal ) {

			if ( 'excl' === WC()->cart->get_tax_price_display_mode() ) {
				$row_price = $cart_item['line_subtotal'];

				$product_subtotal = wc_price( $row_price );

				if ( WC()->cart->prices_include_tax && $cart_item['line_tax'] > 0 ) {
					$product_subtotal .= ' <small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>';
				}
			} else {
				$row_price = $cart_item['line_subtotal'] + $cart_item['line_subtotal_tax'];

				$product_subtotal = wc_price( $row_price );

				if ( ! WC()->cart->prices_include_tax && $cart_item['line_tax'] > 0 ) {
					$product_subtotal .= ' <small class="tax_label">' . WC()->countries->inc_tax_or_vat() . '</small>';
				}
			}
		}

		return $product_subtotal;
	}

	/**
	 * This method get item tax rates based on package destination.
	 *
	 * @param array $item Cart item.
	 * @param array $package Cart package.
	 *
	 * @since 3.6.34
	 * @return array
	 */
	public function get_item_tax_rates( $item, $package ) {

		$customer = new WC_Customer();

		$customer_country  = $package['destination']['country'];
		$customer_state    = $package['destination']['state'];
		$customer_postcode = $package['destination']['postcode'];
		$customer_city     = $package['destination']['city'];

		$customer->set_billing_location( $customer_country, $customer_state, $customer_postcode, $customer_city );
		$customer->set_shipping_location( $customer_country, $customer_state, $customer_postcode, $customer_city );

		$item_tax_rates = WC_Tax::get_rates( $item->product->get_tax_class(), $customer );

		return $item_tax_rates;
	}

	/**
	 * Create order shipments.
	 *
	 * @param int|WC_Order $new_order Either Order ID or Order object.
	 */
	public function create_order_shipments( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			return;
		}

		$order_id = $order->get_id();

		$multishipping = $order->get_meta( '_multiple_shipping' );
		$created       = $order->get_meta( '_shipments_created' );
		$packages      = $order->get_meta( '_wcms_packages' );
		$shipment      = $this->wcms->shipments;

		if ( 'yes' !== $multishipping || 'yes' === $created ) {
			return;
		}

		foreach ( $packages as $i => $package ) {
			$shipment->create_from_package( $package, $i, $order_id );
		}

		$order->update_meta_data( '_shipments_created', 'yes' );
		$order->save();
	}

	/**
	 * Prevent updating customer data when the cart has multi shipping.
	 *
	 * @param boolean $update Update.
	 *
	 * @return boolean
	 */
	public function prevent_customer_data_update( $update ) {
		if ( $this->wcms->cart->cart_has_multi_shipping() ) {
			return false;
		}

		return $update;
	}

	/**
	 * Store shipping address on certain condition.
	 *
	 * @param int|WC_Customer $customer Either customer object or customer ID.
	 */
	public function maybe_store_shipping_address( $customer ) {
		$customer = ( $customer instanceof WC_Customer ) ? $customer : new WC_Customer( $customer );

		if ( ! $customer ) {
			return;
		}

		$customer_id = $customer->get_id();

		if ( ! $this->wcms->cart->cart_has_multi_shipping() ) {
			return;
		}

		$checkout = WC()->checkout;

		// Check if we should update customer data.
		remove_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );

		/**
		 * Filter to be able updating the customer data.
		 *
		 * @param boolean     Should update customer data?
		 * @param WC_Checkout Checkout object.
		 *
		 * @since 3.3.23
		 */
		$update_customer_data = apply_filters( 'woocommerce_checkout_update_customer_data', true, $checkout );
		add_filter( 'woocommerce_checkout_update_customer_data', array( $this, 'prevent_customer_data_update' ), 90, 1 );

		if ( $update_customer_data ) {

			// Save billing address.
			if ( $checkout->checkout_fields['billing'] ) {
				foreach ( array_keys( $checkout->checkout_fields['billing'] ) as $field ) {
					$field_name = str_replace( 'billing_', '', $field );
					update_user_meta( $customer_id, 'billing_' . $field_name, $checkout->get_posted_address_data( $field_name ) );
				}
			}

			// Get user addresses.
			$addresses = $this->wcms->address_book->get_user_addresses( $customer_id );

			// Add guest addresses (needed for when account is created at checkout).
			$guest_addresses = ( wcms_session_isset( 'user_addresses' ) ) ? wcms_session_get( 'user_addresses' ) : array();
			$addresses       = array_merge( $addresses, $guest_addresses );

			$filtered_addresses = array_filter(
				$addresses,
				function ( $address ) {
					return ! empty( $address['default_address'] );
				}
			);

			if ( count( $filtered_addresses ) > 0 ) {
				$default_address = array_shift( $filtered_addresses );
				wcms_session_set( 'user_default_address', $default_address );
			}

			$this->wcms->address_book->save_user_addresses( $customer_id, $addresses );
		}
	}

	/**
	 * Set the shipping methods from WCMS session when shipping rate is selected from store API.
	 *
	 * @param string|null      $package_id The sanitized ID of the package being updated. Null if all packages are being updated.
	 * @param string           $rate_id The sanitized chosen rate ID for the package.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function select_shipping_rate_on_store_api( $package_id, $rate_id, $request ) {
		$this->set_shipping_methods_on_block();
	}

	/**
	 * Set the shipping methods from WCMS session when customer is being updated from store API.
	 *
	 * @param \WC_Customer     $customer Customer object.
	 * @param \WP_REST_Request $request Full details about the request.
	 */
	public function update_customer_on_store_api( $customer, $request ) {
		$this->set_shipping_methods_on_block();
	}

	/**
	 * Set the shipping methods on WCMS session for WC blocks.
	 */
	public function set_shipping_methods_on_block() {
		// Update chosen shipping methods to match with WooCommerce session variable.
		$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );

		if ( ! is_array( $chosen_shipping_methods ) || empty( $chosen_shipping_methods ) ) {
			return;
		}

		// Will always set new session for shipping methods.
		// It's being done this way because checkout blocks is already compatible with multiple package.
		// And the plugin can just grab the chosen shipping methods and save it into shipping methods wcms session.
		$methods = array();
		foreach ( $chosen_shipping_methods as $key => $method ) {
			if ( 'undefined' === $key ) {
				continue;
			}

			$methods[ $key ] = array(
				'id'    => $method,
				'label' => $method,
			);
		}

		wcms_session_set( 'shipping_methods', $methods );
	}

	/**
	 * Restore default shipping address.
	 *
	 * @param int|WC_Order $new_order Either order object or order ID.
	 */
	public function restore_customer_default_data( $new_order ) {
		$order = ( $new_order instanceof WC_Order ) ? $new_order : wc_get_order( $new_order );

		if ( ! $order ) {
			wcms_session_delete( 'user_default_address' );
			return;
		}

		$multishipping = $order->get_meta( '_multiple_shipping' );

		if ( 'yes' !== $multishipping ) {
			wcms_session_delete( 'user_default_address' );
			return;
		}

		// Use customer session if it's a guest and use customer data if it's a user.
		$customer_id = $order->get_customer_id();
		$customer    = ( 0 === $customer_id ) ? WC()->customer : new WC_Customer( $customer_id );

		$this->restore_default_shipping_address( $customer );
		wcms_session_delete( 'user_default_address' );
	}

	/**
	 * Force to use billing address when WCMS is active.
	 * It needs to force it in order to hide the shipping address on WC Blocks.
	 *
	 * @param mixed $value  Option value.
	 * @param mixed $option Option.
	 * @param mixed $default_value Option default value.
	 */
	public function force_use_billing_address( $value, $option, $default_value ) {
		// force billing address only on cart blocks or checkout blocks.
		if ( $this->wcms->cart->cart_has_multi_shipping() && wc_get_page_id( 'myaccount' ) !== get_the_ID() ) {
			return 'billing_only';
		}

		return $value;
	}

	/**
	 * Calculate order taxes only for WC blocks.
	 *
	 * @param boolean  $and_taxes And calculate taxes.
	 * @param WC_Order $order Order object.
	 *
	 * @throws RouteException When cart cannot be loaded.
	 */
	public function calculate_order_taxes_for_wc_blocks( $and_taxes, $order ) {
		if ( ! wcms_session_isset( 'wcms_packages' ) ) {
			return;
		}

		$multishipping = $order->get_meta( '_multiple_shipping' );
		$wcms_packages = $order->get_meta( '_wcms_packages' );

		if ( 'yes' !== $multishipping || ! ( is_array( $wcms_packages ) && count( $wcms_packages ) > 1 ) ) {
			return;
		}

		$cart = WC()->cart;

		if ( ! $cart || ! $cart instanceof WC_Cart ) {
			throw new RouteException( 'woocommerce_rest_cart_error', __( 'Unable to retrieve cart.', 'woocommerce' ), 500 );
		}

		$cart_hashes = array(
			'line_items' => $cart->get_cart_hash(),
			'shipping'   => md5( wp_json_encode( $cart->shipping_methods ) ),
			'fees'       => md5( wp_json_encode( $cart->get_fees() ) ),
			'coupons'    => md5( wp_json_encode( $cart->get_applied_coupons() ) ),
			'taxes'      => md5( wp_json_encode( $cart->get_taxes() ) ),
		);

		if ( $order->get_cart_hash() !== $cart_hashes['line_items'] ) {
			$order->set_cart_hash( $cart_hashes['line_items'] );
			$order->remove_order_items( 'line_item' );
			WC()->checkout->create_order_line_items( $order, $cart );
		}

		if ( $order->get_meta_data( '_shipping_hash' ) !== $cart_hashes['shipping'] ) {
			$order->update_meta_data( '_shipping_hash', $cart_hashes['shipping'] );
			$order->remove_order_items( 'shipping' );
			WC()->checkout->create_order_shipping_lines( $order, WC()->session->get( 'chosen_shipping_methods' ), WC()->shipping()->get_packages() );
		}

		if ( $order->get_meta_data( '_coupons_hash' ) !== $cart_hashes['coupons'] ) {
			$order->remove_order_items( 'coupon' );
			$order->update_meta_data( '_coupons_hash', $cart_hashes['coupons'] );
			WC()->checkout->create_order_coupon_lines( $order, $cart );
		}

		if ( $order->get_meta_data( '_fees_hash' ) !== $cart_hashes['fees'] ) {
			$order->update_meta_data( '_fees_hash', $cart_hashes['fees'] );
			$order->remove_order_items( 'fee' );
			WC()->checkout->create_order_fee_lines( $order, $cart );
		}

		if ( $order->get_meta_data( '_taxes_hash' ) !== $cart_hashes['taxes'] ) {
			$order->update_meta_data( '_taxes_hash', $cart_hashes['taxes'] );
			$order->remove_order_items( 'tax' );
			WC()->checkout->create_order_tax_lines( $order, $cart );
		}

		$cart_tax          = floatval( $cart->get_cart_contents_tax() ) + floatval( $cart->get_fee_tax() );
		$cart_shipping_tax = floatval( $cart->get_shipping_tax() );
		
		$order->set_cart_tax( $cart_tax );
		$order->set_shipping_tax( $cart_shipping_tax );
		
		$cart_total = $cart->get_total( 'edit' );
		$order->set_total( $cart_total );
	}

	/**
	 * Clear session for old cart/checkout page.
	 */
	public function legacy_clear_session() {
		$this->wcms->clear_session();
	}

	/**
	 * Clear session for WC cart/checkout blocks.
	 */
	public function blocks_clear_session() {
		unset( wc()->session->cart_item_addresses );
		unset( wc()->session->wcms_item_addresses );
		unset( wc()->session->cart_address_sigs );
		unset( wc()->session->address_relationships );
		unset( wc()->session->shipping_methods );
		unset( wc()->session->wcms_original_cart );
		unset( wc()->session->wcms_packages );
		unset( wc()->session->wcms_packages_after_tax_calc );
		unset( wc()->session->wcms_item_delivery_dates );
		unset( wc()->session->user_default_address );
		unset( wc()->session->wcms_package_notes );
		unset( wc()->session->wcms_delivery_dates );
		unset( wc()->session->wcms_package_gifts );
	}

	/**
	 * Reset multiple shipping address session.
	 */
	public function reset_multiple_shipping_address() {
		$nonce = isset( $_GET['nonce'] ) ? sanitize_text_field( wp_unslash( $_GET['nonce'] ) ) : '';
		if ( empty( $_GET['wcms_reset_address'] ) || ! wp_verify_nonce( $nonce, 'wcms_reset_address_security' ) ) {
			return;
		}

		$customer = WC()->customer;

		if ( $customer instanceof WC_Customer && 0 === $customer->get_id() ) {
			$this->restore_default_shipping_address( $customer );
		}

		wcms_session_delete( 'cart_item_addresses' );
		wcms_session_delete( 'cart_address_sigs' );
		wcms_session_delete( 'address_relationships' );
		wcms_session_delete( 'shipping_methods' );
		wcms_session_delete( 'wcms_original_cart' );
		wcms_session_delete( 'wcms_item_addresses' );
		wcms_session_delete( 'user_default_address' );
		wcms_session_delete( 'wcms_package_notes' );
		wcms_session_delete( 'wcms_delivery_dates' );
		wcms_session_delete( 'wcms_package_gifts' );
	}

	/**
	 * Restore default shipping address.
	 *
	 * @param WC_Customer $customer Customer object.
	 */
	public function restore_default_shipping_address( $customer ) {
		// Get user addresses.
		$user_default_address = ( wcms_session_isset( 'user_default_address' ) ) ? wcms_session_get( 'user_default_address' ) : array();
		$legacy_address_keys  = array(
			'address_2',
			'city',
			'state',
			'postcode',
			'country',
		);

		foreach ( $user_default_address as $key => $value ) {
			// Use setters where available.
			$key = in_array( $key, $legacy_address_keys ) ? 'shipping_' . $key : $key;

			if ( is_callable( array( $customer, "set_{$key}" ) ) ) {
				$customer->{"set_{$key}"}( $value );

				// Store custom fields prefixed with wither billing_.
			} elseif ( 0 === stripos( $key, 'billing_' ) ) {
				$customer->update_meta_data( $key, $value );
			}
		}

		$customer->save();
	}
}
</file>

<file path="templates/shipping-address-table.php">
<?php
/**
 * Shipping address table template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<form method="post" action="" id="address_form">
	<div class="wcms-error-notice">
		<?php
		wc_get_template(
			'notices/notice.php',
			array(
				'notices' => array(
					array(
						'notice' => esc_html__( 'Shipping addresses cannot be saved. Please make sure the item quantity usage in the addresses match with cart quantity.', 'woocommerce-shipping-multiple-addresses' ),
					),
				),
			)
		);
		?>
	</div>
	<div class="wcms-error-address-empty hidden">
		<?php
		wc_get_template(
			'notices/error.php',
			array(
				'notices' => array(
					array(
						'notice' => esc_html__( 'There is no shipping addresses. Please add new shipping address.', 'woocommerce-shipping-multiple-addresses' ),
					),
				),
			)
		);
		?>
	</div>
	<?php
	// Set the address fields.
	foreach ( $addresses as $x => $addr ) {
		if ( empty( $addr ) ) {
			continue;
		}

		$address_fields = WC()->countries->get_address_fields( $addr['shipping_country'], 'shipping_' );

		$address           = array();
		$formatted_address = false;

		foreach ( $address_fields as $field_name => $field ) {
			$addr_key             = str_replace( 'shipping_', '', $field_name );
			$address[ $addr_key ] = ( isset( $addr[ $field_name ] ) ) ? $addr[ $field_name ] : '';
		}

		if ( ! empty( $address ) ) {
			$formatted_address = wcms_get_formatted_address( $address );
			$json_address      = wp_json_encode( $address );
			$json_address      = wc_esc_json( $json_address );
		}

		if ( ! $formatted_address ) {
			continue;
		}
		?>
		<div style="display: none;">
			<?php
			/**
			 * Action to add element on wcms address form.
			 *
			 * @param WC_Checkout $checkout checkout object.
			 *
			 * @since 3.3
			 */
			do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
			?>
		<input type="hidden" name="addresses[]" value="<?php echo esc_attr( $x ); ?>" />
		<textarea style="display:none;"><?php echo $json_address; // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- already escaped above. ?></textarea>
		</div>
		<?php
	}
	$ms_settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

	$add_url = add_query_arg( 'address-form', '1' );
	?>

	<div>
		<a class="h2-link" href="<?php echo esc_url( $add_url ); ?>"><?php esc_html_e( 'Add a new shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></a>

		<?php
		if ( isset( $ms_settings['cart_duplication'] ) && 'no' !== $ms_settings['cart_duplication'] ) :
			$dupe_url = add_query_arg(
				array(
					'duplicate-form' => '1',
					'_wcmsnonce'     => wp_create_nonce( 'wcms-duplicate-cart' ),
				),
				get_permalink( wc_get_page_id( 'multiple_addresses' ) )
			);
			?>
			<div style="float: right;">
				<a class="h2-link" href="<?php echo esc_url( $dupe_url ); ?>"><?php esc_html_e( 'Duplicate Cart', 'woocommerce-shipping-multiple-addresses' ); ?></a>
				<img class="help_tip" title="<?php esc_html_e( 'Duplicating your cart will allow you to ship the exact same cart contents to multiple locations. This will also increase the price of your purchase.', 'woocommerce-shipping-multiple-addresses' ); ?>" src="<?php echo WC()->plugin_url(); ?>/assets/images/help.png" height="16" width="16">
			</div>
			<?php
		endif;
		?>
	</div>

	<table class="wc-shipping-multiple-addresses shop_table cart" cellspacing="0">
		<thead>
			<tr>
				<th class="product-name" width="20%"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th class="product-quantity" width="12%"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<?php
				/**
				 * Action to add a column within the shipping address table head.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_ms_address_table_head' );
				?>
				<th class="shipping-address" width="25%"><?php esc_html_e( 'Shipping Address', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th class="remove-item" width="33%">&nbsp;</th>
			</tr>
		</thead>
		<tfoot>
			<tr id="row-add-input">
				<td class="add-product">
					<select name="item_product" class="product-select" id="row_add_item_product">
						<option value=""><?php esc_html_e( 'Select Item', 'woocommerce-shipping-multiple-addresses' ); ?></option>
					<?php

					foreach ( $contents as $key => $content ) {
						echo '<option value="' . esc_attr( $key ) . '">' . esc_html( $content['data']->get_name() ) . '</option>';
					}
					?>
					</select>
				</td>
				<td class="add-quantity">
					<div class="quantity">
						<input type="number" name="item_qty" id="row_add_item_qty" min="0" class="input-text qty text input-quantity" value="0" />
					</div>
				</td>
				<?php
				/**
				 * Action to add a column within the shipping address table specifically on add row form.
				 *
				 * @since 4.1.0
				 */
				do_action( 'wc_ms_multiple_address_table_add_row' );
				?>
				<td class="add-address">
					<select name="item_address" class="address-select" id="row_add_item_address">
						<option value=""><?php esc_html_e( 'Select Address', 'woocommerce-shipping-multiple-addresses' ); ?></option>
					<?php

					foreach ( $addresses as $addr_key => $address ) {
						$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
						$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
						$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

						echo '<option value="' . $addr_key . '">' . $formatted . '</option>';
					}
					?>
					</select>
				</td>
				<td class="add-button"><button id="wcms-add-row" data-error-text="<?php esc_attr_e( 'Please input the empty fields!', 'woocommerce-shipping-multiple-addresses' ); ?>" class="button alt"><?php esc_html_e( 'Add Item(s)', 'woocommerce-shipping-multiple-addresses' ); ?></button></td>
			</tr>
			<tr id="row-availability-info">
				<?php
				/**
				 * Filter to manipulate the colspan value.
				 *
				 * @param int Colspan value.
				 *
				 * @since 4.1.0
				 */
				?>
				<td colspan="<?php echo esc_attr( apply_filters( 'wc_ms_default_availability_colspan', 4 ) ); ?>" id="availability-info">
					<div class="availability-title"><?php esc_html_e( 'Item quantity usage:', 'woocommerce-shipping-multiple-addresses' ); ?></div>
					<?php
					foreach ( $contents as $key => $value ) {
						$_product = $value['data'];
						$pid      = $value['product_id'];
						?>
						<div class="availability-product <?php echo esc_attr( $key ); ?>" data-cart-key="<?php echo esc_attr( $key ); ?>">
							<span class="product-name"><?php echo esc_html( $_product->get_name() ); ?></span>
							<div class="usage-percentage">
								<div class="percentage-bar">
									<span class="bar-text"><?php esc_html_e( 'Usage:', 'woocommerce-shipping-multiple-addresses' ); ?></span>
									<span class="bar-percent"></span>
								</div>
							</div>
						</div>
						<?php
					}
					?>
				</td>
			</tr>
		</tfoot>
		<tbody id="content-addresses">
			<tr class="row-template">
				<td class="product-name">
					<div class="product-text"></div>
					<input type="hidden" class="input-product" name="" value="" />
				</td>
				<td class="product-quantity">
					<div class="quantity">
						<input type="number" class="input-quantity input-text qty text" name="" value="" />
					</div>
				</td>
				<?php
				/**
				 * Action to add a column within the shipping address table specifically on add row template.
				 *
				 * @since 4.1.0
				 */
				do_action( 'wc_ms_multiple_address_table_template_add_row' );
				?>
				<td class="shipping-address">
					<select name="" class="address-select input-address">
					<?php

					foreach ( $addresses as $addr_key => $address ) {
						$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
						$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
						$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

						echo '<option value="' . $addr_key . '">' . $formatted . '</option>';
					}
					?>
					</select>
				</td>
				<td class="row-action">
					<button class="delete-item"><?php esc_html_e( 'Delete item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
					<button class="split-item"><?php esc_html_e( 'Split item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
				</td>
			</tr>
		<?php
		$row_index = 0;

		foreach ( $contents as $key => $value ) :
			$_product = $value['data'];
			$pid      = $value['product_id'];

			if ( ! $_product->needs_shipping() ) {
				continue;
			}

			if ( empty( $relations ) ) {
				continue;
			}

			foreach ( $relations as $adrs_idx => $cart_keys ) {
				$relation_qty = 0;

				foreach ( $cart_keys as $cart_key ) {
					if ( $cart_key === $key ) {
						$relation_qty++;
					}
				}

				if ( 0 === $relation_qty ) {
					continue;
				}
				?>
				<tr class="row-added" data-row-index="<?php echo esc_attr( $row_index ); ?>" data-cart-key="<?php echo esc_attr( $key ); ?>">
					<td class="product-name">
						<div class="product-text">
							<?php
							/**
							 * Filter to amnipulate the product title.
							 *
							 * @param string Product title.
							 * @param array  $value Cart item value.
							 *
							 * @since 3.3
							 */
							echo esc_html( apply_filters( 'wcms_product_title', $_product->get_name(), $value ) );
							?>
						</div>
						<input type="hidden" class="input-product" data-row-index="<?php echo esc_attr( $row_index ); ?>" name="items[<?php echo esc_attr( $key ); ?>][product][]" value="<?php echo esc_attr( $_product->get_id() ); ?>" />
					</td>
					<td class="product-quantity">
						<?php
						$product_quantity = woocommerce_quantity_input(
							array(
								'classes'     => array( 'input-quantity', 'input-text', 'qty', 'text' ),
								'input_name'  => "items[{$key}][qty][]",
								'input_value' => $relation_qty,
								'max_value'   => $_product->backorders_allowed() ? '' : $_product->get_stock_quantity(),
							),
							$_product,
							false
						);
						echo $product_quantity;
						?>
					</td>
					<?php
					/**
					 * Action to add a column within the shipping address table.
					 *
					 * @param string $key Cart item keys.
					 * @param array  $value Cart item value.
					 * @param string $address_key Address index or key.
					 *
					 * @since 3.3
					 */
					do_action( 'wc_ms_multiple_address_table_row', $key, $value, $adrs_idx );
					?>
					<td class="shipping-address">
						<select name="items[<?php echo esc_attr( $key ); ?>][address][]" class="address-select input-address" data-row-index="<?php echo esc_attr( $row_index ); ?>">
						<?php

						foreach ( $addresses as $addr_key => $address ) {
							$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
							$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
							$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

							echo '<option value="' . $addr_key . '" ' . selected( $adrs_idx, $addr_key, false ) . '>' . $formatted . '</option>';
							$selected = '';
						}
						?>
						</select>
					</td>
					<td class="row-action">
						<button class="delete-item" data-row-index="<?php echo esc_attr( $row_index ); ?>"><?php esc_html_e( 'Delete item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
						<button class="split-item" data-row-index="<?php echo esc_attr( $row_index ); ?>"><?php esc_html_e( 'Split item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
					</td>
				</tr>
				<?php
				$row_index++;
			}
		endforeach;
		?>
		</tbody>
	</table>

	<div class="form-row">
		<?php wp_nonce_field( 'shipping_address_action' ); ?>
		<input type="hidden" name="delete[index]" id="delete_index" value="" />
		<input type="hidden" name="delete[key]" id="delete_key" value="" />
		<input type="hidden" name="shipping_type" value="item" />
		<input type="hidden" name="shipping_address_action" value="save" />

		<div class="update-shipping-addresses">
			<input type="submit" name="update_quantities" class="button" value="<?php esc_attr_e( 'Update', 'woocommerce-shipping-multiple-addresses' ); ?>" />
		</div>

		<div class="set-shipping-addresses">
			<input class="button alt" type="submit" name="set_addresses" value="<?php esc_attr_e( 'Save Addresses and Continue', 'woocommerce-shipping-multiple-addresses' ); ?>" />
		</div>

	</div>

	<div class="clear"></div>

	<small class="address-form-note">
		<?php esc_html_e( 'Please note: To send a single item to more than one person, you must change the quantity of that item to match the number of people you\'re sending it to, then click the Update button.', 'woocommerce-shipping-multiple-addresses' ); ?>
	</small>

</form>
</file>

<file path="package.json">
{
  "name": "woocommerce-shipping-multiple-addresses",
  "title": "WooCommerce Shipping Multiple Addresses",
  "version": "4.2.9",
  "homepage": "https://woocommerce.com/products/shipping-multiple-addresses/",
  "repository": {
    "type": "git",
    "url": "git://github.com/woocommerce/woocommerce-shipping-multiple-addresses.git"
  },
  "devDependencies": {
    "clean-css-cli": "^4.3.0",
    "uglify-js": "^3.14.3",
    "@wordpress/scripts": "^27.4.0",
    "babel-loader": "^9.1.3",
    "babel-minify": "^0.5.2",
    "css-loader": "^6.8.1",
    "mini-css-extract-plugin": "^2.9.0",
    "sass": "^1.62.1",
    "sass-loader": "^13.3.1",
    "style-loader": "^3.3.3",
    "webpack": "^5.91.0",
    "node-wp-i18n": "~1.2.3"
  },
  "config": {
    "use_pnpm": true,
    "translate": true,
    "use_gh_release_notes": true,
    "paths": {
      "js": "assets/js/*.js",
      "js_min": "assets/js/*.min.js",
      "css": "assets/css/*.css",
      "sass": "assets/sass",
      "cssfolder": "assets/css"
    }
  },
  "scripts": {
    "build": "pnpm run build:prod && pnpm run archive",
    "build:dev": "composer install -o && pnpm run makepot && pnpm run uglify && pnpm run sass && webpack --env mode=development",
    "build:prod": "composer install --no-dev -o && pnpm run makepot && pnpm run uglify && pnpm run sass && webpack --env mode=production",
    "archive": "composer archive --file=$npm_package_name --format=zip",
    "postarchive": "rm -rf $npm_package_name && unzip $npm_package_name.zip -d $npm_package_name && rm $npm_package_name.zip && zip -r $npm_package_name.zip $npm_package_name && rm -rf $npm_package_name",
    "preuglify": "rm -f $npm_package_config_paths_js_min",
    "uglify": "for f in $npm_package_config_paths_js; do file=${f%.js}; node_modules/.bin/uglifyjs $f -c -m > $file.min.js; done",
    "presass": "rm -f $npm_package_config_paths_css",
    "sass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --no-source-map --style compressed",
    "watchsass": "sass $npm_package_config_paths_sass:$npm_package_config_paths_cssfolder --watch",
    "postsass": "for f in $npm_package_config_paths_css; do file=${f%.css}; node_modules/.bin/cleancss -o $file.css $f; done",
    "makepot": "wpi18n makepot --domain-path languages --pot-file $npm_package_name.pot --type plugin --main-file $npm_package_name.php --exclude node_modules,tests,docs",
    "preminify": "rm -f assets/js/*.min.js",
    "minify": "for f in assets/js/*.js; do file=${f%.js}; node_modules/.bin/minify $f --out-file $file.min.js; done",
    "sass:watch": "pnpm run sass --watch",
    "wp-start": "wp-scripts start"
  },
  "engines": {
    "node": "^22.14.0",
    "pnpm": "^10.4.1"
  },
  "peerDependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  }
}
</file>

<file path="woocommerce-shipping-multiple-addresses.php">
<?php
/**
 * Plugin Name: WooCommerce Ship to Multiple Addresses
 * Plugin URI: https://woocommerce.com/products/shipping-multiple-addresses/
 * Description: Allow customers to ship orders with multiple products or quantities to separate addresses instead of forcing them to place multiple orders for different delivery addresses.
 * Version: 4.2.9
 * Author: WooCommerce
 * Author URI: https://woocommerce.com
 * Text Domain: woocommerce-shipping-multiple-addresses
 * Domain Path: /languages
 * Requires Plugins: woocommerce
 * Requires PHP: 7.4
 * Requires at least: 6.7
 * Tested up to: 6.8
 * WC requires at least: 9.9
 * WC tested up to: 10.1
 * Woo: 18741:aa0eb6f777846d329952d5b891d6f8cc
 *
 * Copyright 2020 WooCommerce.
 * License: GNU General Public License v3.0
 * License URI: http://www.gnu.org/licenses/gpl-3.0.html
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! defined( 'WC_MS_FILE' ) ) {
	define( 'WC_MS_FILE', __FILE__ );
}

if ( ! defined( 'WC_MS_ABSPATH' ) ) {
	define( 'WC_MS_ABSPATH', trailingslashit( __DIR__ ) );
}

define( 'WC_MS_PLUGIN_URL', trailingslashit( plugins_url( '', __FILE__ ) ) );
define( 'WC_MS_DIST_DIR', WC_MS_ABSPATH . 'dist/' );
define( 'WC_MS_DIST_URL', WC_MS_PLUGIN_URL . 'dist/' );

require_once WC_MS_ABSPATH . 'class-wc-ms-compatibility.php';

/**
 * WooCommerce fallback notice.
 *
 * @since 3.6.15
 * @return void
 */
function woocommerce_shipping_multiple_addresses_missing_wc_notice() {
	/* translators: %s WC download URL link. */
	echo '<div class="error"><p><strong>' . sprintf( esc_html__( 'Multiple Addresses requires WooCommerce to be installed and active. You can download %s here.', 'woocommerce-shipping-multiple-addresses' ), '<a href="https://woocommerce.com/" target="_blank">WooCommerce</a>' ) . '</strong></p></div>';
}

if ( ! class_exists( 'WC_Ship_Multiple' ) ) :
	define( 'WC_SHIPPING_MULTIPLE_ADDRESSES_VERSION', '4.2.9' ); // WRCS: DEFINED_VERSION.
	include_once WC_MS_ABSPATH . 'includes/class-wc-ship-multiple.php';
endif;

add_action( 'plugins_loaded', 'woocommerce_shipping_multiple_addresses_init' );

/**
 * Declare compatibility with WC Cart and Checkout blocks.
 */
function woocommerce_shipping_multiple_addresses_blocks_compatibility() {
	if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'cart_checkout_blocks', WC_MS_FILE, true );
	}
}

/**
 * Initializes the extension.
 *
 * @since 3.6.15
 * @return void
 */
function woocommerce_shipping_multiple_addresses_init() {
	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'woocommerce_shipping_multiple_addresses_missing_wc_notice' );
		return;
	}

	add_action( 'before_woocommerce_init', 'woocommerce_shipping_multiple_addresses_blocks_compatibility' );

	$GLOBALS['wcms'] = new WC_Ship_Multiple();
}
</file>

<file path="changelog.txt">
*** Ship to Multiple Addresses Changelog ***

2025-08-18 - version 4.2.9
* Tweak - WooCommerce 10.1 compatibility.

2025-06-30 - version 4.2.8
* Tweak - WooCommerce 10.0 compatibility.

2025-06-09 - version 4.2.7
* Tweak - WooCommerce 9.9 compatibility.

2025-06-03 - version 4.2.6
* Update Requires headers for WooCommerce compatibility.
* Update to ubuntu-latest to fix QIT tests.

2025-04-07 - version 4.2.5
* Tweak - WooCommerce 9.8 compatibility.

2025-03-17 - version 4.2.4
* Tweak - Improve PHP 8.1+ compatibility.

2025-03-04 - version 4.2.3
* Fix   - Error on address selection page where the same product ids are used on different cart keys.
* Tweak - WooCommerce 9.7 Compatibility.

2025-01-27 - version 4.2.2
* Tweak - PHP 8.4 Compatibility.

2025-01-20 - version 4.2.1
* Fix   - Ensure the user can set the multiple addresses after WooCommerce Role-Based Payment/Shipping plugin is installed.

2024-12-09 - version 4.2.0
* Add   - Enhance the address selection page to let the user be able to group products together on the same address.
* Fix   - Ensure the guest user gets the correct shipping total and tax on checkout block.
* Fix   - Free shipping coupon not working when the shipping debug is disabled.
* Fix   - Stored addresses dropdown now only shows addresses for available shipping countries.
* Tweak - Change the filter name `wc_ms_multiple_shipping_checkout_locale` into `wc_ms_checkout_locale`.

2024-10-29 - version 4.1.1
* Fix   - Incorrect tax total when using checkout blocks.
* Tweak - WordPress 6.7 compatibility.
* Fix   - Change the textdomain from `wc_shipping_multiple_address` into `woocommerce-shipping-multiple-addresses`.
* Fix   - Automated translation from WordPress.com.

2024-10-15 - version 4.1.0
* Add - WooCommerce Blocks compatibility.
* Fix - [woocommerce_select_multiple_addresses] shortcode is not rendering state dropdown on certain condition.

2024-07-22 - version 4.0.7
* Fix   - Gift flag not showing on order admin page.
* Fix   - Customer note is not displayed on order edit page.
* Tweak - Change the delete button text on shipping address page to "Delete item".

2024-07-02 - version 4.0.6
* Tweak - WordPress 6.6 and WooCommerce 9.0 Compatibility.

2024-05-20 - version 4.0.5
* Fix - All shipping address columns are blank in "Customer / Order / CSV Export" plugin.
* Fix - Default address deleted on second order when adding a new address from the duplicated cart page.
* Fix - "Partial Order Completed Email > Send Email" settings is being ignored.

2024-04-09 - version 4.0.4
* Fix - Prevent child order created from multiple address to update the stock.

2024-04-02 - version 4.0.3
* Fix   - Addresses cannot be saved on My Account > Addresses page.

2024-03-25 - version 4.0.2
* Tweak - WordPress 6.5 Compatibility.

2024-03-04 - version 4.0.1
* Fix    - Apply WordPress coding standards.

2023-10-16 - version 4.0.0
* Add    - High-Performance Order Storage (HPOS) compatibility.
* Add    - `wc_ms_new_shipment_before_save` action to modify shipment object before it is saved.
* Fix    - Default shipping address being overwritten when user using saved address.
* Fix    - Pass order ID to `wc_ms_shop_table_row` hook everywhere.
* Fix    - Persist shipment data in `WC_MS_Order_Shipment::create_from_package()`.
* Fix    - Order data exporter on the "Export Personal Data" admin screen crashing.
* Fix    - Ensure value is an array in `get_product_shipping_method()`.
* Update - Drop support for WC < 3.0.

2023-10-10 - version 3.9.0
* Fix   - Default shipping address being overwritten when user using saved address.

2023-10-06 - version 3.8.10
* Update - Security update.

2023-09-05 - version 3.8.9
* Fix    - Package note does not automatically break.
* Fix    - Save the package note using hash of address and items.
* Tweak  - PHP 8.2 compatibility.
* Update - Security update.

2023-08-22 - version 3.8.8
* Fix - "Undefined array key" error when deleting the address.

2023-08-03 - version 3.8.7
* Fix - Remove "Edit" and "Delete" button for default shipping address.

2023-06-12 - version 3.8.6
* Update - Security update

2023-05-23 - version 3.8.5
* Fix - User cannot choose default address when setting up the multiple addresses.

2023-04-20 - version 3.8.4
* Update - Security update.

2023-03-14 - version 3.8.3
* Fix - Remove duplicate multiple address information.

2023-01-16 - version 3.8.2
* Add - Reset button in checkout page to reset the multiple addresses.

2022-12-05 - version 3.8.1
* Fix - Fatal error on WooCommerce 7.1.

2022-11-07 - version 3.8.0
* Add - Declared High-Performance Order Storage (HPOS) incompatibility.

2022-10-05 - version 3.7.2
* Update - Security update

2022-09-07 - version 3.7.1
* Fix - Exclude unnecessary files from plugin zip file.

2022-08-03 - version 3.7.0
* Fix   - `Multiple Shipping` column is blank in "Customer / Order / CSV Export" plugin.
* Tweak - Transition version numbering to WordPress versioning.

2022-06-20 - version 3.6.42
* Fix   - Multiple address display incorrectly in plain text email.
* Fix   - Updating status of single package.

2022-05-25 - version 3.6.41
* Fix   - Unsupported operand types: string - string in wcms-order.php on PHP 8.

2022-05-03 - version 3.6.40
* Fix   - Fatal error on checkout with PHP 8.0 when virtual products added to the cart.

2022-01-05 - version 3.6.39
* Fix   - Missing Languages folder and .pot file in release-ready zip file.

2021-12-01 - version 3.6.38
* Fix   - Missing CSS and JS assets causing broken styles and functionality.

2021-11-17 - version 3.6.37
* Tweak - Refactor address validation.
* Fix   - Fatal error with custom billing fields added from 'woocommerce_checkout_fields' filter hook.

2021-10-21 - version 3.6.36
* Tweak - Use order CRUD classes to save or get meta data.
* Fix   - Editing address in admin does not support custom address fields.

2021-10-06 - version 3.6.35
* Tweak - Remove the empty recipient-form.php file.
* Fix   - Different subtotal, tax, and discount value when one cart item has multiple addresses.
* Fix   - Incorrect shipping method in the order.
* Fix   - Overlap select field in address form.

2021-09-29 - version 3.6.34
* Fix   - Different subtotal value when discount coupon is used.
* Fix   - "Undefined Index: price" notice error when using WC Composite Product plugin.
* Tweak - Update the "WC requires at least" header to 3.2.3.

2021-08-31 - version 3.6.33
* Fix   - Address array is missing in package destination.
* Fix   - "Set Multiple Address" button to always show regardless of cart item quantity.
* Fix   - Shipping methods always display "(incl. tax)" text regardless of tax display settings.

2021-08-19 - version 3.6.32
* Add   - Save billing address fields functionality when click "Set Multiple Address" button.
* Tweak - Move the location of the "Set Multiple Address" button to shipping address area.

2021-08-12 - version 3.6.31
* Tweak - Simplify duplicate cart process.
* Fix   - Taxes added even when customer is tax exempt.

2021-07-27 - version 3.6.30
* Tweak - Refactor the code for add ons compatibility.

2021-06-29 - version 3.6.29
* Fix   - Use subtotal for free shipping calculation.
* Fix   - Add new address button does not work in plain or numeric permalink.
* Fix   - Display multiple addresses in preview order.

2021-06-28 - version 3.6.28
* Fix   - Change error text for no shipping method error.

2021-06-22 - version 3.6.27
* Fix   - Order total does not match for tax-inclusive pricing.
* Fix   - The tax shows up as NaN in recurring cart if multiple shipping is used.
* Fix   - Order total does not include virtual product.

2021-06-10 - version 3.6.26
* Fix   - Display of delete address link on My Account page.

2020-12-10 - version 3.6.25
* Fix   - Replace deprecated WooCommerce 4.4 functions.

2020-11-24 - version 3.6.24
* Fix   - Replace deprecated jquery.bind() and jquery.parseJSON() functions.

2020-09-30 - version 3.6.23
* Tweak - WC 4.5 compatibility.
* Fix   - Localize address form.

2020-08-19 - version 3.6.22
* Tweak - WordPress 5.5 compatibility.

2020-08-06 - version 3.6.21
* Fix - Remove unused code causing undefined variable notice.

2020-07-07 - version 3.6.20
* Fix - Switch to using selectWoo fields and escape output.

2020-06-16 - version 3.6.19
* Fix - Should check if there are valid states for the country before validating the field.

2020-06-10 - version 3.6.18
* Tweak - WC 4.2 compatibility.

2020-04-30 - version 3.6.17
* Fix - Free Shipping coupon with minimum spend not working as expected.
* Tweak - WC 4.1 compatibility.

2020-03-29 - version 3.6.16
* Fix - Postal codes with format 0000 AA are not recognized.
* Fix - When there's specific country restrictions, SMA does not abide to restrictions for state/provice.

2020-03-17 - version 3.6.15
* Tweak - Remove legacy code.

2020-02-26 - version 3.6.14
* Fix - Missing settings.

2020-02-18 - version 3.6.13
* Add - Compatibility for WC Print Invoices/Packing Lists.
* Tweak - Update main plugin filename.

2020-02-04 - version 3.6.12
* Fix - Use proper escape for attributes.
* Fix - Keep shipping methods in sync when moving from Checkout to Cart pages.

2020-01-14 - version 3.6.11
* Tweak - CSV export now includes only the corresponding address per line item.
* Fix   - Fix single package orders showing multiple address notice.
* Fix - Coupon for percentage amount isn't applied to taxes
* Tweak  - WC tested up to 3.9

2019-11-05 - version 3.6.10
* Tweak  - WC tested up to 3.8

2019-08-08 - version 3.6.9
* Fix - Setting shipping address not mobile friendly.
* Tweak  - WC tested up to 3.7

2019-04-23 - version 3.6.8
* Fix    - PHP 7.3 compatibility.
* Fix    - Undefined index on checkout page in certain conditions.
* Fix    - Cannot convert string to array in order page in certain conditions.
* Fix    - Shipping method names not displaying selected shipping method and repeats the same method name.
* Tweak  - Use friendly display name for shipping methods in order receipt page.

2019-04-11 - version 3.6.7
* Fix    - Shipping Address header placement
* Tweak  - WC tested up to 3.6

2019-02-13 - version 3.6.6
* Fix    - Add missing translations file.

2018-09-25 - version 3.6.5
* Update - WC 3.5 compatibility.

2018-09-10 - version 3.6.4
* Fix    - Taxes in subtotal only reflect the taxes for the first product.

2018-05-31 - version 3.6.3
* Fix    - Adding New Address via "My Account > Addresses > Add address" is not working.

2018-05-23 - version 3.6.2
* Fix    - Discounts cause checkout errors.
* Fix    - Usage of deprecated 'Thanks' page.
* Update - Privacy policy notification.
* Update - Export/erasure hooks added.
* Update - WC 3.4 compatibility.

2018-03-14 - version 3.6.1
* Fix   - Free shipping data (min_amount/requires) was not being extracted correctly.
* Fix   - Visual bug in displaying shipping cost where rate instance doesn't apply taxes.
* Fix   - Compatibility with Product Add-Ons.

2017-11-21 - version 3.6.0
* Add   - Compatibility for Order/Customer CSV Export.
* Fix   - Taxes are not based on the correct product quantity.
* Fix   - Tax totals are not calculated properly for WC > 3.2.

2017-11-14 - version 3.5.1
* Fix   - Fatal Error when trying to add addresses via My Account.
* Fix   - Undefined rate index notice.

2017-11-08 - version 3.5.0
* Fix   - Bad index when saving multiple addresses.
* Tweak - Populate Address when editing an existing address.
* Fix   - Internationalization and text domain for strings.
* Fix   - Variable products cause unexpected behavior.
* Fix   - Subtotal incorrect when multiples of item ship to multiple addresses.
* Fix   - Additional compatibility with WooCommerce Subscriptions.

2017-10-02 - version 3.4.2
* Fix - WC 3.2 compatibility.

2017-09-21 - version 3.4.1
* Fix - Checkout of just virtual product is not allowed.

2017-07-31 - version 3.4.0
* Fix - Issue where incorrect meta value of order shipment note and date saved as `true`.
* Fix - Deprecated WC_Order_Item_Meta being used that may throws notice.
* Fix - Compat for PHP 5.4.
* Fix - Allow html formatting for wcms_product_title.
* Fix - Mixed cases sorted wrongly.
* Fix - Addresses not always sorted.
* Fix - International addresses displayed correctly when editing in My Account addresses page.
* Fix - Order line item taxes do not show up for items purchased in multiple quantities.
* Fix - Ensures we don't use a static path when calling country-select.js.
* Fix - Guest checkout duplicates addresses.
* Fix - Calendar button not showing up in the admin date exclusion setting.
* Fix - Variations not showing on the order item address selection.
* New - Add address on My Account addresses page.
* New - Delete multiple shipping address on My Account addresses page.

2017-05-15 - version 3.3.25
 * Fix: Additional updates for the fix for editing a user address from the profile section.
 * Fix: Textdomain for several more labels.
 * Fix: Shipping rates are only showing up for the first Shipping package.
 * Fix: One or more items has no shipping address error.
 * Fix: Compatibility with Product Add-Ons showing HTML code in cart.

2017-05-10 - version 3.3.24
 * Fix: When days are excluded from Valid Shipping Days, they can still be selected and processed as valid dates to check out.
 * Fix: Use correct textdomain in a string in wcms-checkout.
 * Fix: Taxes are not based on the correct shipping location.
 * Fix: Creating new instances of singleton classes such as Checkout and Cart.
 * Fix: Shortcodes were echoing instead of returning the output.
 * Fix: Datepicker functioning incorrectly when there is 1 product in packages.
 * Fix: Blank items show up when adding items after selecting addresses.
 * Fix: Editing a user address from the profile section doesn't work as expected.
 * Fix: Save addresses consistently for users when they create an account.

2017-04-03 - version 3.3.23
 * Fix: Update for WooCommerce 3.0 compatibility.

2017-01-03 - version 3.3.22
 * Tweak: Update deprecated WooCommerce API calls
 * Improvement: Updated rounding method from round_tax() to round() in WC_Tax
 * Bug fix: Fixed issue where WCMS was processing a single-package shipment causing an infinite loop
 * Bug fix: Fix for localStorage detection script to allow guest purchases to store addresses
 * Bug fix: Fixed the delete button for guest customers
 * Bug fix: Added .PO file for better translation support
 * Bug fix: Fixed bug where product could possibly be FALSE if the previously selected products for "Exclude Products" have been deleted
 * Tweak: Added filters for product titles for Mix and Match compatibility
 * Feature: Allow customer to mark an order as a gift even if order is not shipped to multiple addresses
 * Improvement: Store the _gift postmeta to shipments created by multishipping
 * Bugfix: Send a note to ShippingEasy when a shipment is marked as a gift

2016-09-07 - version 3.3.21
 * Bug fix: Fix issue where no shipping options showed when cart contained only one item allowing users to checkout without a method

2016-08-22 - version 3.3.20
 * Bug fix: Fixed issue where wrong address could be loaded when new addresses are added
 * Bug fix: Return an empty package if no shippable products are found in the cart to support Virtual Products

2016-07-15 - version 3.3.19
 * Bug fix: Rely on WC_Countries to check for invalid addresses in is_address_empty()
 * Bug fix: Removed the redundant full_address package key
 * Bug fix: Destination city not getting saved
 * Feature: Added table in the edit-user screen to manege a user's stored addresses
 * Improvement: Updated support for WC Address Validation

2016-07-04 - version 3.3.18
 * Bug fix: Use WooCommerce 2.6 hooks to display addresses in address tab

2016-06-21 - version 3.3.17
 * Bug fix: Resetting the shipping values forces WC to load the shipping methods for the default shipping zone
 * Bug fix: Don't set the shipping_methods session variable when there is only 1 package
 * Improvement: Moved the Other Addresses section to the Address tab in my-account in WC 2.6

2016-05-06 - version 3.3.16
 * Added: WooCommerce 2.6 compatibility (WordPress 4.4+ required)

2016-04-26 - version 3.3.15
 * Bug fix: Saving the shipping methods to a session variable caused FedEx plugin to load late and resets the previously selected shipping method

2016-04-19 - version 3.3.14
 * Feature: Updated Order Delivery Date compatibility
 * Bug fix: Use WC_Shipping::calculate_shipping for all the packages instead of calling WC_Shipping::calculate_shipping_for_package multiple times
 * Bug fix: Switched from using WC_Shipping::load_shipping_methods() to WC_Shipping::shipping_methods to avoid triggering the Table Rate calculation several times

2016-04-12 - version ********
 * Bug fix: Fixed warning that is displayed when clearing the current session data

2016-04-06 - version 3.3.13
 * Bug fix: Do not clear the addresses in WC_MS_Checkout::checkout_process so failed orders do not lose multi-shipping data

2016-03-22 - version 3.3.12
 * Bug fix: Added the new hook that Shipworks is using in exporting orders from WooCommerce
 * Bug fix: Added hooks for Order Delivery Date compatibility
 * Bug fix: Fixed account address form not updating the state field

2016-03-13 - version 3.3.11
 * Improvement: Initially select the lowest rates when getting shipping costs
 * Improvement: Added hooks to add in item meta to package contents
 * Bug fix: Added support for Composite products when calculating package taxes
 * Bug fix: Fixed shipping total not getting added when the selected shipping method/rate does not exist after an update_order_review refresh
 * Bug fix: Prevent WooCommerce from updating the customer shipping address on checkout
 * Bug fix: Store new addresses that are used during checkout

2016-02-16 - version 3.3.10
 * Bug fix: Made non-editable strings translatable
 * Bug fix: Added filter to the default address to add ability to add in additional fields
 * Bug fix: Copy package address to the order if only 1 package [address] exists for the order

2016-02-04 - version 3.3.9
 * Bug fix: Shipping totals were not updating when shipping methods were changed
 * Bug fix: Fixed state field not updated on country change

2015-12-30 - version 3.3.8
 * Bug fix: Fixed warning in admin on orders that did not use multiple addresses
 * Bug fix: Fixed issue where form became too big for the server when managing 100+ addresses

2015-12-10 - version 3.3.7
 * Bug fix: Possible fix for taxes getting injected when changing the billing state or address
 * Bug fix: Fixed warning when creating order shipments from the backend admin
 * Bug fix: Fixed edge case where manipulating the shipping method would get shipping fees out of sync
 * Bug fix: Fixed issue where discount amount would get re-added for every address in the packages
 * Feature: Adding WCMS shipping packages data to the core WooCommerce API order response (currently only read-only)

2015-11-24 - version 3.3.6
 * Bug fix: Display delivery notes in order details and emails when multiple shipping is enabled but not used
 * Bug fix: Enforce character limit on delivery notes field if set for any delivery notes fields
 * Feature: Allow users to import any billing and shipping details they have entered in the checkout form into the multiple address form (requires browser that supports localStorage)

2015-11-16 - version 3.3.5
 * Bug fix: Fixed issue where Multiple Shipping pulls up the wrong address when editing addresses from the My Account page

2015-11-12 - version 3.3.4
 * Bug fix: Fixed issue where character limits for order notes were stuck at 1
 * Bug fix: Added controls to toggle the calendar when defining delivery dates in admin
 * Bug fix: Fixed redirection issue when editing stored addresses as a customer

2015-11-05 - version 3.3.3
 * Feature: Added the ability to limit the number of characters allowed on delivery notes
 * Improvement: Added functionality to inject the delivery notes and dates into the order shipment's post_excerpt field to use with external shipping services integrations
 * Bug fix: Fix for issue where guest addresses were not saving consistently
 * Bug fix: Fixed cases where edit address button was not pointing to the correct edit page
 * Bug fix: Fixed the Delete Address button for guests as it was not working

2015-10-27 - version 3.3.2
 * Feature: Added ability to limit shipping days and dates in the multi-shipping datepicker
 * Bug fix: Remove cart dependency that caused order view to not load in some cases for multi-shipping orders
 * Bug fix: Fixed a number of errors that could appear at checkout and in the order emails

2015-10-13 - version 3.3.1
 * Bug fix: Enhance the date picker for shipping date to support the localized date format
 * Bug fix: Fixed address for to share the WooCommerce setting for default customer address
 * Bug fix: Ensure that ->get_active_provider() returns an object to support all shipping methods
 * Feature: Added localStorage to avoid losing existing notes when editing an address (requires browser that supports localStorage)

2015-08-31 - version 3.3
 * Feature: Added support for WooCommerce PIP including product attributes
 * Feature: Added support WooCommerce Role base shipping methods
 * Feature: Added support for Shipping Easy exports
 * Feature: Added support for Xero exports
 * Feature: Added support for Shipworks exports
 * Feature: Added ability to quickly select stored address on shipping form
 * Feature: Allow users to manage and edit stored addresses directly within the My Account page
 * Improvement: Added option to allow a user to add a date-picker to the shipping address form
 * Improvement: Added address functions to render custom shipping fields
 * Bug fix: Fixed the display of the delivery notes for orders not using multiple packages
 * Bug fix: Adding an address was causing a duplication of the primary address
 * Bug fix: Added a flag with order data to define whether the order used multiple addresses or not
 * Bug fix: Fixed issue where shipping addresses may be forgotten on AJAX checkout reloads
 * Bug fix: Fixed a bug where WooCommerce would overwrite Multi-shipping tax calculations
 * Bug fix: General code cleanup, minor fixes, warnings, and re-organization

2015-07-08 - version 3.2.20
 * Bug fix: Improve installer to better ensure pages are created correctly

2015-07-02 - version 3.2.19
 * Bug fix: Delete shipping session data when cart is ineligible for multiple-shipping

2015-04-21 - version 3.2.18
 * Bug fix: Potential XSS with add_query_arg

2015-03-29 - version 3.2.17
 * Bug fix: Non-taxable items should not be taxed
 * Improvement: Display Gravity Forms data in checkout

2015-02-08 - version 3.2.16
 * Feature: Ability to overwrite templates by creating a "multi-shipping" directory in your active theme's directory

2015-01-29 - version 3.2.15
 * WooCommerce 2.3 compatibility

2015-01-12 - version 3.2.14
 * Bug fix: Additional tax fixes for multiple packages

2014-12-29 - version 3.2.13
 * Improvement: Added new filter that overrides Free Shipping module to implement a per-package minimum order requirement

2014-11-23 - version 3.2.12
 * Improvement: Added gift notes to the admin order email
 * Bug fix: Fixed issue where address selection button was not visible when first product in cart was virtual
 * Bug fix: Enhanced handling of shipping countries to ensure country is populated with correct form data
 * Bug fix: Fixed issue with state selector when country was modified
 * Bug fix: Spelling corrections

2014-11-04 - version 3.2.11
 * Bug fix: Notes will no longer display blank in the order admin if no note is entered
 * Bug fix: Fixed incorrect totals due to taxes being added even if woocommerce_calc_tax is off
 * Bug fix: Fixed undefined index warning for missing gift settings

2014-11-03 - version 3.2.10
 * Bug fix: Fix issue related to session jumbling addresses
 * Bug fix: Minor gift fixes to ensure data is presented in order review screen

2014-10-23 - version 3.2.9
 * Feature: Added ability to add order notes to each individual address
 * Feature: Added ability to mark specific packages as gifts

2014-10-22 - version 3.2.8
 * Bug fix: Fixed issue when shipping physical and virtual goods (or subscriptions) in a single order

2014-10-16 - version 3.2.7
 * Improvement: Change layout slightly to put multi-shipping in control of its own messages
 * Bug fix: Fixed incorrect subtotal tax due to the default customer shipping address

2014-10-14 - version 3.2.6
 * Bug fix: Fixed Fatal Error when resending order emails with multiple addresses
 * Bug fix: Fixed multi-shipping overriding addresses when it is not necessary

2014-10-07 - version 3.2.5
 * Bug fix: Pass the complete destination address to FedEx shipping
 * Bug fix: Cooperation with Product Add-ons
 * Bug fix: Cooperation with Composite Products

2014-09-29 - version 3.2.4
 * Bug fix: Correctly pass variation_id when generating the cart item key

2014-09-25 - version 3.2.3
 * Bug fix: Fixed address validation process to not require the state field
 * Bug fix: Ensure correct cart duplications
 * Bug fix: Fixed cart item key generation that caused issues with product add-ons

2014-09-15 - version 3.2.2
 * Bug fix: Additional tax fixes

2014-09-12 - version 3.2.1
 * Bug fix: Will no longer modify taxes if multiple addresses are not called
 * Bug fix: Minor backwards compatibility fix

2014-09-04 - version 3.2
 * Added: WooCommerce 2.2 compatibility
 * Deprecated the drag and drop options
 * Added: Shipping addresses to order table and details pages
 * Bug fix: Changed .live jQuery to .on to avoid conflicts
 * Bug fix: Do not show multiple address message if multiple addresses are not used
 * Bug fix: Fixed address duplications on new address creation
 * Bug fix: Various CSS fixes to ensure no conflicts and easy theming
 * Bug fix: Tax calculations could be incorrect in certain scenarios

2014-08-15 - version 3.1
 * Bug fix: Various JS and CSS cleanup
 * Improvement: Improved address checking on the checkout page
 * Improvement: Save billing data in real-time and restore stored data after assigning shipping addresses
 * Bug fix: Fixed not getting the correct shipping method names
 * Bug fix: Fixed issue with loading content on tooltips that were generating JS errors
 * Improvement: Added a check to allow plugins to disable ship to multiple location's manipulation of the shipping packages
 * Bug fix: Removed the Shipping Address block on the Thank You page when multiple addresses are used
 * Improvement: Added the display of a table of shipping addresses for the order on the Thank You page
 * Bug fix: Fixed updating of quantities when assigning shipping addresses at the same time on the Checkout > Shipping Addresses page

2014-06-29 - version 3.0.2
 * Bug fix: Text domain fixes for correct translations

2014-06-04 - version 3.0.1
 * Bug fix: Fixed ability for non-logged users to add addresses
 * Bug fix: Added a few missing settings in admin

2014-05-29 - version 3.0
 * Feature: New tabled add addresses view. Modeled after a large online retailer’s multiple shipping addresses checkout
 * Bug fix: Better support of address validation
 * Bug fix: Various bug fixes and general code cleanup and improvements
 * Bug fix: Removed unnecessary user warnings
 * Enhancement: Improved cart duplication interaction and interface

2014-05-13 - version 2.4.4
 * Bug fix: JS errors

2014-05-01 - version 2.4.3
 * Bug fix: Fix address selection overlay appearing inactive

2014-04-29 - version 2.4.2
 * Cleanup: Code organization
 * Bug fix: Address validation fixes (not support for address validation plugin)
 * Cleanup: Various code fixes

2014-03-19 - version 2.4.1
 * Bug fix: Ensure that default address is shown for logged in users
 * Bug fix: Fixed checkout validation for items with no shipping address
 * Bug fix: Allow resizing of address entry view in mobile browsers

2014-03-07 - version 2.4
 * Enhancement: Mobile drag, drop, select quantity for mobile browser support

2014-02-02 - version 2.3.2
 * Bug fix: Fixed support for selecting Canadian Provinces

2014-01-06 - version 2.3.1
 * Bug fix: Rebuilt tax calculations

2013-12-18 - version 2.3
 * Added: WooCommerce 2.1 compatibility (WordPress 3.8 required)
 * Feature: Ability to mark a portion of order as complete, and notify customer via email
 * Bug fix: Make sure that multiple shipping meta is being stored and displayed
 * Bug fix: Error related to an unexpected amount of products being ordered
 * Cleanup: Some CSS cleanup
 * Cleanup: Remove shipping as billing text when multi-shipping is selected

2013-12-10 - version 2.2.3
 * Bug fix: Remove an extraneous code that was printing extraneous text to the page

2013-11-27 - version 2.2.2
 * Bug fix: Cleanup support for adding product bundles to multiple locations

2013-11-21 - version 2.2.1
 * Bug fix: Adjusted drag pointer tolerances

2013-11-19 - version 2.2
 * Cleanup: Drag drop and CSS
 * Feature: Ability to turn off shipping to multiple addresses when certain products/categories are in cart

2013-11-13 - version 2.1.16
 * Cleanup: Better support of shipping calculations

2013-11-11 - version 2.1.15
 * Fix: Additional cleanup on drag/drop

2013-11-10 - version 2.1.14
 * Fix: Clean up cursor positioning on drag/drop

2013-11-01 - version 2.1.13
 * Cleanup: CSS housekeeping
 * Bug fix: Clear sessions post purchase

2013-10-29 - version 2.1.12
 * Enhancement: Added body class to the overlay

2013-09-19 - version 2.1.11
 * Bug fix: Odd fatal error corrected
 * Bug fix: Don't load CSS unless necessary in the admin

2013-09-17 - version 2.1.10
 * Enhancement: Cleaned up some translatable strings

2013-09-10 - version 2.1.9
 * Bug fix: Better total calculations when using USPS and Local shipping with discounts

2013-08-12 - version 2.1.8
 * Bug fix: Enhancement to touch device support
 * Enhancement: Added filter 'wcms_order_shipping_packages_table' to output multiple shipping address

2013-07-23 - version 2.1.7
 * Bug fix: Changing between local pickup and delivery had caused multi-shipping form to display

2013-07-03 - version 2.1.6
 * Bug fix: Ability to edit an address that has been added to ship to
 * Bug fix: Do not show select address form on virtual products only in cart
 * Enhancement: Support for adding multiple addresses on touch devices
 * Enhancement: Better support for multiple gift certificates only in cart
 * Enhancement: Better support for Local Pickup Plus plugin

2013-06-19 - version 2.1.5
 * Bug fix: Do not show select address form if local pickup is only available method

2013-06-15 - version 2.1.4
 * Cleanup: Better support of guest checkout when using multiple addresses
 * Bug fix: Allow selecting of address you've saved to address book in cases where it wasn't being allowed
 * Bug fix: Support Order Notes which weren't being supported in all cases

2013-06-09 - version 2.1.3
 * Cleanup: Address form overlay cleanup.

2013-05-21 - version 2.1.2
 * Bug fix: The 'woocommerce_shipping_fields' filter not working in some installs. Updated instances of that hook/filter to WC_Countries::get_address_fields, which also calls apply_filters('woocommerce_shipping_fields').

2013-05-16 - version 2.1.1
 * Bug fix: No longer allow 0.5 of a product to be shipped to a location
 * Cleanup: Various code improvements to the add to address drag/drop

2013-05-09 - version 2.1.0
 * Added support for the WooCommerce Checkout Field Editor by WooThemes only

2013-05-03 - version 2.0.6
 * Enhancement: If quantity of item in cart > 1, dragging/dropping will prompt customer for quantity to add to the respective address

2013-04-30 - version 2.0.5
 * Bug fix: Fixed issue where if only one product was selected, the shipping methods wouldn't load

2013-04-11 - version 2.0.4
 * Bug fix: At times draggable wouldn't initialize
 * Cleanup: Made draggable areas more precise for customers when selecting products from cart

2013-03-29 - version 2.0.3
 * Bug fix: Fixed inaccurate loading of flat-rate shipping fees where it double loaded in some cases

2013-03-14 - version 2.0.2
 * Bug fixes: Fixed issue where multi-shipping options never loaded addresses

2013-01-30 - version 2.0.1
 * Code cleanup
 * 2.0 Fixes
 * Fix missing addresses on address-form

2013-01-30 - version 2.0.0
 * Added new drag and drop interface to adding products to addresses
 * Added ability to ship entire cart to multiple locations
 * Bug fixes
 * Ability to edit text shown on checkout page
 * Support for WooCommerce 2.0
 * Support for taxes by billing or shipping address

2012-12-04 - version 1.1.1
 * New updater

2012-10-04 - version 1.1.0
 * Bug fixes
 * Changed free-form entries to static selections to avoid customer typos

2012-09-21 - version 1.0.1
 * Minor CSS changes
 * Added address edit button to address table on checkout

2012-08-07 - version 1.0
 * First release
</file>

</files>
