<?php
/**
 * Account address form template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<form action="" method="post" id="address_form">

	<?php if ( $updating ) : ?>

		<div id="addresses">

			<div class="shipping_address address_block" id="shipping_address_<?php echo esc_attr( $idx ); ?>">
				<?php
				foreach ( $ship_fields as $key => $field ) {
					$val = '';

					if ( isset( $address[ $key ] ) ) {
						$val = $address[ $key ];
					}

					woocommerce_form_field( $key, $field, $val );
				}

				/**
				 * Action to add element on wcms address form.
				 *
				 * @param WC_Checkout $checkout checkout object.
				 *
				 * @since 3.3
				 */
				do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
				?>
			</div>

		</div>

	<?php else : ?>

		<div id="addresses" class="address-column">

		<?php
		foreach ( $ship_fields as $key => $field ) :
			$val = '';

			woocommerce_form_field( $key, $field, $val );
		endforeach;
		?>
		</div>
	<?php endif; ?>

	<div class="form-row">
		<?php wp_nonce_field( 'shipping_account_address_action' ); ?>
		<input type="submit" name="set_addresses" value="<?php esc_attr_e( 'Save Address', 'woocommerce-shipping-multiple-addresses' ); ?>" class="button alt" />
	</div>
</form>
<script type="text/javascript">
	jQuery( document ).ready( function( $ ) {
		$( '#address_form' ).submit( function() {
			var valid = true;

			$( '.input-text, select, input:checkbox' ).each( function( e ) {
				var $this             = $( this ),
					$parent           = $this.closest( '.form-row' ),
					validate_required = $parent.is( '.validate-required' );

				if ( validate_required ) {
					if ( 'checkbox' === $this.attr( 'type' ) && ! $this.is( ':checked' ) ) {
						valid = false;
					} else if ( $this.val() === '' ) {
						valid = false;
					}
				}

				if ( ! valid ) {
					$parent.removeClass( 'woocommerce-validated' ).addClass( 'woocommerce-invalid woocommerce-invalid-required-field' );
					$this.focus();
					return false;
				}
			});

			return valid;
		});
	});
</script>
