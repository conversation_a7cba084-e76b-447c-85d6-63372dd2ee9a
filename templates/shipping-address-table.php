<?php
/**
 * Shipping address table template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

?>
<form method="post" action="" id="address_form">
	<div class="wcms-error-notice">
		<?php
		wc_get_template(
			'notices/notice.php',
			array(
				'notices' => array(
					array(
						'notice' => esc_html__( 'Shipping addresses cannot be saved. Please make sure the item quantity usage in the addresses match with cart quantity.', 'woocommerce-shipping-multiple-addresses' ),
					),
				),
			)
		);
		?>
	</div>
	<div class="wcms-error-address-empty hidden">
		<?php
		wc_get_template(
			'notices/error.php',
			array(
				'notices' => array(
					array(
						'notice' => esc_html__( 'There is no shipping addresses. Please add new shipping address.', 'woocommerce-shipping-multiple-addresses' ),
					),
				),
			)
		);
		?>
	</div>
	<?php
	// Set the address fields.
	foreach ( $addresses as $x => $addr ) {
		if ( empty( $addr ) ) {
			continue;
		}

		$address_fields = WC()->countries->get_address_fields( $addr['shipping_country'], 'shipping_' );

		$address           = array();
		$formatted_address = false;

		foreach ( $address_fields as $field_name => $field ) {
			$addr_key             = str_replace( 'shipping_', '', $field_name );
			$address[ $addr_key ] = ( isset( $addr[ $field_name ] ) ) ? $addr[ $field_name ] : '';
		}

		if ( ! empty( $address ) ) {
			$formatted_address = wcms_get_formatted_address( $address );
			$json_address      = wp_json_encode( $address );
			$json_address      = wc_esc_json( $json_address );
		}

		if ( ! $formatted_address ) {
			continue;
		}
		?>
		<div style="display: none;">
			<?php
			/**
			 * Action to add element on wcms address form.
			 *
			 * @param WC_Checkout $checkout checkout object.
			 *
			 * @since 3.3
			 */
			do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
			?>
		<input type="hidden" name="addresses[]" value="<?php echo esc_attr( $x ); ?>" />
		<textarea style="display:none;"><?php echo $json_address; // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- already escaped above. ?></textarea>
		</div>
		<?php
	}
	$ms_settings = get_option( 'woocommerce_multiple_shipping_settings', array() );

	$add_url = add_query_arg( 'address-form', '1' );
	?>

	<div>
		<a class="h2-link" href="<?php echo esc_url( $add_url ); ?>"><?php esc_html_e( 'Add a new shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></a>

		<?php
		if ( isset( $ms_settings['cart_duplication'] ) && 'no' !== $ms_settings['cart_duplication'] ) :
			$dupe_url = add_query_arg(
				array(
					'duplicate-form' => '1',
					'_wcmsnonce'     => wp_create_nonce( 'wcms-duplicate-cart' ),
				),
				get_permalink( wc_get_page_id( 'multiple_addresses' ) )
			);
			?>
			<div style="float: right;">
				<a class="h2-link" href="<?php echo esc_url( $dupe_url ); ?>"><?php esc_html_e( 'Duplicate Cart', 'woocommerce-shipping-multiple-addresses' ); ?></a>
				<img class="help_tip" title="<?php esc_html_e( 'Duplicating your cart will allow you to ship the exact same cart contents to multiple locations. This will also increase the price of your purchase.', 'woocommerce-shipping-multiple-addresses' ); ?>" src="<?php echo WC()->plugin_url(); ?>/assets/images/help.png" height="16" width="16">
			</div>
			<?php
		endif;
		?>
	</div>

	<table class="wc-shipping-multiple-addresses shop_table cart" cellspacing="0">
		<thead>
			<tr>
				<th class="product-name" width="20%"><?php esc_html_e( 'Product', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th class="product-quantity" width="12%"><?php esc_html_e( 'Quantity', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<?php
				/**
				 * Action to add a column within the shipping address table head.
				 *
				 * @since 3.3
				 */
				do_action( 'wc_ms_address_table_head' );
				?>
				<th class="shipping-address" width="25%"><?php esc_html_e( 'Shipping Address', 'woocommerce-shipping-multiple-addresses' ); ?></th>
				<th class="remove-item" width="33%">&nbsp;</th>
			</tr>
		</thead>
		<tfoot>
			<tr id="row-add-input">
				<td class="add-product">
					<select name="item_product" class="product-select" id="row_add_item_product">
						<option value=""><?php esc_html_e( 'Select Item', 'woocommerce-shipping-multiple-addresses' ); ?></option>
					<?php

					foreach ( $contents as $key => $content ) {
						echo '<option value="' . esc_attr( $key ) . '">' . esc_html( $content['data']->get_name() ) . '</option>';
					}
					?>
					</select>
				</td>
				<td class="add-quantity">
					<div class="quantity">
						<input type="number" name="item_qty" id="row_add_item_qty" min="0" class="input-text qty text input-quantity" value="0" />
					</div>
				</td>
				<?php
				/**
				 * Action to add a column within the shipping address table specifically on add row form.
				 *
				 * @since 4.1.0
				 */
				do_action( 'wc_ms_multiple_address_table_add_row' );
				?>
				<td class="add-address">
					<select name="item_address" class="address-select" id="row_add_item_address">
						<option value=""><?php esc_html_e( 'Select Address', 'woocommerce-shipping-multiple-addresses' ); ?></option>
					<?php

					foreach ( $addresses as $addr_key => $address ) {
						$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
						$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
						$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

						echo '<option value="' . $addr_key . '">' . $formatted . '</option>';
					}
					?>
					</select>
				</td>
				<td class="add-button"><button id="wcms-add-row" data-error-text="<?php esc_attr_e( 'Please input the empty fields!', 'woocommerce-shipping-multiple-addresses' ); ?>" class="button alt"><?php esc_html_e( 'Add Item(s)', 'woocommerce-shipping-multiple-addresses' ); ?></button></td>
			</tr>
			<tr id="row-availability-info">
				<?php
				/**
				 * Filter to manipulate the colspan value.
				 *
				 * @param int Colspan value.
				 *
				 * @since 4.1.0
				 */
				?>
				<td colspan="<?php echo esc_attr( apply_filters( 'wc_ms_default_availability_colspan', 4 ) ); ?>" id="availability-info">
					<div class="availability-title"><?php esc_html_e( 'Item quantity usage:', 'woocommerce-shipping-multiple-addresses' ); ?></div>
					<?php
					foreach ( $contents as $key => $value ) {
						$_product = $value['data'];
						$pid      = $value['product_id'];
						?>
						<div class="availability-product <?php echo esc_attr( $key ); ?>" data-cart-key="<?php echo esc_attr( $key ); ?>">
							<span class="product-name"><?php echo esc_html( $_product->get_name() ); ?></span>
							<div class="usage-percentage">
								<div class="percentage-bar">
									<span class="bar-text"><?php esc_html_e( 'Usage:', 'woocommerce-shipping-multiple-addresses' ); ?></span>
									<span class="bar-percent"></span>
								</div>
							</div>
						</div>
						<?php
					}
					?>
				</td>
			</tr>
		</tfoot>
		<tbody id="content-addresses">
			<tr class="row-template">
				<td class="product-name">
					<div class="product-text"></div>
					<input type="hidden" class="input-product" name="" value="" />
				</td>
				<td class="product-quantity">
					<div class="quantity">
						<input type="number" class="input-quantity input-text qty text" name="" value="" />
					</div>
				</td>
				<?php
				/**
				 * Action to add a column within the shipping address table specifically on add row template.
				 *
				 * @since 4.1.0
				 */
				do_action( 'wc_ms_multiple_address_table_template_add_row' );
				?>
				<td class="shipping-address">
					<select name="" class="address-select input-address">
					<?php

					foreach ( $addresses as $addr_key => $address ) {
						$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
						$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
						$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

						echo '<option value="' . $addr_key . '">' . $formatted . '</option>';
					}
					?>
					</select>
				</td>
				<td class="row-action">
					<button class="delete-item"><?php esc_html_e( 'Delete item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
					<button class="split-item"><?php esc_html_e( 'Split item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
				</td>
			</tr>
		<?php
		$row_index = 0;

		foreach ( $contents as $key => $value ) :
			$_product = $value['data'];
			$pid      = $value['product_id'];

			if ( ! $_product->needs_shipping() ) {
				continue;
			}

			if ( empty( $relations ) ) {
				continue;
			}

			foreach ( $relations as $adrs_idx => $cart_keys ) {
				$relation_qty = 0;

				foreach ( $cart_keys as $cart_key ) {
					if ( $cart_key === $key ) {
						$relation_qty++;
					}
				}

				if ( 0 === $relation_qty ) {
					continue;
				}
				?>
				<tr class="row-added" data-row-index="<?php echo esc_attr( $row_index ); ?>" data-cart-key="<?php echo esc_attr( $key ); ?>">
					<td class="product-name">
						<div class="product-text">
							<?php
							/**
							 * Filter to amnipulate the product title.
							 *
							 * @param string Product title.
							 * @param array  $value Cart item value.
							 *
							 * @since 3.3
							 */
							echo esc_html( apply_filters( 'wcms_product_title', $_product->get_name(), $value ) );
							?>
						</div>
						<input type="hidden" class="input-product" data-row-index="<?php echo esc_attr( $row_index ); ?>" name="items[<?php echo esc_attr( $key ); ?>][product][]" value="<?php echo esc_attr( $_product->get_id() ); ?>" />
					</td>
					<td class="product-quantity">
						<?php
						$product_quantity = woocommerce_quantity_input(
							array(
								'classes'     => array( 'input-quantity', 'input-text', 'qty', 'text' ),
								'input_name'  => "items[{$key}][qty][]",
								'input_value' => $relation_qty,
								'max_value'   => $_product->backorders_allowed() ? '' : $_product->get_stock_quantity(),
							),
							$_product,
							false
						);
						echo $product_quantity;
						?>
					</td>
					<?php
					/**
					 * Action to add a column within the shipping address table.
					 *
					 * @param string $key Cart item keys.
					 * @param array  $value Cart item value.
					 * @param string $address_key Address index or key.
					 *
					 * @since 3.3
					 */
					do_action( 'wc_ms_multiple_address_table_row', $key, $value, $adrs_idx );
					?>
					<td class="shipping-address">
						<select name="items[<?php echo esc_attr( $key ); ?>][address][]" class="address-select input-address" data-row-index="<?php echo esc_attr( $row_index ); ?>">
						<?php

						foreach ( $addresses as $addr_key => $address ) {
							$formatted  = $address['shipping_first_name'] . ' ' . $address['shipping_last_name'] . ',';
							$formatted .= ' ' . $address['shipping_address_1'] . ' ' . $address['shipping_address_2'] . ',';
							$formatted .= ' ' . $address['shipping_city'] . ', ' . $address['shipping_state'];

							echo '<option value="' . $addr_key . '" ' . selected( $adrs_idx, $addr_key, false ) . '>' . $formatted . '</option>';
							$selected = '';
						}
						?>
						</select>
					</td>
					<td class="row-action">
						<button class="delete-item" data-row-index="<?php echo esc_attr( $row_index ); ?>"><?php esc_html_e( 'Delete item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
						<button class="split-item" data-row-index="<?php echo esc_attr( $row_index ); ?>"><?php esc_html_e( 'Split item', 'woocommerce-shipping-multiple-addresses' ); ?></button>
					</td>
				</tr>
				<?php
				$row_index++;
			}
		endforeach;
		?>
		</tbody>
	</table>

	<div class="form-row">
		<?php wp_nonce_field( 'shipping_address_action' ); ?>
		<input type="hidden" name="delete[index]" id="delete_index" value="" />
		<input type="hidden" name="delete[key]" id="delete_key" value="" />
		<input type="hidden" name="shipping_type" value="item" />
		<input type="hidden" name="shipping_address_action" value="save" />

		<div class="update-shipping-addresses">
			<input type="submit" name="update_quantities" class="button" value="<?php esc_attr_e( 'Update', 'woocommerce-shipping-multiple-addresses' ); ?>" />
		</div>

		<div class="set-shipping-addresses">
			<input class="button alt" type="submit" name="set_addresses" value="<?php esc_attr_e( 'Save Addresses and Continue', 'woocommerce-shipping-multiple-addresses' ); ?>" />
		</div>

	</div>

	<div class="clear"></div>

	<small class="address-form-note">
		<?php esc_html_e( 'Please note: To send a single item to more than one person, you must change the quantity of that item to match the number of people you\'re sending it to, then click the Update button.', 'woocommerce-shipping-multiple-addresses' ); ?>
	</small>

</form>
