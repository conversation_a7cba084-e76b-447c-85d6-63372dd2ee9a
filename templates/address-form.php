<?php
/**
 * Address form template file.
 *
 * @package woocommerce-shipping-multiple-addresses
 */

if ( empty( $addresses ) ) {
	echo '<p>' . esc_html__( 'No address on file. Please add one below.', 'woocommerce-shipping-multiple-addresses' ) . '</p>';
} else {
	echo '<div class="address-container">';
	foreach ( $addresses as $idx => $address ) {

		wc_get_template(
			'address-block.php',
			array(
				'idx'         => $idx,
				'address'     => $address,
				'checkout'    => $checkout,
				'ship_fields' => $ship_fields,
			),
			'multi-shipping',
			dirname( WC_Ship_Multiple::FILE ) . '/templates/'
		);

	}
		echo '<div class="clear"></div>';
	echo '</div>';

}

?>

<hr />

<?php
$address_id = isset( $_GET['edit'] ) ? intval( $_GET['edit'] ) : -1; // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation
$address    = array();

if ( -1 !== $address_id ) :
	$address = $addresses[ $address_id ];
	?>
	<h2><?php esc_html_e( 'Edit address', 'woocommerce-shipping-multiple-addresses' ); ?></h2>
<?php else : ?>
	<h2><?php esc_html_e( 'Add a new address', 'woocommerce-shipping-multiple-addresses' ); ?></h2>
	<?php if ( ! isset( $_GET['ref'] ) ) : // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation ?>
	<p>
		<a href="#" class="button btn-import-billing" style="display: none;"><?php esc_html_e( 'Import billing address', 'woocommerce-shipping-multiple-addresses' ); ?></a>
		<a href="#" class="button btn-import-shipping" style="display: none;"><?php esc_html_e( 'Import shipping address', 'woocommerce-shipping-multiple-addresses' ); ?></a>
	</p>
	<?php endif; ?>
<?php endif; ?>

<?php wc_print_notices(); ?>

<form action="" method="post" class="wcms-address-form">
	<div class="shipping_address address_block" id="shipping_address">
		<?php
		/**
		 * Action to add element on wcms before address form.
		 *
		 * @param WC_Checkout $checkout checkout object.
		 *
		 * @since 3.3
		 */
		do_action( 'woocommerce_before_checkout_shipping_form', $checkout );
		?>

		<div class="address-column">
			<?php
			$location = wc_get_customer_default_location();

			if ( empty( $address ) && ! empty( $location ) ) {
				foreach ( $location as $key => $value ) {
					$address[ 'shipping_' . $key ] = $value;
				}
			}

			foreach ( $ship_fields as $key => $field ) :
				$val             = ( isset( $address[ $key ] ) ) ? $address[ $key ] : '';
				$ship_field_id   = rtrim( str_replace( '[', '_', $key ), ']' );
				$field['return'] = true;

				$val = ( empty( $val ) && ! empty( $_GET[ $key ] ) ) ? sanitize_text_field( wp_unslash( $_GET[ $key ] ) ) : $val; // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation.

				// Making sure to get the state dropdown by checking the available shipping countries.
				// If the available country is only one, the state field will use `country` args to display the state.
				if ( 'shipping_state' === $key ) {
					$countries = WC()->countries->get_shipping_countries();

					if ( 1 === count( $countries ) ) {
						$country_code     = array_keys( $countries );
						$field['country'] = array_shift( $country_code );
					}
				}

                // phpcs:disable WordPress.Security.EscapeOutput.OutputNotEscaped --- skipping escaping since fragments are being escaped
				echo str_replace( 'name="' . esc_attr( $key ) . '"', 'name="address[' . esc_attr( $ship_field_id ) . ']"', woocommerce_form_field( $key, $field, $val ) );
			endforeach;

			/**
			 * Action to add element on wcms address form.
			 *
			 * @param WC_Checkout $checkout checkout object.
			 *
			 * @since 3.3
			 */
			do_action( 'woocommerce_after_checkout_shipping_form', $checkout );
			?>
			<?php wp_nonce_field( 'save_to_address_book' ); ?>
			<input type="hidden" name="action" value="save_to_address_book" />
			<input type="hidden" name="id" id="address_id" value="<?php echo esc_attr( $address_id ); ?>" />

			<input type="hidden" name="return" value="list" />

			<?php if ( ! empty( $_GET['ref'] ) ) : // phpcs:ignore WordPress.Security.NonceVerification.Recommended --- No DB operation. ?>
			<input type="hidden" name="next" value="<?php echo esc_url( get_permalink( wc_get_page_id( 'myaccount' ) ) ); ?>" />
			<?php endif; ?>
		</div>

	</div>

	<?php if ( $address_id > -1 ) : ?>
		<input type="submit" class="button alt" id="use_address" value="<?php esc_html_e( 'Update Address', 'woocommerce-shipping-multiple-addresses' ); ?>" />
	<?php else : ?>
		<input type="submit" class="button alt" id="use_address" value="<?php esc_html_e( 'Save Address', 'woocommerce-shipping-multiple-addresses' ); ?>" />
	<?php endif; ?>

</form>
<script type="text/javascript">
	var billing_address = null,
		shipping_address = null;

	jQuery(document).ready(function($) {
		if ( supports_html5_storage() ) {
			billing_address = localStorage.getItem( 'wcms_billing_fields' );
			shipping_address = localStorage.getItem( 'wcms_shipping_fields' );

			if ( billing_address ) {
				billing_address = JSON.parse( billing_address );
				$(".btn-import-billing").show();
			}

			if ( shipping_address ) {
				shipping_address = JSON.parse( shipping_address );
				$(".btn-import-shipping").show();
			}

			$(".btn-import-billing").click(function(e) {
				e.preventDefault();
				$( '#ms_addresses' ).val(''); // Reset dropdown on add screen

				for ( field in billing_address ) {
					var shipping_field = field.replace('billing_', 'shipping_');
					$("#"+ shipping_field)
						.val( billing_address[field] )
						.change();
				}
			});

			$(".btn-import-shipping").click(function(e) {
				e.preventDefault();
				$( '#ms_addresses' ).val(''); // Reset dropdown on add screen

				for ( field in shipping_address ) {
					$("#"+ field)
						.val( shipping_address[field] )
						.change();
				}
			});
		}
	});
</script>
