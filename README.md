[![CI](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-multiple-addresses/actions/workflows/cron_qit.yml)

woocommerce-shipping-multiple-addresses
====================

Ship to multiple addresses from WooCommerce.

| Product Page | Documentation | Ideas board |
| ------------ | ------------- | ----------- |
| https://woocommerce.com/products/shipping-multiple-addresses/ | https://docs.woocommerce.com/document/multiple-ship-to-addresses/ | https://ideas.woocommerce.com/forums/133476-woocommerce/category/75738-category-shipping-methods |

## NPM Scripts

WooCommerce Shipping Multiple Addresses utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These include building JavaScript, SASS, CSS minification, and language files.

`pnpm run build:dev` - Runs the tasks necessary when developing.

`pnpm run watchsass` - Will continuously monitor changes in the `scss` files and will minify them automatically.
